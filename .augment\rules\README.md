# DCS World 启动器项目规则文档

本目录包含了 DCS World 启动器项目的完整开发规范和指南，旨在确保代码质量、团队协作效率和项目的可维护性。

## 📋 规则文档概览

### 1. [项目规范](./project-rules.md)
**核心内容**：
- 项目概述和技术栈
- 目录结构和文件命名规范
- 功能模块规范
- 性能优化和安全规范
- Git 规范和代码审查标准

**适用人员**：所有项目成员
**更新频率**：项目架构变更时

### 2. [技术规范](./technical-standards.md)
**核心内容**：
- 代码质量标准（ESLint、Prettier 配置）
- React 开发标准（Hooks、组件设计模式）
- Styled-Components 规范
- 状态管理规范（Context 设计）
- API 服务规范和错误处理
- 性能优化标准
- 测试标准和安全标准

**适用人员**：开发人员
**更新频率**：技术栈升级时

### 3. [代码风格指南](./code-style-guide.md)
**核心内容**：
- JavaScript/React 代码风格
- 变量和函数命名规范
- 组件结构和 Props 规范
- Styled-Components 风格
- 注释风格和错误处理风格
- 导入导出规范

**适用人员**：开发人员
**更新频率**：团队代码风格调整时

### 4. [开发工作流程](./development-workflow.md)
**核心内容**：
- 开发环境设置
- 功能开发流程
- 测试流程（单元测试、集成测试）
- 代码质量保证
- 发布流程和版本管理
- 问题处理流程
- 团队协作规范

**适用人员**：所有项目成员
**更新频率**：工作流程优化时

## 🚀 快速开始

### 新成员入门
1. **阅读顺序**：
   - 首先阅读 [项目规范](./project-rules.md) 了解项目整体架构
   - 然后阅读 [开发工作流程](./development-workflow.md) 了解开发流程
   - 接着阅读 [代码风格指南](./code-style-guide.md) 了解编码规范
   - 最后阅读 [技术规范](./technical-standards.md) 了解技术细节

2. **环境设置**：
   ```bash
   # 克隆项目
   git clone <repository-url>
   cd dcs-world-launcher
   
   # 安装依赖
   npm install
   
   # 启动开发服务器
   npm start
   ```

3. **开发工具配置**：
   - 安装推荐的 VS Code 扩展
   - 配置 ESLint 和 Prettier
   - 设置 Git hooks

### 常用命令
```bash
# 开发
npm start                 # 启动开发服务器
npm run electron         # 启动 Electron 应用

# 代码质量
npm run lint             # 代码检查
npm run lint:fix         # 自动修复代码问题
npm run format           # 代码格式化

# 测试
npm test                 # 运行单元测试
npm test -- --coverage   # 运行测试并生成覆盖率报告
npm run test:e2e         # 运行端到端测试

# 构建
npm run build            # 生产构建
npm run build:electron   # 构建 Electron 应用
npm run dist             # 生成安装包
```

## 📊 项目技术栈

### 前端技术
- **框架**: React 18
- **样式**: styled-components
- **动画**: framer-motion
- **路由**: react-router-dom
- **状态管理**: React Context + Hooks
- **HTTP 客户端**: axios
- **图标**: react-icons

### 开发工具
- **构建工具**: Create React App
- **代码检查**: ESLint
- **代码格式化**: Prettier
- **测试框架**: Jest + React Testing Library
- **端到端测试**: Cypress（可选）
- **版本控制**: Git

### 桌面应用
- **框架**: Electron
- **打包工具**: electron-builder

## 🔧 开发规范要点

### 代码提交规范
```
<type>(<scope>): <subject>

# 示例
feat(auth): add user login functionality
fix(ui): resolve sidebar navigation issue
docs(readme): update installation instructions
```

### 分支管理
```
main (生产分支)
├── develop (开发分支)
│   ├── feature/user-auth (功能分支)
│   ├── feature/game-launcher (功能分支)
│   └── feature/mod-manager (功能分支)
├── release/v1.0.0 (发布分支)
└── hotfix/critical-bug (热修复分支)
```

### 组件命名规范
- **组件文件**: PascalCase (如 `HomePage.js`)
- **服务文件**: camelCase + Service 后缀 (如 `authService.js`)
- **Context 文件**: PascalCase + Context 后缀 (如 `AuthContext.js`)
- **样式组件**: 描述性 PascalCase (如 `HeaderContainer`)

### 测试覆盖率要求
- **单元测试覆盖率**: ≥ 80%
- **关键功能覆盖率**: ≥ 95%
- **组件测试**: 所有页面组件必须有测试
- **服务测试**: 所有 API 服务必须有测试

## 📈 质量保证

### 代码审查清单
- [ ] 功能是否按需求实现
- [ ] 代码是否遵循项目规范
- [ ] 是否有适当的测试覆盖
- [ ] 性能是否满足要求
- [ ] 安全性是否考虑充分
- [ ] 错误处理是否完善
- [ ] 代码是否有适当的注释

### 发布前检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 版本号更新
- [ ] 更新日志编写
- [ ] 文档更新
- [ ] 性能测试通过
- [ ] 安全扫描通过

## 🤝 团队协作

### 沟通渠道
- **日常沟通**: 团队聊天工具
- **技术讨论**: GitHub Issues/Discussions
- **代码审查**: Pull Request 评论
- **文档协作**: 项目 Wiki

### 会议安排
- **每日站会**: 15分钟，同步进度和问题
- **周计划会**: 1小时，制定周计划
- **代码审查会**: 根据需要安排
- **技术分享会**: 每月一次

### 知识管理
- **技术文档**: 维护在项目 Wiki
- **最佳实践**: 记录在规范文档中
- **问题解决方案**: 记录在 FAQ 中
- **培训材料**: 为新成员准备

## 📚 相关资源

### 官方文档
- [React 官方文档](https://reactjs.org/docs/getting-started.html)
- [styled-components 文档](https://styled-components.com/docs)
- [framer-motion 文档](https://www.framer.com/motion/)
- [Electron 文档](https://www.electronjs.org/docs)

### 最佳实践
- [React 最佳实践](https://reactjs.org/docs/thinking-in-react.html)
- [JavaScript 最佳实践](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide)
- [Git 工作流程](https://www.atlassian.com/git/tutorials/comparing-workflows)

### 工具和插件
- [VS Code React 扩展](https://marketplace.visualstudio.com/items?itemName=dsznajder.es7-react-js-snippets)
- [Chrome React DevTools](https://chrome.google.com/webstore/detail/react-developer-tools/fmkadmapgofadopljbjfkapdkoienihi)
- [Redux DevTools](https://chrome.google.com/webstore/detail/redux-devtools/lmhkpmbekcpmknklioeibfkpmmfibljd)

## 🔄 规则更新

### 更新流程
1. **提出修改建议**: 通过 Issue 或团队讨论
2. **团队讨论**: 评估修改的必要性和影响
3. **起草修改**: 创建 PR 修改相关文档
4. **审查批准**: 团队 Leader 审查并批准
5. **通知团队**: 通知所有成员规则变更

### 版本控制
- 规则文档使用语义化版本控制
- 重大变更需要更新主版本号
- 小幅修改更新次版本号
- 错误修正更新补丁版本号

### 变更记录
所有规则变更都会记录在 `CHANGELOG.md` 文件中，包括：
- 变更日期
- 变更内容
- 影响范围
- 迁移指南（如需要）

---

## 📞 联系方式

如果对规则文档有任何疑问或建议，请通过以下方式联系：

- **项目 Issues**: 在 GitHub 项目中创建 Issue
- **团队讨论**: 在团队聊天群中讨论
- **邮件联系**: 发送邮件给项目负责人

---

*本规则文档是活文档，会根据项目发展和团队反馈持续更新和完善。*

**最后更新**: 2024年12月31日
**文档版本**: v1.0.0
**维护者**: DCS World 启动器开发团队