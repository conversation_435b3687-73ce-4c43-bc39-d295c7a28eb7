# DCS World 启动器代码风格指南

## 总体原则

1. **一致性**: 整个项目保持统一的代码风格
2. **可读性**: 代码应该易于理解和维护
3. **简洁性**: 避免不必要的复杂性
4. **可扩展性**: 代码结构应支持未来的功能扩展

## JavaScript/React 代码风格

### 变量命名

#### 变量和函数
```javascript
// ✅ 使用 camelCase
const userName = 'john_doe';
const isLoggedIn = true;
const gameResourceList = [];

// 函数名应该是动词或动词短语
const fetchUserData = async () => {};
const handleButtonClick = () => {};
const validateForm = (data) => {};

// ❌ 避免
const user_name = 'john_doe'; // 下划线命名
const IsLoggedIn = true; // PascalCase 用于变量
const data = []; // 不够描述性
```

#### 常量
```javascript
// ✅ 使用 SCREAMING_SNAKE_CASE
const API_BASE_URL = 'http://localhost:3000/api';
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_TIMEOUT = 5000;

// ✅ 枚举对象
const RESOURCE_STATUS = {
  INSTALLED: 'installed',
  AVAILABLE: 'available',
  UPDATING: 'updating',
  ERROR: 'error'
};
```

#### 组件命名
```javascript
// ✅ 使用 PascalCase
const HomePage = () => {};
const UserProfile = () => {};
const GameResourceCard = () => {};

// ✅ 高阶组件使用 with 前缀
const withAuth = (Component) => {};
const withLoading = (Component) => {};

// ✅ Hook 使用 use 前缀
const useAuth = () => {};
const useGameResources = () => {};
```

### 文件和目录命名

```
// ✅ 组件文件使用 PascalCase
HomePage.js
UserProfile.js
GameResourceCard.js

// ✅ 工具文件使用 camelCase
authService.js
gameResourceService.js
validationUtils.js

// ✅ 目录使用 camelCase
src/
├── components/
├── pages/
├── services/
├── utils/
└── hooks/
```

### 函数定义

#### 箭头函数 vs 函数声明
```javascript
// ✅ 组件使用箭头函数
const HomePage = () => {
  return <div>Home Page</div>;
};

// ✅ 工具函数使用函数声明（支持提升）
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// ✅ 事件处理器使用箭头函数
const handleSubmit = (event) => {
  event.preventDefault();
  // 处理逻辑
};
```

#### 异步函数
```javascript
// ✅ 使用 async/await
const fetchUserData = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user data:', error);
    throw error;
  }
};

// ❌ 避免 Promise 链
const fetchUserData = (userId) => {
  return api.get(`/users/${userId}`)
    .then(response => response.data)
    .catch(error => {
      console.error('Failed to fetch user data:', error);
      throw error;
    });
};
```

### 对象和数组

#### 对象定义
```javascript
// ✅ 使用对象字面量
const user = {
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
  isActive: true
};

// ✅ 使用计算属性名
const createUser = (key, value) => ({
  id: generateId(),
  [key]: value,
  createdAt: new Date()
});

// ✅ 使用对象解构
const { name, email, isActive } = user;

// ✅ 使用展开运算符
const updatedUser = {
  ...user,
  lastLoginAt: new Date()
};
```

#### 数组操作
```javascript
// ✅ 使用数组方法而不是循环
const activeUsers = users.filter(user => user.isActive);
const userNames = users.map(user => user.name);
const totalScore = scores.reduce((sum, score) => sum + score, 0);

// ✅ 使用展开运算符
const newUsers = [...existingUsers, newUser];
const updatedUsers = users.map(user => 
  user.id === targetId ? { ...user, isActive: false } : user
);
```

### 条件语句

```javascript
// ✅ 使用三元运算符处理简单条件
const statusText = isLoading ? '加载中...' : '加载完成';
const buttonText = isInstalled ? '卸载' : '安装';

// ✅ 使用逻辑运算符
const userName = user?.name || '匿名用户';
const shouldShowButton = isLoggedIn && hasPermission;

// ✅ 复杂条件使用 if 语句
if (user && user.isActive && user.permissions.includes('admin')) {
  showAdminPanel();
} else if (user && user.isActive) {
  showUserPanel();
} else {
  showLoginForm();
}

// ✅ 使用 switch 处理多个条件
switch (resource.status) {
  case RESOURCE_STATUS.INSTALLED:
    return <InstalledBadge />;
  case RESOURCE_STATUS.AVAILABLE:
    return <AvailableBadge />;
  case RESOURCE_STATUS.UPDATING:
    return <UpdatingBadge />;
  default:
    return <UnknownBadge />;
}
```

## React 特定风格

### 组件结构

```javascript
// ✅ 标准组件结构
import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';

// 样式组件
const Container = styled(motion.div)`
  padding: ${props => props.theme.spacing.medium};
  background: ${props => props.theme.colors.surface};
`;

const Title = styled.h2`
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing.small};
`;

// 主组件
const GameResourceCard = ({ resource, onInstall, onEnable }) => {
  // State hooks
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Effect hooks
  useEffect(() => {
    // 副作用逻辑
  }, [resource.id]);

  // Event handlers
  const handleInstall = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      await onInstall(resource.id);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [resource.id, onInstall]);

  // Early returns
  if (!resource) {
    return null;
  }

  // Render
  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Title>{resource.name}</Title>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      <ActionButton 
        onClick={handleInstall} 
        disabled={isLoading}
      >
        {isLoading ? '处理中...' : (resource.installed ? '卸载' : '安装')}
      </ActionButton>
    </Container>
  );
};

export default GameResourceCard;
```

### Props 和 PropTypes

```javascript
// ✅ 使用解构赋值
const UserCard = ({ user, onEdit, onDelete, className }) => {
  // 组件逻辑
};

// ✅ 默认参数
const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  disabled = false,
  onClick 
}) => {
  // 组件逻辑
};

// ✅ PropTypes（如果使用）
import PropTypes from 'prop-types';

UserCard.propTypes = {
  user: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired
  }).isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  className: PropTypes.string
};
```

### Hooks 使用

```javascript
// ✅ 自定义 Hook
const useGameResource = (resourceId) => {
  const [resource, setResource] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchResource = useCallback(async () => {
    if (!resourceId) return;
    
    setLoading(true);
    setError(null);
    try {
      const data = await gameResourceService.getResource(resourceId);
      setResource(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [resourceId]);

  useEffect(() => {
    fetchResource();
  }, [fetchResource]);

  const refetch = useCallback(() => {
    fetchResource();
  }, [fetchResource]);

  return { resource, loading, error, refetch };
};
```

## Styled-Components 风格

### 组件命名和组织

```javascript
// ✅ 描述性命名
const HeaderContainer = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing.medium};
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const NavigationList = styled.ul`
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: ${props => props.theme.spacing.small};
`;

const NavigationItem = styled.li`
  /* 样式 */
`;

const NavigationLink = styled.a`
  color: ${props => props.theme.colors.text.primary};
  text-decoration: none;
  padding: ${props => props.theme.spacing.small};
  border-radius: ${props => props.theme.borderRadius.small};
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.hover};
    color: ${props => props.theme.colors.primary};
  }

  &.active {
    background: ${props => props.theme.colors.primary};
    color: white;
  }
`;
```

### 主题使用

```javascript
// ✅ 一致的主题使用
const Button = styled.button`
  /* 基础样式 */
  padding: ${props => props.theme.spacing.small} ${props => props.theme.spacing.medium};
  border-radius: ${props => props.theme.borderRadius.medium};
  border: none;
  font-size: ${props => props.theme.typography.body.fontSize};
  font-weight: ${props => props.theme.typography.body.fontWeight};
  cursor: pointer;
  transition: all 0.2s ease;

  /* 变体样式 */
  ${props => props.variant === 'primary' && `
    background: ${props.theme.colors.primary};
    color: white;
    
    &:hover {
      background: ${props.theme.colors.primaryDark};
    }
  `}

  ${props => props.variant === 'secondary' && `
    background: transparent;
    color: ${props.theme.colors.primary};
    border: 1px solid ${props.theme.colors.primary};
    
    &:hover {
      background: ${props.theme.colors.primary};
      color: white;
    }
  `}

  /* 状态样式 */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;
```

### 响应式设计

```javascript
// ✅ 移动优先的响应式设计
const ResponsiveGrid = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.medium};
  
  /* 移动设备 */
  grid-template-columns: 1fr;
  
  /* 平板设备 */
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 桌面设备 */
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* 大屏设备 */
  @media (min-width: 1440px) {
    grid-template-columns: repeat(4, 1fr);
  }
`;
```

## 注释风格

### JSDoc 注释

```javascript
/**
 * 获取游戏资源列表
 * @param {Object} params - 查询参数
 * @param {string} [params.category] - 资源分类
 * @param {string} [params.search] - 搜索关键词
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.limit=20] - 每页数量
 * @returns {Promise<Array>} 资源列表
 * @throws {Error} 当网络请求失败时抛出错误
 */
const getGameResources = async (params = {}) => {
  // 实现逻辑
};

/**
 * 游戏资源卡片组件
 * @param {Object} props - 组件属性
 * @param {Object} props.resource - 资源对象
 * @param {string} props.resource.id - 资源ID
 * @param {string} props.resource.name - 资源名称
 * @param {Function} props.onInstall - 安装回调函数
 * @param {Function} props.onEnable - 启用回调函数
 * @returns {JSX.Element} React 组件
 */
const GameResourceCard = ({ resource, onInstall, onEnable }) => {
  // 组件实现
};
```

### 行内注释

```javascript
// ✅ 解释复杂逻辑
const calculateInstallProgress = (downloaded, total) => {
  // 避免除零错误
  if (total === 0) return 0;
  
  // 计算百分比并限制在 0-100 范围内
  const percentage = Math.min(Math.max((downloaded / total) * 100, 0), 100);
  
  // 保留两位小数
  return Math.round(percentage * 100) / 100;
};

// ✅ 标记 TODO 和 FIXME
const handleResourceUpdate = async (resourceId) => {
  // TODO: 添加更新进度显示
  // FIXME: 处理网络中断的情况
  
  try {
    await gameResourceService.updateResource(resourceId);
  } catch (error) {
    // 错误处理
  }
};
```

## 错误处理风格

```javascript
// ✅ 统一的错误处理
const handleAsyncOperation = async (operation, errorMessage) => {
  try {
    const result = await operation();
    return { success: true, data: result };
  } catch (error) {
    console.error(errorMessage, error);
    return { 
      success: false, 
      error: error.message || '操作失败' 
    };
  }
};

// ✅ 组件中的错误处理
const ResourceManager = () => {
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleInstall = async (resourceId) => {
    setLoading(true);
    setError(null);
    
    const result = await handleAsyncOperation(
      () => gameResourceService.toggleInstall(resourceId),
      'Failed to install resource'
    );
    
    if (!result.success) {
      setError(result.error);
    }
    
    setLoading(false);
  };

  return (
    <div>
      {error && <ErrorBanner message={error} onDismiss={() => setError(null)} />}
      {/* 其他内容 */}
    </div>
  );
};
```

## 导入和导出风格

```javascript
// ✅ 导入顺序
// 1. React 相关
import React, { useState, useEffect, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// 2. 第三方库
import styled from 'styled-components';
import { motion } from 'framer-motion';
import axios from 'axios';

// 3. 内部模块（按层级排序）
import { AuthProvider } from './context/AuthContext';
import { GameResourceProvider } from './context/GameResourceContext';
import HomePage from './pages/HomePage';
import authService from './services/authService';
import { validateForm } from './utils/validation';

// ✅ 命名导出 vs 默认导出
// 默认导出用于主要组件
export default HomePage;

// 命名导出用于工具函数和常量
export { validateForm, API_ENDPOINTS, RESOURCE_STATUS };

// ✅ 重新导出
export { default as HomePage } from './HomePage';
export { default as LoginPage } from './LoginPage';
export * from './utils';
```

---

*本代码风格指南将根据团队反馈和最佳实践持续更新*