# DCS World 启动器开发工作流程

## 开发环境设置

### 必需工具
- **Node.js**: 版本 16.x 或更高
- **npm**: 版本 8.x 或更高
- **Git**: 版本控制
- **VS Code**: 推荐的代码编辑器
- **Chrome DevTools**: 调试工具

### VS Code 扩展推荐
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json",
    "ritwickdey.liveserver"
  ]
}
```

### 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd dcs-world-launcher

# 安装依赖
npm install

# 启动开发服务器
npm start

# 在新终端中启动 Electron（可选）
npm run electron
```

## 开发流程

### 1. 功能开发流程

#### 步骤 1: 创建功能分支
```bash
# 从 develop 分支创建新功能分支
git checkout develop
git pull origin develop
git checkout -b feature/user-authentication
```

#### 步骤 2: 开发前准备
1. **需求分析**: 明确功能需求和验收标准
2. **技术设计**: 确定实现方案和技术选型
3. **任务分解**: 将功能分解为可管理的小任务

#### 步骤 3: 编码实现
```bash
# 开发过程中频繁提交
git add .
git commit -m "feat(auth): add login form validation"

# 定期推送到远程分支
git push origin feature/user-authentication
```

#### 步骤 4: 代码审查
1. **自我审查**: 检查代码质量和规范
2. **创建 PR**: 提交 Pull Request
3. **同行审查**: 等待团队成员审查
4. **修改完善**: 根据反馈修改代码

#### 步骤 5: 合并代码
```bash
# 合并到 develop 分支
git checkout develop
git pull origin develop
git merge feature/user-authentication
git push origin develop

# 删除功能分支
git branch -d feature/user-authentication
git push origin --delete feature/user-authentication
```

### 2. 日常开发规范

#### 代码提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 示例
feat(auth): add user login functionality
fix(ui): resolve sidebar navigation issue
docs(readme): update installation instructions
style(components): format code with prettier
refactor(services): optimize API service structure
test(auth): add unit tests for login service
chore(deps): update react to version 18.2.0
```

#### 提交类型说明
- **feat**: 新功能
- **fix**: 修复 bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动
- **perf**: 性能优化
- **ci**: CI/CD 相关

#### 分支管理策略
```
main (生产分支)
├── develop (开发分支)
│   ├── feature/user-auth (功能分支)
│   ├── feature/game-launcher (功能分支)
│   └── feature/mod-manager (功能分支)
├── release/v1.0.0 (发布分支)
└── hotfix/critical-bug (热修复分支)
```

## 测试流程

### 1. 单元测试
```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- AuthService.test.js

# 运行测试并生成覆盖率报告
npm test -- --coverage

# 监听模式运行测试
npm test -- --watch
```

#### 测试文件结构
```
src/
├── components/
│   ├── HomePage.js
│   └── __tests__/
│       └── HomePage.test.js
├── services/
│   ├── authService.js
│   └── __tests__/
│       └── authService.test.js
└── utils/
    ├── validation.js
    └── __tests__/
        └── validation.test.js
```

#### 测试编写规范
```javascript
// ✅ 测试文件示例
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider } from '../context/AuthContext';
import LoginPage from '../LoginPage';

describe('LoginPage Component', () => {
  const renderWithProviders = (component) => {
    return render(
      <AuthProvider>
        {component}
      </AuthProvider>
    );
  };

  beforeEach(() => {
    // 测试前的设置
    localStorage.clear();
  });

  afterEach(() => {
    // 测试后的清理
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('should render login form elements', () => {
      renderWithProviders(<LoginPage />);
      
      expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/密码/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    test('should handle form submission', async () => {
      const mockLogin = jest.fn().mockResolvedValue({ success: true });
      renderWithProviders(<LoginPage />);
      
      fireEvent.change(screen.getByLabelText(/用户名/i), {
        target: { value: 'testuser' }
      });
      fireEvent.change(screen.getByLabelText(/密码/i), {
        target: { value: 'password123' }
      });
      fireEvent.click(screen.getByRole('button', { name: /登录/i }));
      
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123'
        });
      });
    });
  });

  describe('Error Handling', () => {
    test('should display error message on login failure', async () => {
      const mockLogin = jest.fn().mockRejectedValue(new Error('登录失败'));
      renderWithProviders(<LoginPage />);
      
      // 触发登录失败
      fireEvent.click(screen.getByRole('button', { name: /登录/i }));
      
      await waitFor(() => {
        expect(screen.getByText(/登录失败/i)).toBeInTheDocument();
      });
    });
  });
});
```

### 2. 集成测试
```bash
# 运行端到端测试（如果配置了 Cypress）
npm run test:e2e

# 运行特定的端到端测试
npm run test:e2e -- --spec "cypress/integration/login.spec.js"
```

### 3. 手动测试清单

#### 功能测试
- [ ] 用户登录/注册功能
- [ ] 游戏资源列表显示
- [ ] 模组安装/卸载功能
- [ ] 设置页面配置保存
- [ ] 侧边栏导航功能

#### 兼容性测试
- [ ] Chrome 浏览器
- [ ] Firefox 浏览器
- [ ] Edge 浏览器
- [ ] 不同屏幕分辨率
- [ ] 移动设备响应式

#### 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] 大量数据渲染性能
- [ ] 内存使用情况
- [ ] 网络请求优化

## 代码质量保证

### 1. 代码检查
```bash
# ESLint 检查
npm run lint

# 自动修复可修复的问题
npm run lint:fix

# Prettier 格式化
npm run format

# 类型检查（如果使用 TypeScript）
npm run type-check
```

### 2. 预提交钩子
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "src/**/*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "src/**/*.{css,scss,less}": [
      "prettier --write",
      "git add"
    ]
  }
}
```

### 3. 代码审查清单

#### 功能性
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误处理是否完善
- [ ] 性能是否满足要求

#### 代码质量
- [ ] 代码是否遵循项目规范
- [ ] 变量和函数命名是否清晰
- [ ] 代码是否有适当的注释
- [ ] 是否有重复代码

#### 安全性
- [ ] 输入验证是否充分
- [ ] 敏感信息是否泄露
- [ ] XSS 和 CSRF 防护
- [ ] 权限控制是否正确

#### 可维护性
- [ ] 代码结构是否清晰
- [ ] 组件是否可复用
- [ ] 依赖关系是否合理
- [ ] 文档是否完善

## 发布流程

### 1. 版本管理
```bash
# 查看当前版本
npm version

# 更新版本号
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0

# 手动指定版本
npm version 1.2.3
```

### 2. 构建和部署
```bash
# 生产构建
npm run build

# 构建 Electron 应用
npm run build:electron

# 生成安装包
npm run dist

# 部署到服务器（如果是 Web 版本）
npm run deploy
```

### 3. 发布清单

#### 发布前检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 版本号更新
- [ ] 更新日志编写
- [ ] 文档更新

#### 发布步骤
1. **创建发布分支**
   ```bash
   git checkout -b release/v1.0.0
   ```

2. **最终测试**
   - 运行完整测试套件
   - 手动测试关键功能
   - 性能测试

3. **合并到主分支**
   ```bash
   git checkout main
   git merge release/v1.0.0
   git tag v1.0.0
   git push origin main --tags
   ```

4. **部署到生产环境**
   - 构建生产版本
   - 部署到服务器
   - 监控部署状态

5. **发布后验证**
   - 验证生产环境功能
   - 监控错误日志
   - 用户反馈收集

## 问题处理流程

### 1. Bug 修复流程
```bash
# 创建 bug 修复分支
git checkout -b fix/login-validation-error

# 修复 bug
# ...

# 提交修复
git commit -m "fix(auth): resolve login validation error"

# 合并到 develop 分支
git checkout develop
git merge fix/login-validation-error
```

### 2. 热修复流程
```bash
# 从 main 分支创建热修复分支
git checkout main
git checkout -b hotfix/critical-security-issue

# 修复问题
# ...

# 提交修复
git commit -m "fix(security): resolve critical security vulnerability"

# 合并到 main 和 develop
git checkout main
git merge hotfix/critical-security-issue
git checkout develop
git merge hotfix/critical-security-issue

# 立即发布
npm version patch
git push origin main --tags
```

### 3. 问题跟踪

#### 问题分类
- **P0**: 严重问题，影响核心功能
- **P1**: 重要问题，影响主要功能
- **P2**: 一般问题，影响次要功能
- **P3**: 轻微问题，不影响功能使用

#### 处理时间
- **P0**: 24小时内修复
- **P1**: 3天内修复
- **P2**: 1周内修复
- **P3**: 下个版本修复

## 团队协作

### 1. 沟通规范
- **日常沟通**: 使用团队聊天工具
- **技术讨论**: 创建技术文档或 RFC
- **代码审查**: 在 PR 中进行讨论
- **问题报告**: 使用 Issue 跟踪系统

### 2. 会议安排
- **每日站会**: 15分钟，同步进度和问题
- **周计划会**: 1小时，制定周计划
- **代码审查会**: 根据需要安排
- **技术分享会**: 每月一次

### 3. 知识分享
- **技术文档**: 维护项目技术文档
- **代码注释**: 重要逻辑添加详细注释
- **最佳实践**: 总结和分享最佳实践
- **培训材料**: 为新成员准备培训材料

---

*本开发工作流程将根据团队实际情况和项目需求持续优化*