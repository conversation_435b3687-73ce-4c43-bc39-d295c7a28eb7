# DCS World 启动器项目规范

## 项目概述
这是一个基于 React 和 Electron 的 DCS World 游戏启动器，提供用户认证、游戏模组管理、商店系统等功能。

## 技术栈
- **前端框架**: React 18
- **样式方案**: styled-components
- **动画库**: framer-motion
- **路由**: react-router-dom
- **桌面应用**: Electron
- **HTTP 客户端**: axios
- **图标**: react-icons

## 项目结构规范

### 目录结构
```
src/
├── components/          # 可复用组件
│   └── layout/         # 布局组件
├── context/            # React Context
├── pages/              # 页面组件
├── services/           # API 服务
└── styles/             # 全局样式和主题
```

### 文件命名规范
- **组件文件**: PascalCase (如 `HomePage.js`, `Sidebar.js`)
- **服务文件**: camelCase + Service 后缀 (如 `authService.js`)
- **Context 文件**: PascalCase + Context 后缀 (如 `AuthContext.js`)
- **样式文件**: PascalCase (如 `GlobalStyle.js`)

## 编码规范

### React 组件规范
1. **函数组件优先**: 使用函数组件和 Hooks
2. **组件结构**:
   ```javascript
   // 导入
   import React, { useState, useEffect } from 'react';
   import styled from 'styled-components';
   
   // 样式组件
   const Container = styled.div`
     // 样式
   `;
   
   // 主组件
   const ComponentName = () => {
     // Hooks
     // 事件处理函数
     // 渲染
   };
   
   export default ComponentName;
   ```

### 样式规范
1. **使用 styled-components** 进行组件样式定义
2. **主题系统**: 使用 `src/styles/theme.js` 中定义的主题变量
3. **响应式设计**: 支持不同屏幕尺寸
4. **动画效果**: 使用 framer-motion 实现流畅动画

### 状态管理规范
1. **本地状态**: 使用 `useState` 管理组件内部状态
2. **全局状态**: 使用 React Context (AuthContext, GameResourceContext)
3. **异步状态**: 使用 `useEffect` 和 `useState` 组合管理

### API 服务规范
1. **服务文件**: 统一放在 `src/services/` 目录
2. **错误处理**: 统一的错误处理机制
3. **模拟模式**: 支持 MOCK_MODE 用于开发测试
4. **请求拦截**: 统一添加认证 token

## 开发模式

### 模拟数据模式
- 设置 `MOCK_MODE = true` 启用模拟数据
- 用于无后端环境下的前端开发
- 包含完整的用户认证和游戏资源管理模拟

### 生产模式
- 设置 `MOCK_MODE = false` 连接真实后端
- API 基础地址: `http://localhost:3000/api`

## 功能模块规范

### 用户认证模块
- 登录/注册功能
- Token 管理和自动刷新
- 用户状态持久化

### 游戏资源管理模块
- 模组列表展示和筛选
- 安装/卸载操作
- 启用/禁用管理
- 更新和修复功能

### 商店系统模块
- 产品展示和筛选
- 购买流程模拟
- 分类和搜索功能

### 系统设置模块
- 游戏配置管理
- 启动器设置
- 网络和账户设置

## 性能优化规范

1. **组件懒加载**: 使用 React.lazy 和 Suspense
2. **图片优化**: 优先使用 SVG 格式
3. **代码分割**: 按页面进行代码分割
4. **缓存策略**: 合理使用 localStorage 和 sessionStorage

## 安全规范

1. **敏感信息**: 不在代码中硬编码密钥和敏感信息
2. **XSS 防护**: 避免直接使用 dangerouslySetInnerHTML
3. **CSRF 防护**: API 请求包含适当的安全头
4. **输入验证**: 对用户输入进行验证和清理

## 测试规范

1. **单元测试**: 使用 Jest 和 React Testing Library
2. **组件测试**: 测试组件的渲染和交互
3. **服务测试**: 测试 API 服务的调用和错误处理
4. **端到端测试**: 使用 Cypress 进行关键流程测试

## 部署规范

### 开发环境
- 运行 `npm start` 启动开发服务器
- 端口: 3000
- 热重载支持

### 生产环境
- 运行 `npm run build` 构建生产版本
- 使用 `npm run electron` 打包桌面应用
- 静态资源优化和压缩

## Git 规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 类型说明
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 分支规范
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 代码审查规范

1. **功能完整性**: 确保功能按需求实现
2. **代码质量**: 遵循编码规范和最佳实践
3. **性能考虑**: 避免性能瓶颈
4. **安全检查**: 确保没有安全漏洞
5. **测试覆盖**: 确保有适当的测试覆盖

## 文档规范

1. **README**: 项目介绍、安装和使用说明
2. **API 文档**: 详细的 API 接口文档
3. **组件文档**: 重要组件的使用说明
4. **部署文档**: 部署流程和注意事项

---

*本规范文档会根据项目发展持续更新和完善*