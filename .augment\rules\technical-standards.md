# DCS World 启动器技术规范

## 代码质量标准

### ESLint 配置
```json
{
  "extends": [
    "react-app",
    "react-app/jest"
  ],
  "rules": {
    "no-unused-vars": "warn",
    "no-console": "warn",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### Prettier 配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## React 开发标准

### Hooks 使用规范

#### useState
```javascript
// ✅ 正确
const [isLoading, setIsLoading] = useState(false);
const [user, setUser] = useState(null);
const [resources, setResources] = useState([]);

// ❌ 错误
const [data, setData] = useState(); // 缺少初始值
```

#### useEffect
```javascript
// ✅ 正确 - 明确依赖项
useEffect(() => {
  fetchUserData();
}, [userId]);

// ✅ 正确 - 清理函数
useEffect(() => {
  const timer = setInterval(() => {
    checkUpdates();
  }, 30000);
  
  return () => clearInterval(timer);
}, []);

// ❌ 错误 - 缺少依赖项
useEffect(() => {
  fetchData(userId);
}, []); // 应该包含 userId
```

#### 自定义 Hooks
```javascript
// ✅ 正确的自定义 Hook
const useGameResources = () => {
  const [resources, setResources] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchResources = useCallback(async () => {
    setLoading(true);
    try {
      const data = await gameResourceService.getResources();
      setResources(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  return { resources, loading, error, fetchResources };
};
```

### 组件设计模式

#### 容器组件与展示组件分离
```javascript
// 容器组件 - 处理逻辑
const ModulesPageContainer = () => {
  const { resources, loading, error } = useGameResources();
  const [filter, setFilter] = useState('all');
  
  const filteredResources = useMemo(() => {
    return resources.filter(resource => {
      if (filter === 'all') return true;
      return resource.status === filter;
    });
  }, [resources, filter]);

  return (
    <ModulesPageView
      resources={filteredResources}
      loading={loading}
      error={error}
      filter={filter}
      onFilterChange={setFilter}
    />
  );
};

// 展示组件 - 纯渲染
const ModulesPageView = ({ resources, loading, error, filter, onFilterChange }) => {
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;
  
  return (
    <Container>
      <FilterBar value={filter} onChange={onFilterChange} />
      <ResourceList resources={resources} />
    </Container>
  );
};
```

## Styled-Components 规范

### 组件命名
```javascript
// ✅ 正确 - 描述性命名
const HeaderContainer = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const NavigationList = styled.ul`
  display: flex;
  list-style: none;
  gap: 1rem;
`;

const ActionButton = styled.button`
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
`;
```

### 主题使用
```javascript
// ✅ 正确 - 使用主题变量
const Card = styled.div`
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.medium};
  box-shadow: ${props => props.theme.shadows.small};
`;

// ❌ 错误 - 硬编码值
const Card = styled.div`
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
`;
```

### 响应式设计
```javascript
const ResponsiveContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
  
  @media (max-width: 480px) {
    padding: 0.5rem;
  }
`;
```

## 状态管理规范

### Context 设计
```javascript
// ✅ 正确的 Context 设计
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const login = useCallback(async (credentials) => {
    setLoading(true);
    setError(null);
    try {
      const userData = await authService.login(credentials);
      setUser(userData);
      localStorage.setItem('token', userData.token);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    setUser(null);
    localStorage.removeItem('token');
    authService.logout();
  }, []);

  const value = useMemo(() => ({
    user,
    loading,
    error,
    login,
    logout,
    isAuthenticated: !!user
  }), [user, loading, error, login, logout]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

## API 服务规范

### 服务结构
```javascript
// ✅ 标准服务结构
const createApiService = (baseURL) => {
  const api = axios.create({
    baseURL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  });

  // 请求拦截器
  api.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // 响应拦截器
  api.interceptors.response.use(
    (response) => response.data,
    (error) => {
      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return api;
};
```

### 错误处理
```javascript
// ✅ 统一错误处理
const handleApiError = (error) => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response;
    switch (status) {
      case 400:
        return new Error(data.message || '请求参数错误');
      case 401:
        return new Error('未授权，请重新登录');
      case 403:
        return new Error('权限不足');
      case 404:
        return new Error('资源不存在');
      case 500:
        return new Error('服务器内部错误');
      default:
        return new Error(data.message || '请求失败');
    }
  } else if (error.request) {
    // 网络错误
    return new Error('网络连接失败，请检查网络设置');
  } else {
    // 其他错误
    return new Error(error.message || '未知错误');
  }
};
```

## 性能优化标准

### 组件优化
```javascript
// ✅ 使用 React.memo 优化渲染
const ResourceCard = React.memo(({ resource, onInstall, onEnable }) => {
  return (
    <Card>
      <Title>{resource.name}</Title>
      <Description>{resource.description}</Description>
      <Actions>
        <Button onClick={() => onInstall(resource.id)}>
          {resource.installed ? '卸载' : '安装'}
        </Button>
        {resource.installed && (
          <Button onClick={() => onEnable(resource.id)}>
            {resource.enabled ? '禁用' : '启用'}
          </Button>
        )}
      </Actions>
    </Card>
  );
});

// ✅ 使用 useCallback 优化回调函数
const ResourceList = ({ resources }) => {
  const handleInstall = useCallback((resourceId) => {
    gameResourceService.toggleInstall(resourceId);
  }, []);

  const handleEnable = useCallback((resourceId) => {
    gameResourceService.toggleEnable(resourceId);
  }, []);

  return (
    <List>
      {resources.map(resource => (
        <ResourceCard
          key={resource.id}
          resource={resource}
          onInstall={handleInstall}
          onEnable={handleEnable}
        />
      ))}
    </List>
  );
};
```

### 懒加载
```javascript
// ✅ 页面懒加载
const HomePage = lazy(() => import('../pages/HomePage'));
const ModulesPage = lazy(() => import('../pages/ModulesPage'));
const StorePage = lazy(() => import('../pages/StorePage'));
const SettingsPage = lazy(() => import('../pages/SettingsPage'));

// 在路由中使用
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/" element={<HomePage />} />
    <Route path="/modules" element={<ModulesPage />} />
    <Route path="/store" element={<StorePage />} />
    <Route path="/settings" element={<SettingsPage />} />
  </Routes>
</Suspense>
```

## 测试标准

### 组件测试
```javascript
// ✅ 组件测试示例
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider } from '../context/AuthContext';
import LoginPage from '../pages/LoginPage';

describe('LoginPage', () => {
  const renderWithAuth = (component) => {
    return render(
      <AuthProvider>
        {component}
      </AuthProvider>
    );
  };

  test('renders login form', () => {
    renderWithAuth(<LoginPage />);
    
    expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/密码/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument();
  });

  test('handles login submission', async () => {
    const mockLogin = jest.fn();
    renderWithAuth(<LoginPage />);
    
    fireEvent.change(screen.getByLabelText(/用户名/i), {
      target: { value: 'testuser' }
    });
    fireEvent.change(screen.getByLabelText(/密码/i), {
      target: { value: 'password' }
    });
    fireEvent.click(screen.getByRole('button', { name: /登录/i }));
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password'
      });
    });
  });
});
```

### 服务测试
```javascript
// ✅ 服务测试示例
import authService from '../services/authService';
import axios from 'axios';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('authService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('login success', async () => {
    const mockResponse = {
      data: {
        user: { id: 1, username: 'testuser' },
        token: 'mock-token'
      }
    };
    mockedAxios.post.mockResolvedValue(mockResponse);

    const result = await authService.login({
      username: 'testuser',
      password: 'password'
    });

    expect(result).toEqual(mockResponse.data);
    expect(mockedAxios.post).toHaveBeenCalledWith('/auth/login', {
      username: 'testuser',
      password: 'password'
    });
  });

  test('login failure', async () => {
    const mockError = new Error('Invalid credentials');
    mockedAxios.post.mockRejectedValue(mockError);

    await expect(authService.login({
      username: 'testuser',
      password: 'wrongpassword'
    })).rejects.toThrow('Invalid credentials');
  });
});
```

## 安全标准

### 输入验证
```javascript
// ✅ 输入验证示例
const validateLoginForm = (data) => {
  const errors = {};
  
  if (!data.username || data.username.trim().length < 3) {
    errors.username = '用户名至少需要3个字符';
  }
  
  if (!data.password || data.password.length < 6) {
    errors.password = '密码至少需要6个字符';
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(data.username)) {
    errors.username = '用户名只能包含字母、数字和下划线';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
```

### XSS 防护
```javascript
// ✅ 安全的内容渲染
const SafeContent = ({ content }) => {
  // 使用 DOMPurify 清理 HTML 内容
  const cleanContent = DOMPurify.sanitize(content);
  
  return (
    <div dangerouslySetInnerHTML={{ __html: cleanContent }} />
  );
};

// ✅ 更安全的方式 - 避免使用 dangerouslySetInnerHTML
const SafeText = ({ text }) => {
  return <span>{text}</span>; // React 自动转义
};
```

## 部署标准

### 环境变量
```javascript
// ✅ 环境配置
const config = {
  API_BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000/api',
  MOCK_MODE: process.env.REACT_APP_MOCK_MODE === 'true',
  VERSION: process.env.REACT_APP_VERSION || '1.0.0',
  ENVIRONMENT: process.env.NODE_ENV || 'development'
};

export default config;
```

### 构建优化
```json
{
  "scripts": {
    "build": "react-scripts build",
    "build:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js",
    "build:prod": "NODE_ENV=production npm run build"
  }
}
```

---

*本技术规范将根据项目需求和技术发展持续更新*