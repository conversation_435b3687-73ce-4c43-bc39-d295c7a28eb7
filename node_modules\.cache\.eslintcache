[{"D:\\Test\\Battle Launcher\\src\\index.js": "1", "D:\\Test\\Battle Launcher\\src\\App.js": "2", "D:\\Test\\Battle Launcher\\src\\styles\\theme.js": "3", "D:\\Test\\Battle Launcher\\src\\styles\\GlobalStyle.js": "4", "D:\\Test\\Battle Launcher\\src\\context\\AuthContext.js": "5", "D:\\Test\\Battle Launcher\\src\\context\\GameResourceContext.js": "6", "D:\\Test\\Battle Launcher\\src\\pages\\LoginPage.js": "7", "D:\\Test\\Battle Launcher\\src\\pages\\SettingsPage.js": "8", "D:\\Test\\Battle Launcher\\src\\pages\\HomePage.js": "9", "D:\\Test\\Battle Launcher\\src\\pages\\ModulesPage.js": "10", "D:\\Test\\Battle Launcher\\src\\pages\\StorePage.js": "11", "D:\\Test\\Battle Launcher\\src\\services\\gameResourceService.js": "12", "D:\\Test\\Battle Launcher\\src\\services\\authService.js": "13", "D:\\Test\\Battle Launcher\\src\\components\\layout\\TitleBar.js": "14", "D:\\Test\\Battle Launcher\\src\\components\\layout\\Layout.js": "15", "D:\\Test\\Battle Launcher\\src\\components\\layout\\Sidebar.js": "16", "D:\\Test\\Battle Launcher\\src\\pages\\admin\\UserManagement.js": "17", "D:\\Test\\Battle Launcher\\src\\pages\\admin\\ModuleManagement.js": "18"}, {"size": 1059, "mtime": 1753235133396, "results": "19", "hashOfConfig": "20"}, {"size": 2411, "mtime": 1753237276694, "results": "21", "hashOfConfig": "20"}, {"size": 3502, "mtime": 1753237609377, "results": "22", "hashOfConfig": "20"}, {"size": 2932, "mtime": 1753235169717, "results": "23", "hashOfConfig": "20"}, {"size": 3689, "mtime": 1753235213620, "results": "24", "hashOfConfig": "20"}, {"size": 6643, "mtime": 1753237261244, "results": "25", "hashOfConfig": "20"}, {"size": 8110, "mtime": 1753235404135, "results": "26", "hashOfConfig": "20"}, {"size": 24115, "mtime": 1753235695211, "results": "27", "hashOfConfig": "20"}, {"size": 9674, "mtime": 1753239038452, "results": "28", "hashOfConfig": "20"}, {"size": 15145, "mtime": 1753239258274, "results": "29", "hashOfConfig": "20"}, {"size": 19017, "mtime": 1753235600846, "results": "30", "hashOfConfig": "20"}, {"size": 14261, "mtime": 1753240557288, "results": "31", "hashOfConfig": "20"}, {"size": 13375, "mtime": 1753240910052, "results": "32", "hashOfConfig": "20"}, {"size": 3673, "mtime": 1753235309835, "results": "33", "hashOfConfig": "20"}, {"size": 2389, "mtime": 1753235326844, "results": "34", "hashOfConfig": "20"}, {"size": 10691, "mtime": 1753239025525, "results": "35", "hashOfConfig": "20"}, {"size": 12587, "mtime": 1753237701994, "results": "36", "hashOfConfig": "20"}, {"size": 21834, "mtime": 1753239276683, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lqs7t7", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Test\\Battle Launcher\\src\\index.js", [], [], "D:\\Test\\Battle Launcher\\src\\App.js", [], [], "D:\\Test\\Battle Launcher\\src\\styles\\theme.js", [], [], "D:\\Test\\Battle Launcher\\src\\styles\\GlobalStyle.js", [], [], "D:\\Test\\Battle Launcher\\src\\context\\AuthContext.js", [], [], "D:\\Test\\Battle Launcher\\src\\context\\GameResourceContext.js", ["92"], [], "D:\\Test\\Battle Launcher\\src\\pages\\LoginPage.js", [], [], "D:\\Test\\Battle Launcher\\src\\pages\\SettingsPage.js", ["93"], [], "D:\\Test\\Battle Launcher\\src\\pages\\HomePage.js", [], [], "D:\\Test\\Battle Launcher\\src\\pages\\ModulesPage.js", ["94", "95"], [], "D:\\Test\\Battle Launcher\\src\\pages\\StorePage.js", ["96", "97", "98"], [], "D:\\Test\\Battle Launcher\\src\\services\\gameResourceService.js", [], [], "D:\\Test\\Battle Launcher\\src\\services\\authService.js", [], [], "D:\\Test\\Battle Launcher\\src\\components\\layout\\TitleBar.js", [], [], "D:\\Test\\Battle Launcher\\src\\components\\layout\\Layout.js", [], [], "D:\\Test\\Battle Launcher\\src\\components\\layout\\Sidebar.js", [], [], "D:\\Test\\Battle Launcher\\src\\pages\\admin\\UserManagement.js", [], [], "D:\\Test\\Battle Launcher\\src\\pages\\admin\\ModuleManagement.js", ["99", "100"], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 72, "column": 6, "nodeType": "103", "endLine": 72, "endColumn": 12, "suggestions": "104"}, {"ruleId": "105", "severity": 1, "message": "106", "line": 1, "column": 27, "nodeType": "107", "messageId": "108", "endLine": 1, "endColumn": 36}, {"ruleId": "105", "severity": 1, "message": "106", "line": 1, "column": 27, "nodeType": "107", "messageId": "108", "endLine": 1, "endColumn": 36}, {"ruleId": "105", "severity": 1, "message": "109", "line": 294, "column": 5, "nodeType": "107", "messageId": "108", "endLine": 294, "endColumn": 15}, {"ruleId": "105", "severity": 1, "message": "106", "line": 1, "column": 27, "nodeType": "107", "messageId": "108", "endLine": 1, "endColumn": 36}, {"ruleId": "105", "severity": 1, "message": "110", "line": 4, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 26}, {"ruleId": "105", "severity": 1, "message": "111", "line": 341, "column": 19, "nodeType": "107", "messageId": "108", "endLine": 341, "endColumn": 29}, {"ruleId": "112", "severity": 1, "message": "113", "line": 602, "column": 36, "nodeType": "114", "messageId": "115", "endLine": 602, "endColumn": 38}, {"ruleId": "112", "severity": 1, "message": "113", "line": 602, "column": 95, "nodeType": "114", "messageId": "115", "endLine": 602, "endColumn": 97}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchResources'. Either include it or remove the dependency array.", "ArrayExpression", ["116"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'categories' is assigned a value but never used.", "'useGameResources' is defined but never used.", "'setLoading' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", {"desc": "117", "fix": "118"}, "Update the dependencies array to be: [fetchResources, user]", {"range": "119", "text": "120"}, [1932, 1938], "[fetchResources, user]"]