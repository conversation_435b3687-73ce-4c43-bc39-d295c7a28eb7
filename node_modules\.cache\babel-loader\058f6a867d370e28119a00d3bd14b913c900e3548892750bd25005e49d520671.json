{"ast": null, "code": "import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟游戏资源数据\nlet mockResources = [{\n  id: 1,\n  name: 'F/A-18C Hornet',\n  category: 'aircraft-jet',\n  description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\n  installed: true,\n  enabled: true,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '15.2 GB',\n  author: 'Eagle Dynamics',\n  uploadDate: '2023-01-15',\n  downloadCount: 15420,\n  rating: 4.8\n}, {\n  id: 2,\n  name: 'A-10C II Tank Killer',\n  category: 'aircraft-jet',\n  description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\n  installed: true,\n  enabled: false,\n  hasUpdate: true,\n  updating: false,\n  version: '2.7.5',\n  size: '12.8 GB',\n  author: 'Eagle Dynamics',\n  uploadDate: '2023-02-20',\n  downloadCount: 12850,\n  rating: 4.7\n}, {\n  id: 3,\n  name: 'F-16C Viper',\n  category: 'aircraft-jet',\n  description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\n  installed: false,\n  enabled: false,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.0',\n  size: '14.5 GB',\n  author: 'Eagle Dynamics',\n  uploadDate: '2023-03-10',\n  downloadCount: 18920,\n  rating: 4.9\n}, {\n  id: 4,\n  name: 'Persian Gulf Map',\n  category: 'terrain',\n  description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\n  installed: true,\n  enabled: true,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '8.9 GB',\n  author: 'Eagle Dynamics',\n  uploadDate: '2023-01-25',\n  downloadCount: 9540,\n  rating: 4.6\n}, {\n  id: 5,\n  name: 'Syria Map',\n  category: 'terrain',\n  description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\n  installed: false,\n  enabled: false,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '7.2 GB',\n  author: 'Eagle Dynamics',\n  uploadDate: '2023-04-05',\n  downloadCount: 7320,\n  rating: 4.5\n}];\n\n// 模拟API响应\nconst mockAPI = {\n  getResources: async (params = {}) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    let filtered = [...mockResources];\n    if (params.category && params.category !== 'all') {\n      filtered = filtered.filter(r => r.category === params.category);\n    }\n    if (params.search) {\n      filtered = filtered.filter(r => r.name.toLowerCase().includes(params.search.toLowerCase()));\n    }\n    return filtered;\n  },\n  toggleInstall: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.installed = !resource.installed;\n      if (!resource.installed) {\n        resource.enabled = false;\n      }\n    }\n    return resource;\n  },\n  toggleEnable: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource && resource.installed) {\n      resource.enabled = !resource.enabled;\n    }\n    return resource;\n  },\n  checkUpdates: async () => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    return mockResources.filter(r => r.hasUpdate);\n  },\n  updateResource: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.hasUpdate = false;\n      resource.updating = false;\n    }\n    return resource;\n  },\n  repairResource: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 600));\n    const resource = mockResources.find(r => r.id === resourceId);\n    return resource;\n  },\n  // 管理员API - 上传模组\n  uploadModule: async moduleData => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const newModule = {\n      id: mockResources.length + 1,\n      name: moduleData.name,\n      category: moduleData.category,\n      description: moduleData.description,\n      installed: false,\n      enabled: false,\n      hasUpdate: false,\n      updating: false,\n      version: moduleData.version || '1.0.0',\n      size: moduleData.size || '0 MB',\n      author: moduleData.author,\n      uploadDate: new Date().toISOString().split('T')[0],\n      downloadCount: 0,\n      rating: 0\n    };\n    mockResources.push(newModule);\n    return newModule;\n  },\n  // 管理员API - 更新模组信息\n  updateModule: async (moduleId, moduleData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);\n    if (moduleIndex === -1) {\n      throw new Error('模组不存在');\n    }\n    mockResources[moduleIndex] = {\n      ...mockResources[moduleIndex],\n      ...moduleData\n    };\n    return mockResources[moduleIndex];\n  },\n  // 管理员API - 删除模组\n  deleteModule: async moduleId => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);\n    if (moduleIndex === -1) {\n      throw new Error('模组不存在');\n    }\n    mockResources.splice(moduleIndex, 1);\n    return {\n      success: true\n    };\n  },\n  // 管理员API - 获取所有模组（包括未发布的）\n  getAllModules: async () => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    return mockResources;\n  },\n  // 根据用户权限过滤模组\n  getAuthorizedModules: async userAuthorizedModules => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (!userAuthorizedModules || userAuthorizedModules.length === 0) {\n      return [];\n    }\n    return mockResources.filter(r => userAuthorizedModules.includes(r.id));\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getResources(params);\n      } else {\n        const response = await api.get('/game-resources', {\n          params\n        });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源分类\n  async getCategories() {\n    try {\n      if (MOCK_MODE) {\n        return [{\n          id: 'aircraft-jet',\n          name: '喷气发动机飞机'\n        }, {\n          id: 'aircraft-prop',\n          name: '活塞发动机飞机'\n        }, {\n          id: 'terrain',\n          name: '地形'\n        }, {\n          id: 'campaign',\n          name: '战役'\n        }];\n      } else {\n        const response = await api.get('/game-resources/categories');\n        return response.categories || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return mockResources.find(r => r.id === resourceId);\n      } else {\n        const response = await api.get(`/game-resources/${resourceId}`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleInstall(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleEnable(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 检查更新\n  async checkUpdates() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.checkUpdates();\n      } else {\n        const response = await api.post('/game-resources/check-updates');\n        return response.updates || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/update`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.repairResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/repair`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: {\n          q: query,\n          ...filters\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 上传模组\n  async uploadModule(moduleData, file) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.uploadModule(moduleData);\n      } else {\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('data', JSON.stringify(moduleData));\n        const response = await api.post('/admin/modules/upload', formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        return response.module;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 更新模组\n  async updateModule(moduleId, moduleData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateModule(moduleId, moduleData);\n      } else {\n        const response = await api.put(`/admin/modules/${moduleId}`, moduleData);\n        return response.module;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 删除模组\n  async deleteModule(moduleId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteModule(moduleId);\n      } else {\n        const response = await api.delete(`/admin/modules/${moduleId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 获取所有模组\n  async getAllModules() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAllModules();\n      } else {\n        const response = await api.get('/admin/modules');\n        return response.modules || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 根据用户权限获取授权模组\n  async getAuthorizedModules(userAuthorizedModules) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAuthorizedModules(userAuthorizedModules);\n      } else {\n        const response = await api.post('/game-resources/authorized', {\n          authorizedModules: userAuthorizedModules\n        });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  }\n};\nexport default gameResourceService;", "map": {"version": 3, "names": ["axios", "MOCK_MODE", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "mockResources", "id", "name", "category", "description", "installed", "enabled", "hasUpdate", "updating", "version", "size", "author", "uploadDate", "downloadCount", "rating", "mockAPI", "getResources", "params", "Promise", "resolve", "setTimeout", "filtered", "filter", "r", "search", "toLowerCase", "includes", "toggleInstall", "resourceId", "resource", "find", "toggleEnable", "checkUpdates", "updateResource", "repairResource", "uploadModule", "moduleData", "newModule", "length", "Date", "toISOString", "split", "push", "updateModule", "moduleId", "moduleIndex", "findIndex", "Error", "deleteModule", "splice", "success", "getAllModules", "getAuthorizedModules", "userAuthorizedModules", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "gameResourceService", "get", "resources", "getCategories", "categories", "getResource", "post", "updates", "cleanCache", "batchOperation", "operation", "resourceIds", "getInstallProgress", "progress", "cancelInstall", "searchResources", "query", "filters", "q", "getRecommended", "getLatest", "limit", "getPopular", "rateResource", "comment", "getResourceRatings", "ratings", "file", "formData", "FormData", "append", "JSON", "stringify", "module", "put", "delete", "modules", "authorizedModules"], "sources": ["D:/Test/Battle Launcher/src/services/gameResourceService.js"], "sourcesContent": ["import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000, // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟游戏资源数据\nlet mockResources = [\n  {\n    id: 1,\n    name: 'F/A-18C Hornet',\n    category: 'aircraft-jet',\n    description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\n    installed: true,\n    enabled: true,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '15.2 GB',\n    author: 'Eagle Dynamics',\n    uploadDate: '2023-01-15',\n    downloadCount: 15420,\n    rating: 4.8\n  },\n  {\n    id: 2,\n    name: 'A-10C II Tank Killer',\n    category: 'aircraft-jet',\n    description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\n    installed: true,\n    enabled: false,\n    hasUpdate: true,\n    updating: false,\n    version: '2.7.5',\n    size: '12.8 GB',\n    author: 'Eagle Dynamics',\n    uploadDate: '2023-02-20',\n    downloadCount: 12850,\n    rating: 4.7\n  },\n  {\n    id: 3,\n    name: 'F-16C Viper',\n    category: 'aircraft-jet',\n    description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\n    installed: false,\n    enabled: false,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.0',\n    size: '14.5 GB',\n    author: 'Eagle Dynamics',\n    uploadDate: '2023-03-10',\n    downloadCount: 18920,\n    rating: 4.9\n  },\n  {\n    id: 4,\n    name: 'Persian Gulf Map',\n    category: 'terrain',\n    description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\n    installed: true,\n    enabled: true,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '8.9 GB',\n    author: 'Eagle Dynamics',\n    uploadDate: '2023-01-25',\n    downloadCount: 9540,\n    rating: 4.6\n  },\n  {\n    id: 5,\n    name: 'Syria Map',\n    category: 'terrain',\n    description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\n    installed: false,\n    enabled: false,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '7.2 GB',\n    author: 'Eagle Dynamics',\n    uploadDate: '2023-04-05',\n    downloadCount: 7320,\n    rating: 4.5\n  }\n];\n\n// 模拟API响应\nconst mockAPI = {\n  getResources: async (params = {}) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    let filtered = [...mockResources];\n    \n    if (params.category && params.category !== 'all') {\n      filtered = filtered.filter(r => r.category === params.category);\n    }\n    \n    if (params.search) {\n      filtered = filtered.filter(r => \n        r.name.toLowerCase().includes(params.search.toLowerCase())\n      );\n    }\n    \n    return filtered;\n  },\n  \n  toggleInstall: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.installed = !resource.installed;\n      if (!resource.installed) {\n        resource.enabled = false;\n      }\n    }\n    return resource;\n  },\n  \n  toggleEnable: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource && resource.installed) {\n      resource.enabled = !resource.enabled;\n    }\n    return resource;\n  },\n  \n  checkUpdates: async () => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    return mockResources.filter(r => r.hasUpdate);\n  },\n  \n  updateResource: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.hasUpdate = false;\n      resource.updating = false;\n    }\n    return resource;\n  },\n  \n  repairResource: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 600));\n    const resource = mockResources.find(r => r.id === resourceId);\n    return resource;\n  },\n\n  // 管理员API - 上传模组\n  uploadModule: async (moduleData) => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const newModule = {\n      id: mockResources.length + 1,\n      name: moduleData.name,\n      category: moduleData.category,\n      description: moduleData.description,\n      installed: false,\n      enabled: false,\n      hasUpdate: false,\n      updating: false,\n      version: moduleData.version || '1.0.0',\n      size: moduleData.size || '0 MB',\n      author: moduleData.author,\n      uploadDate: new Date().toISOString().split('T')[0],\n      downloadCount: 0,\n      rating: 0\n    };\n    mockResources.push(newModule);\n    return newModule;\n  },\n\n  // 管理员API - 更新模组信息\n  updateModule: async (moduleId, moduleData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);\n    if (moduleIndex === -1) {\n      throw new Error('模组不存在');\n    }\n    mockResources[moduleIndex] = { ...mockResources[moduleIndex], ...moduleData };\n    return mockResources[moduleIndex];\n  },\n\n  // 管理员API - 删除模组\n  deleteModule: async (moduleId) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);\n    if (moduleIndex === -1) {\n      throw new Error('模组不存在');\n    }\n    mockResources.splice(moduleIndex, 1);\n    return { success: true };\n  },\n\n  // 管理员API - 获取所有模组（包括未发布的）\n  getAllModules: async () => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    return mockResources;\n  },\n\n  // 根据用户权限过滤模组\n  getAuthorizedModules: async (userAuthorizedModules) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (!userAuthorizedModules || userAuthorizedModules.length === 0) {\n      return [];\n    }\n    return mockResources.filter(r => userAuthorizedModules.includes(r.id));\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getResources(params);\n      } else {\n        const response = await api.get('/game-resources', { params });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源分类\n  async getCategories() {\n    try {\n      if (MOCK_MODE) {\n        return [\n          { id: 'aircraft-jet', name: '喷气发动机飞机' },\n          { id: 'aircraft-prop', name: '活塞发动机飞机' },\n          { id: 'terrain', name: '地形' },\n          { id: 'campaign', name: '战役' }\n        ];\n      } else {\n        const response = await api.get('/game-resources/categories');\n        return response.categories || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return mockResources.find(r => r.id === resourceId);\n      } else {\n        const response = await api.get(`/game-resources/${resourceId}`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleInstall(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleEnable(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 检查更新\n  async checkUpdates() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.checkUpdates();\n      } else {\n        const response = await api.post('/game-resources/check-updates');\n        return response.updates || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/update`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.repairResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/repair`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: { q: query, ...filters }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 上传模组\n  async uploadModule(moduleData, file) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.uploadModule(moduleData);\n      } else {\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('data', JSON.stringify(moduleData));\n        const response = await api.post('/admin/modules/upload', formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        return response.module;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 更新模组\n  async updateModule(moduleId, moduleData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateModule(moduleId, moduleData);\n      } else {\n        const response = await api.put(`/admin/modules/${moduleId}`, moduleData);\n        return response.module;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 删除模组\n  async deleteModule(moduleId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteModule(moduleId);\n      } else {\n        const response = await api.delete(`/admin/modules/${moduleId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 获取所有模组\n  async getAllModules() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAllModules();\n      } else {\n        const response = await api.get('/admin/modules');\n        return response.modules || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 根据用户权限获取授权模组\n  async getAuthorizedModules(userAuthorizedModules) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAuthorizedModules(userAuthorizedModules);\n      } else {\n        const response = await api.post('/game-resources/authorized', {\n          authorizedModules: userAuthorizedModules\n        });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default gameResourceService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,SAAS,GAAG,IAAI;AACtB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EAAE;EAChBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,aAAa,GAAG,CAClB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE;AACV,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,sBAAsB;EAC5BC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE;AACV,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,KAAK;EACpBC,MAAM,EAAE;AACV,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE;AACV,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE;AACV,CAAC,CACF;;AAED;AACA,MAAMC,OAAO,GAAG;EACdC,YAAY,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IACnC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,IAAIE,QAAQ,GAAG,CAAC,GAAGrB,aAAa,CAAC;IAEjC,IAAIiB,MAAM,CAACd,QAAQ,IAAIc,MAAM,CAACd,QAAQ,KAAK,KAAK,EAAE;MAChDkB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,QAAQ,KAAKc,MAAM,CAACd,QAAQ,CAAC;IACjE;IAEA,IAAIc,MAAM,CAACO,MAAM,EAAE;MACjBH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAC1BA,CAAC,CAACrB,IAAI,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,MAAM,CAACO,MAAM,CAACC,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOJ,QAAQ;EACjB,CAAC;EAEDM,aAAa,EAAE,MAAOC,UAAU,IAAK;IACnC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAG7B,aAAa,CAAC8B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2B,UAAU,CAAC;IAC7D,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACxB,SAAS,GAAG,CAACwB,QAAQ,CAACxB,SAAS;MACxC,IAAI,CAACwB,QAAQ,CAACxB,SAAS,EAAE;QACvBwB,QAAQ,CAACvB,OAAO,GAAG,KAAK;MAC1B;IACF;IACA,OAAOuB,QAAQ;EACjB,CAAC;EAEDE,YAAY,EAAE,MAAOH,UAAU,IAAK;IAClC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAG7B,aAAa,CAAC8B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2B,UAAU,CAAC;IAC7D,IAAIC,QAAQ,IAAIA,QAAQ,CAACxB,SAAS,EAAE;MAClCwB,QAAQ,CAACvB,OAAO,GAAG,CAACuB,QAAQ,CAACvB,OAAO;IACtC;IACA,OAAOuB,QAAQ;EACjB,CAAC;EAEDG,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAM,IAAId,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD,OAAOnB,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,SAAS,CAAC;EAC/C,CAAC;EAED0B,cAAc,EAAE,MAAOL,UAAU,IAAK;IACpC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAG7B,aAAa,CAAC8B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2B,UAAU,CAAC;IAC7D,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACtB,SAAS,GAAG,KAAK;MAC1BsB,QAAQ,CAACrB,QAAQ,GAAG,KAAK;IAC3B;IACA,OAAOqB,QAAQ;EACjB,CAAC;EAEDK,cAAc,EAAE,MAAON,UAAU,IAAK;IACpC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAG7B,aAAa,CAAC8B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2B,UAAU,CAAC;IAC7D,OAAOC,QAAQ;EACjB,CAAC;EAED;EACAM,YAAY,EAAE,MAAOC,UAAU,IAAK;IAClC,MAAM,IAAIlB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD,MAAMkB,SAAS,GAAG;MAChBpC,EAAE,EAAED,aAAa,CAACsC,MAAM,GAAG,CAAC;MAC5BpC,IAAI,EAAEkC,UAAU,CAAClC,IAAI;MACrBC,QAAQ,EAAEiC,UAAU,CAACjC,QAAQ;MAC7BC,WAAW,EAAEgC,UAAU,CAAChC,WAAW;MACnCC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE2B,UAAU,CAAC3B,OAAO,IAAI,OAAO;MACtCC,IAAI,EAAE0B,UAAU,CAAC1B,IAAI,IAAI,MAAM;MAC/BC,MAAM,EAAEyB,UAAU,CAACzB,MAAM;MACzBC,UAAU,EAAE,IAAI2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAClD5B,aAAa,EAAE,CAAC;MAChBC,MAAM,EAAE;IACV,CAAC;IACDd,aAAa,CAAC0C,IAAI,CAACL,SAAS,CAAC;IAC7B,OAAOA,SAAS;EAClB,CAAC;EAED;EACAM,YAAY,EAAE,MAAAA,CAAOC,QAAQ,EAAER,UAAU,KAAK;IAC5C,MAAM,IAAIlB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAM0B,WAAW,GAAG7C,aAAa,CAAC8C,SAAS,CAACvB,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2C,QAAQ,CAAC;IACnE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,MAAM,IAAIE,KAAK,CAAC,OAAO,CAAC;IAC1B;IACA/C,aAAa,CAAC6C,WAAW,CAAC,GAAG;MAAE,GAAG7C,aAAa,CAAC6C,WAAW,CAAC;MAAE,GAAGT;IAAW,CAAC;IAC7E,OAAOpC,aAAa,CAAC6C,WAAW,CAAC;EACnC,CAAC;EAED;EACAG,YAAY,EAAE,MAAOJ,QAAQ,IAAK;IAChC,MAAM,IAAI1B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAM0B,WAAW,GAAG7C,aAAa,CAAC8C,SAAS,CAACvB,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2C,QAAQ,CAAC;IACnE,IAAIC,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,MAAM,IAAIE,KAAK,CAAC,OAAO,CAAC;IAC1B;IACA/C,aAAa,CAACiD,MAAM,CAACJ,WAAW,EAAE,CAAC,CAAC;IACpC,OAAO;MAAEK,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC;EAED;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,MAAM,IAAIjC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOnB,aAAa;EACtB,CAAC;EAED;EACAoD,oBAAoB,EAAE,MAAOC,qBAAqB,IAAK;IACrD,MAAM,IAAInC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,IAAI,CAACkC,qBAAqB,IAAIA,qBAAqB,CAACf,MAAM,KAAK,CAAC,EAAE;MAChE,OAAO,EAAE;IACX;IACA,OAAOtC,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAI8B,qBAAqB,CAAC3B,QAAQ,CAACH,CAAC,CAACtB,EAAE,CAAC,CAAC;EACxE;AACF,CAAC;;AAED;AACAN,GAAG,CAAC2D,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAAC1D,OAAO,CAAC8D,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAO5C,OAAO,CAAC6C,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAnE,GAAG,CAAC2D,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC1BQ,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACE,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOrD,OAAO,CAAC6C,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMU,mBAAmB,GAAG;EAC1B;EACA,MAAMxD,YAAYA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI;MACF,IAAIxB,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACC,YAAY,CAACC,MAAM,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM+C,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,iBAAiB,EAAE;UAAExD;QAAO,CAAC,CAAC;QAC7D,OAAO+C,QAAQ,CAACU,SAAS,IAAI,EAAE;MACjC;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMa,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,IAAIlF,SAAS,EAAE;QACb,OAAO,CACL;UAAEQ,EAAE,EAAE,cAAc;UAAEC,IAAI,EAAE;QAAU,CAAC,EACvC;UAAED,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE;QAAU,CAAC,EACxC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAK,CAAC,EAC7B;UAAED,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE;QAAK,CAAC,CAC/B;MACH,CAAC,MAAM;QACL,MAAM8D,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,4BAA4B,CAAC;QAC5D,OAAOT,QAAQ,CAACY,UAAU,IAAI,EAAE;MAClC;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMe,WAAWA,CAACjD,UAAU,EAAE;IAC5B,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAOO,aAAa,CAAC8B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAK2B,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,mBAAmB7C,UAAU,EAAE,CAAC;QAC/D,OAAOoC,QAAQ,CAACnC,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMnC,aAAaA,CAACC,UAAU,EAAE;IAC9B,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACY,aAAa,CAACC,UAAU,CAAC;MAChD,CAAC,MAAM;QACL,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,iBAAiB,CAAC;QAC/E,OAAOoC,QAAQ,CAACnC,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM/B,YAAYA,CAACH,UAAU,EAAE;IAC7B,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACgB,YAAY,CAACH,UAAU,CAAC;MAC/C,CAAC,MAAM;QACL,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,gBAAgB,CAAC;QAC9E,OAAOoC,QAAQ,CAACnC,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM9B,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,IAAIvC,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACiB,YAAY,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,MAAMgC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,+BAA+B,CAAC;QAChE,OAAOd,QAAQ,CAACe,OAAO,IAAI,EAAE;MAC/B;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM7B,cAAcA,CAACL,UAAU,EAAE;IAC/B,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACkB,cAAc,CAACL,UAAU,CAAC;MACjD,CAAC,MAAM;QACL,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,SAAS,CAAC;QACvE,OAAOoC,QAAQ,CAACnC,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM5B,cAAcA,CAACN,UAAU,EAAE;IAC/B,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACmB,cAAc,CAACN,UAAU,CAAC;MACjD,CAAC,MAAM;QACL,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,SAAS,CAAC;QACvE,OAAOoC,QAAQ,CAACnC,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkB,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,6BAA6B,CAAC;MAC9D,OAAOd,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMmB,cAAcA,CAACC,SAAS,EAAEC,WAAW,EAAE;IAC3C,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,uBAAuB,EAAE;QACvDI,SAAS;QACTC;MACF,CAAC,CAAC;MACF,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMsB,kBAAkBA,CAACxD,UAAU,EAAE;IACnC,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,mBAAmB7C,UAAU,WAAW,CAAC;MACxE,OAAOoC,QAAQ,CAACqB,QAAQ;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,aAAaA,CAAC1D,UAAU,EAAE;IAC9B,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,SAAS,CAAC;MACvE,OAAOoC,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMyB,eAAeA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,wBAAwB,EAAE;QACvDxD,MAAM,EAAE;UAAEyE,CAAC,EAAEF,KAAK;UAAE,GAAGC;QAAQ;MACjC,CAAC,CAAC;MACF,OAAOzB,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM6B,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,6BAA6B,CAAC;MAC7D,OAAOT,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM8B,SAASA,CAACC,KAAK,GAAG,EAAE,EAAE;IAC1B,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,wBAAwB,EAAE;QACvDxD,MAAM,EAAE;UAAE4E;QAAM;MAClB,CAAC,CAAC;MACF,OAAO7B,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMgC,UAAUA,CAACD,KAAK,GAAG,EAAE,EAAE;IAC3B,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,yBAAyB,EAAE;QACxDxD,MAAM,EAAE;UAAE4E;QAAM;MAClB,CAAC,CAAC;MACF,OAAO7B,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMiC,YAAYA,CAACnE,UAAU,EAAEd,MAAM,EAAEkF,OAAO,GAAG,EAAE,EAAE;IACnD,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,mBAAmBlD,UAAU,OAAO,EAAE;QACpEd,MAAM;QACNkF;MACF,CAAC,CAAC;MACF,OAAOhC,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMmC,kBAAkBA,CAACrE,UAAU,EAAE;IACnC,IAAI;MACF,MAAMoC,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,mBAAmB7C,UAAU,UAAU,CAAC;MACvE,OAAOoC,QAAQ,CAACkC,OAAO,IAAI,EAAE;IAC/B,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM3B,YAAYA,CAACC,UAAU,EAAE+D,IAAI,EAAE;IACnC,IAAI;MACF,IAAI1G,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACoB,YAAY,CAACC,UAAU,CAAC;MAC/C,CAAC,MAAM;QACL,MAAMgE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;QAC7BC,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACpE,UAAU,CAAC,CAAC;QACnD,MAAM4B,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,uBAAuB,EAAEsB,QAAQ,EAAE;UACjErG,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACF,OAAOiE,QAAQ,CAACyC,MAAM;MACxB;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMnB,YAAYA,CAACC,QAAQ,EAAER,UAAU,EAAE;IACvC,IAAI;MACF,IAAI3C,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAAC4B,YAAY,CAACC,QAAQ,EAAER,UAAU,CAAC;MACzD,CAAC,MAAM;QACL,MAAM4B,QAAQ,GAAG,MAAMrE,GAAG,CAAC+G,GAAG,CAAC,kBAAkB9D,QAAQ,EAAE,EAAER,UAAU,CAAC;QACxE,OAAO4B,QAAQ,CAACyC,MAAM;MACxB;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMd,YAAYA,CAACJ,QAAQ,EAAE;IAC3B,IAAI;MACF,IAAInD,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACiC,YAAY,CAACJ,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,MAAMoB,QAAQ,GAAG,MAAMrE,GAAG,CAACgH,MAAM,CAAC,kBAAkB/D,QAAQ,EAAE,CAAC;QAC/D,OAAOoB,QAAQ;MACjB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMX,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,IAAI1D,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACoC,aAAa,CAAC,CAAC;MACtC,CAAC,MAAM;QACL,MAAMa,QAAQ,GAAG,MAAMrE,GAAG,CAAC8E,GAAG,CAAC,gBAAgB,CAAC;QAChD,OAAOT,QAAQ,CAAC4C,OAAO,IAAI,EAAE;MAC/B;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMV,oBAAoBA,CAACC,qBAAqB,EAAE;IAChD,IAAI;MACF,IAAI5D,SAAS,EAAE;QACb,OAAO,MAAMsB,OAAO,CAACqC,oBAAoB,CAACC,qBAAqB,CAAC;MAClE,CAAC,MAAM;QACL,MAAMW,QAAQ,GAAG,MAAMrE,GAAG,CAACmF,IAAI,CAAC,4BAA4B,EAAE;UAC5D+B,iBAAiB,EAAExD;QACrB,CAAC,CAAC;QACF,OAAOW,QAAQ,CAACU,SAAS,IAAI,EAAE;MACjC;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeU,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}