{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nconst underDampedSpring = {\n  type: \"spring\",\n  stiffness: 500,\n  damping: 25,\n  restSpeed: 10\n};\nconst criticallyDampedSpring = target => ({\n  type: \"spring\",\n  stiffness: 550,\n  damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n  restSpeed: 10\n});\nconst keyframesTransition = {\n  type: \"keyframes\",\n  duration: 0.8\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n  type: \"keyframes\",\n  ease: [0.25, 0.1, 0.35, 1],\n  duration: 0.3\n};\nconst getDefaultTransition = (valueKey, {\n  keyframes\n}) => {\n  if (keyframes.length > 2) {\n    return keyframesTransition;\n  } else if (transformProps.has(valueKey)) {\n    return valueKey.startsWith(\"scale\") ? criticallyDampedSpring(keyframes[1]) : underDampedSpring;\n  }\n  return ease;\n};\nexport { getDefaultTransition };", "map": {"version": 3, "names": ["transformProps", "underDampedSpring", "type", "stiffness", "damping", "restSpeed", "criticallyDampedSpring", "target", "Math", "sqrt", "keyframesTransition", "duration", "ease", "getDefaultTransition", "valueKey", "keyframes", "length", "has", "startsWith"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\nexport { getDefaultTransition };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AAEtE,MAAMC,iBAAiB,GAAG;EACtBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAE,EAAE;EACXC,SAAS,EAAE;AACf,CAAC;AACD,MAAMC,sBAAsB,GAAIC,MAAM,KAAM;EACxCL,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,GAAG;EACdC,OAAO,EAAEG,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;EAC/CJ,SAAS,EAAE;AACf,CAAC,CAAC;AACF,MAAMK,mBAAmB,GAAG;EACxBR,IAAI,EAAE,WAAW;EACjBS,QAAQ,EAAE;AACd,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAG;EACTV,IAAI,EAAE,WAAW;EACjBU,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;EAC1BD,QAAQ,EAAE;AACd,CAAC;AACD,MAAME,oBAAoB,GAAGA,CAACC,QAAQ,EAAE;EAAEC;AAAU,CAAC,KAAK;EACtD,IAAIA,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACtB,OAAON,mBAAmB;EAC9B,CAAC,MACI,IAAIV,cAAc,CAACiB,GAAG,CAACH,QAAQ,CAAC,EAAE;IACnC,OAAOA,QAAQ,CAACI,UAAU,CAAC,OAAO,CAAC,GAC7BZ,sBAAsB,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC,GACpCd,iBAAiB;EAC3B;EACA,OAAOW,IAAI;AACf,CAAC;AAED,SAASC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}