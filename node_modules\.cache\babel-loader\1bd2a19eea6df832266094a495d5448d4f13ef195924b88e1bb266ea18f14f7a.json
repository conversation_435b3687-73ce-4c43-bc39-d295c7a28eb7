{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\pages\\\\admin\\\\UserManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../context/AuthContext';\nimport authService from '../../services/authService';\nimport gameResourceService from '../../services/gameResourceService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  padding: 24px;\n  max-width: 1400px;\n  margin: 0 auto;\n  color: #fff;\n  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.6) 100%);\n  min-height: 100vh;\n  backdrop-filter: blur(10px);\n`;\n_c = Container;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n  padding: 24px;\n  background: rgba(255, 107, 53, 0.05);\n  border: 1px solid rgba(255, 107, 53, 0.2);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: 32px;\n  font-weight: 700;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff9d75 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n  text-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n`;\n_c3 = Title;\nconst Button = styled.button`\n  background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)' : 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)'};\n  color: white;\n  border: none;\n  padding: 14px 28px;\n  border-radius: 12px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);\n\n  &:hover {\n    background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)' : 'linear-gradient(135deg, #ff8555 0%, #ff9d75 100%)'};\n    transform: translateY(-3px);\n    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c4 = Button;\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  background: rgba(45, 45, 45, 0.9);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c5 = Table;\nconst Th = styled.th`\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  color: white;\n  padding: 18px 20px;\n  text-align: left;\n  font-weight: 700;\n  font-size: 15px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  border-bottom: 2px solid rgba(255, 107, 53, 0.3);\n`;\n_c6 = Th;\nconst Td = styled.td`\n  padding: 18px 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  color: #fff;\n  font-size: 14px;\n  transition: background 0.3s ease;\n  \n  tr:hover & {\n    background: rgba(255, 107, 53, 0.05);\n  }\n`;\n_c7 = Td;\nconst Tr = styled.tr`\n  &:hover {\n    background: ${props => props.theme.colors.hover};\n  }\n`;\n_c8 = Tr;\nconst Modal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(8px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: fadeIn 0.3s ease;\n  \n  @keyframes fadeIn {\n    from { opacity: 0; }\n    to { opacity: 1; }\n  }\n`;\n_c9 = Modal;\nconst ModalContent = styled.div`\n  background: rgba(26, 26, 26, 0.95);\n  backdrop-filter: blur(20px);\n  padding: 32px;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 520px;\n  max-height: 80vh;\n  overflow-y: auto;\n  border: 1px solid rgba(255, 107, 53, 0.3);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n  animation: slideUp 0.3s ease;\n  \n  @keyframes slideUp {\n    from {\n      opacity: 0;\n      transform: translateY(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n_c0 = ModalContent;\nconst FormGroup = styled.div`\n  margin-bottom: 24px;\n`;\n_c1 = FormGroup;\nconst Label = styled.label`\n  display: block;\n  margin-bottom: 10px;\n  font-weight: 600;\n  color: #fff;\n  font-size: 15px;\n  letter-spacing: 0.3px;\n`;\n_c10 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 14px 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  background: rgba(45, 45, 45, 0.8);\n  color: #fff;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  \n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);\n    background: rgba(55, 55, 55, 0.9);\n  }\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n`;\n_c11 = Input;\nconst Select = styled.select`\n  width: 100%;\n  padding: 14px 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  background: rgba(45, 45, 45, 0.8);\n  color: #fff;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  cursor: pointer;\n  \n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);\n    background: rgba(55, 55, 55, 0.9);\n  }\n  \n  option {\n    background: #2d2d2d;\n    color: #fff;\n  }\n`;\n_c12 = Select;\nconst CheckboxGroup = styled.div`\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: 5px;\n  padding: 10px;\n`;\n_c13 = CheckboxGroup;\nconst CheckboxItem = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n\n  input {\n    margin-right: 10px;\n  }\n\n  label {\n    margin: 0;\n    cursor: pointer;\n  }\n`;\n_c14 = CheckboxItem;\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n_c15 = ButtonGroup;\nconst ErrorMessage = styled.div`\n  color: ${props => props.theme.colors.error};\n  margin-bottom: 15px;\n  padding: 10px;\n  background: rgba(244, 67, 54, 0.1);\n  border-radius: 5px;\n`;\n_c16 = ErrorMessage;\nconst UserManagement = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [users, setUsers] = useState([]);\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    role: 'user',\n    authorizedModules: []\n  });\n  useEffect(() => {\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'admin') {\n      loadData();\n    }\n  }, [user]);\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [usersData, modulesData] = await Promise.all([authService.getAllUsers(), gameResourceService.getAllModules()]);\n      setUsers(usersData);\n      setModules(modulesData);\n    } catch (err) {\n      setError('加载数据失败: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      role: 'user',\n      authorizedModules: []\n    });\n    setShowModal(true);\n  };\n  const handleEditUser = userToEdit => {\n    setEditingUser(userToEdit);\n    setFormData({\n      username: userToEdit.username,\n      email: userToEdit.email,\n      password: '',\n      role: userToEdit.role,\n      authorizedModules: userToEdit.authorizedModules || []\n    });\n    setShowModal(true);\n  };\n  const handleDeleteUser = async userId => {\n    if (window.confirm('确定要删除这个用户吗？')) {\n      try {\n        await authService.deleteUser(userId);\n        await loadData();\n      } catch (err) {\n        setError('删除用户失败: ' + err.message);\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      if (editingUser) {\n        const updateData = {\n          ...formData\n        };\n        if (!updateData.password) {\n          delete updateData.password;\n        }\n        await authService.updateUser(editingUser.id, updateData);\n      } else {\n        await authService.createUser(formData);\n      }\n      setShowModal(false);\n      await loadData();\n    } catch (err) {\n      setError('保存用户失败: ' + err.message);\n    }\n  };\n  const handleModuleToggle = moduleId => {\n    setFormData(prev => ({\n      ...prev,\n      authorizedModules: prev.authorizedModules.includes(moduleId) ? prev.authorizedModules.filter(id => id !== moduleId) : [...prev.authorizedModules, moduleId]\n    }));\n  };\n  if ((user === null || user === void 0 ? void 0 : user.role) !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: \"\\u60A8\\u6CA1\\u6709\\u6743\\u9650\\u8BBF\\u95EE\\u6B64\\u9875\\u9762\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCreateUser,\n        children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(Th, {\n            children: \"ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Th, {\n            children: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Th, {\n            children: \"\\u90AE\\u7BB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Th, {\n            children: \"\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Th, {\n            children: \"\\u6388\\u6743\\u6A21\\u7EC4\\u6570\\u91CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Th, {\n            children: \"\\u64CD\\u4F5C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: users.map(userItem => {\n          var _userItem$authorizedM;\n          return /*#__PURE__*/_jsxDEV(Tr, {\n            children: [/*#__PURE__*/_jsxDEV(Td, {\n              children: userItem.id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Td, {\n              children: userItem.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Td, {\n              children: userItem.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Td, {\n              children: userItem.role === 'admin' ? '管理员' : '普通用户'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Td, {\n              children: ((_userItem$authorizedM = userItem.authorizedModules) === null || _userItem$authorizedM === void 0 ? void 0 : _userItem$authorizedM.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Td, {\n              children: /*#__PURE__*/_jsxDEV(ButtonGroup, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => handleEditUser(userItem),\n                  children: \"\\u7F16\\u8F91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), userItem.role !== 'admin' && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"danger\",\n                  onClick: () => handleDeleteUser(userItem.id),\n                  children: \"\\u5220\\u9664\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, userItem.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), showModal && /*#__PURE__*/_jsxDEV(Modal, {\n      onClick: () => setShowModal(false),\n      children: /*#__PURE__*/_jsxDEV(ModalContent, {\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: editingUser ? '编辑用户' : '添加用户'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"\\u7528\\u6237\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              value: formData.username,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                username: e.target.value\n              })),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"\\u90AE\\u7BB1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"email\",\n              value: formData.email,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                email: e.target.value\n              })),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: [\"\\u5BC6\\u7801 \", editingUser && '(留空表示不修改)']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"password\",\n              value: formData.password,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                password: e.target.value\n              })),\n              required: !editingUser\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"\\u89D2\\u8272\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.role,\n              onChange: e => setFormData(prev => ({\n                ...prev,\n                role: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"user\",\n                children: \"\\u666E\\u901A\\u7528\\u6237\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"admin\",\n                children: \"\\u7BA1\\u7406\\u5458\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), formData.role === 'user' && /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              children: \"\\u6388\\u6743\\u6A21\\u7EC4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CheckboxGroup, {\n              children: modules.map(module => /*#__PURE__*/_jsxDEV(CheckboxItem, {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: formData.authorizedModules.includes(module.id),\n                  onChange: () => handleModuleToggle(module.id)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  children: module.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 25\n                }, this)]\n              }, module.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              onClick: () => setShowModal(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              children: editingUser ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 373,\n    columnNumber: 5\n  }, this);\n};\n_s(UserManagement, \"nFAQ9UR/eefFAid5SIdmUcR+8Yg=\", false, function () {\n  return [useAuth];\n});\n_c17 = UserManagement;\nexport default UserManagement;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"Button\");\n$RefreshReg$(_c5, \"Table\");\n$RefreshReg$(_c6, \"Th\");\n$RefreshReg$(_c7, \"Td\");\n$RefreshReg$(_c8, \"Tr\");\n$RefreshReg$(_c9, \"Modal\");\n$RefreshReg$(_c0, \"ModalContent\");\n$RefreshReg$(_c1, \"FormGroup\");\n$RefreshReg$(_c10, \"Label\");\n$RefreshReg$(_c11, \"Input\");\n$RefreshReg$(_c12, \"Select\");\n$RefreshReg$(_c13, \"CheckboxGroup\");\n$RefreshReg$(_c14, \"CheckboxItem\");\n$RefreshReg$(_c15, \"ButtonGroup\");\n$RefreshReg$(_c16, \"ErrorMessage\");\n$RefreshReg$(_c17, \"UserManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useAuth", "authService", "gameResourceService", "jsxDEV", "_jsxDEV", "Container", "div", "_c", "Header", "_c2", "Title", "h1", "_c3", "<PERSON><PERSON>", "button", "props", "variant", "_c4", "Table", "table", "_c5", "Th", "th", "_c6", "Td", "td", "_c7", "Tr", "tr", "theme", "colors", "hover", "_c8", "Modal", "_c9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c0", "FormGroup", "_c1", "Label", "label", "_c10", "Input", "input", "_c11", "Select", "select", "_c12", "CheckboxGroup", "border", "_c13", "CheckboxItem", "_c14", "ButtonGroup", "_c15", "ErrorMessage", "error", "_c16", "UserManagement", "_s", "user", "users", "setUsers", "modules", "setModules", "loading", "setLoading", "setError", "showModal", "setShowModal", "editingUser", "setEditingUser", "formData", "setFormData", "username", "email", "password", "role", "authorizedModules", "loadData", "usersData", "modulesData", "Promise", "all", "getAllUsers", "getAllModules", "err", "message", "handleCreateUser", "handleEditUser", "userToEdit", "handleDeleteUser", "userId", "window", "confirm", "deleteUser", "handleSubmit", "e", "preventDefault", "updateData", "updateUser", "id", "createUser", "handleModuleToggle", "moduleId", "prev", "includes", "filter", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "userItem", "_userItem$authorizedM", "length", "stopPropagation", "onSubmit", "type", "value", "onChange", "target", "required", "module", "checked", "name", "_c17", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/pages/admin/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../context/AuthContext';\nimport authService from '../../services/authService';\nimport gameResourceService from '../../services/gameResourceService';\n\nconst Container = styled.div`\n  padding: 24px;\n  max-width: 1400px;\n  margin: 0 auto;\n  color: #fff;\n  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.6) 100%);\n  min-height: 100vh;\n  backdrop-filter: blur(10px);\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n  padding: 24px;\n  background: rgba(255, 107, 53, 0.05);\n  border: 1px solid rgba(255, 107, 53, 0.2);\n  border-radius: 16px;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n`;\n\nconst Title = styled.h1`\n  font-size: 32px;\n  font-weight: 700;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff9d75 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n  text-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n`;\n\nconst Button = styled.button`\n  background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)' : 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)'};\n  color: white;\n  border: none;\n  padding: 14px 28px;\n  border-radius: 12px;\n  cursor: pointer;\n  font-size: 16px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);\n\n  &:hover {\n    background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)' : 'linear-gradient(135deg, #ff8555 0%, #ff9d75 100%)'};\n    transform: translateY(-3px);\n    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);\n  }\n\n  &:active {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  background: rgba(45, 45, 45, 0.9);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Th = styled.th`\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  color: white;\n  padding: 18px 20px;\n  text-align: left;\n  font-weight: 700;\n  font-size: 15px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  border-bottom: 2px solid rgba(255, 107, 53, 0.3);\n`;\n\nconst Td = styled.td`\n  padding: 18px 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  color: #fff;\n  font-size: 14px;\n  transition: background 0.3s ease;\n  \n  tr:hover & {\n    background: rgba(255, 107, 53, 0.05);\n  }\n`;\n\nconst Tr = styled.tr`\n  &:hover {\n    background: ${props => props.theme.colors.hover};\n  }\n`;\n\nconst Modal = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(8px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  animation: fadeIn 0.3s ease;\n  \n  @keyframes fadeIn {\n    from { opacity: 0; }\n    to { opacity: 1; }\n  }\n`;\n\nconst ModalContent = styled.div`\n  background: rgba(26, 26, 26, 0.95);\n  backdrop-filter: blur(20px);\n  padding: 32px;\n  border-radius: 20px;\n  width: 90%;\n  max-width: 520px;\n  max-height: 80vh;\n  overflow-y: auto;\n  border: 1px solid rgba(255, 107, 53, 0.3);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n  animation: slideUp 0.3s ease;\n  \n  @keyframes slideUp {\n    from {\n      opacity: 0;\n      transform: translateY(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst FormGroup = styled.div`\n  margin-bottom: 24px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  margin-bottom: 10px;\n  font-weight: 600;\n  color: #fff;\n  font-size: 15px;\n  letter-spacing: 0.3px;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 14px 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  background: rgba(45, 45, 45, 0.8);\n  color: #fff;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  \n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);\n    background: rgba(55, 55, 55, 0.9);\n  }\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.5);\n  }\n`;\n\nconst Select = styled.select`\n  width: 100%;\n  padding: 14px 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 10px;\n  background: rgba(45, 45, 45, 0.8);\n  color: #fff;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  cursor: pointer;\n  \n  &:focus {\n    outline: none;\n    border-color: #ff6b35;\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);\n    background: rgba(55, 55, 55, 0.9);\n  }\n  \n  option {\n    background: #2d2d2d;\n    color: #fff;\n  }\n`;\n\nconst CheckboxGroup = styled.div`\n  max-height: 200px;\n  overflow-y: auto;\n  border: 1px solid ${props => props.theme.colors.border};\n  border-radius: 5px;\n  padding: 10px;\n`;\n\nconst CheckboxItem = styled.div`\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n\n  input {\n    margin-right: 10px;\n  }\n\n  label {\n    margin: 0;\n    cursor: pointer;\n  }\n`;\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  gap: 10px;\n  justify-content: flex-end;\n  margin-top: 20px;\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${props => props.theme.colors.error};\n  margin-bottom: 15px;\n  padding: 10px;\n  background: rgba(244, 67, 54, 0.1);\n  border-radius: 5px;\n`;\n\nconst UserManagement = () => {\n  const { user } = useAuth();\n  const [users, setUsers] = useState([]);\n  const [modules, setModules] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showModal, setShowModal] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    role: 'user',\n    authorizedModules: []\n  });\n\n  useEffect(() => {\n    if (user?.role === 'admin') {\n      loadData();\n    }\n  }, [user]);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [usersData, modulesData] = await Promise.all([\n        authService.getAllUsers(),\n        gameResourceService.getAllModules()\n      ]);\n      setUsers(usersData);\n      setModules(modulesData);\n    } catch (err) {\n      setError('加载数据失败: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      role: 'user',\n      authorizedModules: []\n    });\n    setShowModal(true);\n  };\n\n  const handleEditUser = (userToEdit) => {\n    setEditingUser(userToEdit);\n    setFormData({\n      username: userToEdit.username,\n      email: userToEdit.email,\n      password: '',\n      role: userToEdit.role,\n      authorizedModules: userToEdit.authorizedModules || []\n    });\n    setShowModal(true);\n  };\n\n  const handleDeleteUser = async (userId) => {\n    if (window.confirm('确定要删除这个用户吗？')) {\n      try {\n        await authService.deleteUser(userId);\n        await loadData();\n      } catch (err) {\n        setError('删除用户失败: ' + err.message);\n      }\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (editingUser) {\n        const updateData = { ...formData };\n        if (!updateData.password) {\n          delete updateData.password;\n        }\n        await authService.updateUser(editingUser.id, updateData);\n      } else {\n        await authService.createUser(formData);\n      }\n      setShowModal(false);\n      await loadData();\n    } catch (err) {\n      setError('保存用户失败: ' + err.message);\n    }\n  };\n\n  const handleModuleToggle = (moduleId) => {\n    setFormData(prev => ({\n      ...prev,\n      authorizedModules: prev.authorizedModules.includes(moduleId)\n        ? prev.authorizedModules.filter(id => id !== moduleId)\n        : [...prev.authorizedModules, moduleId]\n    }));\n  };\n\n  if (user?.role !== 'admin') {\n    return (\n      <Container>\n        <ErrorMessage>您没有权限访问此页面</ErrorMessage>\n      </Container>\n    );\n  }\n\n  if (loading) {\n    return (\n      <Container>\n        <div>加载中...</div>\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      <Header>\n        <Title>用户管理</Title>\n        <Button onClick={handleCreateUser}>添加用户</Button>\n      </Header>\n\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n\n      <Table>\n        <thead>\n          <tr>\n            <Th>ID</Th>\n            <Th>用户名</Th>\n            <Th>邮箱</Th>\n            <Th>角色</Th>\n            <Th>授权模组数量</Th>\n            <Th>操作</Th>\n          </tr>\n        </thead>\n        <tbody>\n          {users.map(userItem => (\n            <Tr key={userItem.id}>\n              <Td>{userItem.id}</Td>\n              <Td>{userItem.username}</Td>\n              <Td>{userItem.email}</Td>\n              <Td>{userItem.role === 'admin' ? '管理员' : '普通用户'}</Td>\n              <Td>{userItem.authorizedModules?.length || 0}</Td>\n              <Td>\n                <ButtonGroup>\n                  <Button onClick={() => handleEditUser(userItem)}>编辑</Button>\n                  {userItem.role !== 'admin' && (\n                    <Button \n                      variant=\"danger\" \n                      onClick={() => handleDeleteUser(userItem.id)}\n                    >\n                      删除\n                    </Button>\n                  )}\n                </ButtonGroup>\n              </Td>\n            </Tr>\n          ))}\n        </tbody>\n      </Table>\n\n      {showModal && (\n        <Modal onClick={() => setShowModal(false)}>\n          <ModalContent onClick={(e) => e.stopPropagation()}>\n            <h2>{editingUser ? '编辑用户' : '添加用户'}</h2>\n            <form onSubmit={handleSubmit}>\n              <FormGroup>\n                <Label>用户名</Label>\n                <Input\n                  type=\"text\"\n                  value={formData.username}\n                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}\n                  required\n                />\n              </FormGroup>\n\n              <FormGroup>\n                <Label>邮箱</Label>\n                <Input\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                  required\n                />\n              </FormGroup>\n\n              <FormGroup>\n                <Label>密码 {editingUser && '(留空表示不修改)'}</Label>\n                <Input\n                  type=\"password\"\n                  value={formData.password}\n                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}\n                  required={!editingUser}\n                />\n              </FormGroup>\n\n              <FormGroup>\n                <Label>角色</Label>\n                <Select\n                  value={formData.role}\n                  onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}\n                >\n                  <option value=\"user\">普通用户</option>\n                  <option value=\"admin\">管理员</option>\n                </Select>\n              </FormGroup>\n\n              {formData.role === 'user' && (\n                <FormGroup>\n                  <Label>授权模组</Label>\n                  <CheckboxGroup>\n                    {modules.map(module => (\n                      <CheckboxItem key={module.id}>\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.authorizedModules.includes(module.id)}\n                          onChange={() => handleModuleToggle(module.id)}\n                        />\n                        <label>{module.name}</label>\n                      </CheckboxItem>\n                    ))}\n                  </CheckboxGroup>\n                </FormGroup>\n              )}\n\n              <ButtonGroup>\n                <Button type=\"button\" onClick={() => setShowModal(false)}>取消</Button>\n                <Button type=\"submit\">{editingUser ? '更新' : '创建'}</Button>\n              </ButtonGroup>\n            </form>\n          </ModalContent>\n        </Modal>\n      )}\n    </Container>\n  );\n};\n\nexport default UserManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAS,GAAGN,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,SAAS;AAUf,MAAMG,MAAM,GAAGT,MAAM,CAACO,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,MAAM;AAaZ,MAAME,KAAK,GAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,KAAK;AAWX,MAAMG,MAAM,GAAGd,MAAM,CAACe,MAAM;AAC5B,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,QAAQ,GAAG,mDAAmD,GAAG,mDAAmD;AAC/J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBD,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,QAAQ,GAAG,mDAAmD,GAAG,mDAAmD;AACjK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA3BIJ,MAAM;AA6BZ,MAAMK,KAAK,GAAGnB,MAAM,CAACoB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,KAAK;AAWX,MAAMG,EAAE,GAAGtB,MAAM,CAACuB,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,EAAE;AAYR,MAAMG,EAAE,GAAGzB,MAAM,CAAC0B,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIF,EAAE;AAYR,MAAMG,EAAE,GAAG5B,MAAM,CAAC6B,EAAE;AACpB;AACA,kBAAkBb,KAAK,IAAIA,KAAK,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK;AACnD;AACA,CAAC;AAACC,GAAA,GAJIL,EAAE;AAMR,MAAMM,KAAK,GAAGlC,MAAM,CAACO,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4B,GAAA,GAlBID,KAAK;AAoBX,MAAME,YAAY,GAAGpC,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,GAAA,GAvBID,YAAY;AAyBlB,MAAME,SAAS,GAAGtC,MAAM,CAACO,GAAG;AAC5B;AACA,CAAC;AAACgC,GAAA,GAFID,SAAS;AAIf,MAAME,KAAK,GAAGxC,MAAM,CAACyC,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,KAAK,GAAG3C,MAAM,CAAC4C,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GArBIF,KAAK;AAuBX,MAAMG,MAAM,GAAG9C,MAAM,CAAC+C,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAvBIF,MAAM;AAyBZ,MAAMG,aAAa,GAAGjD,MAAM,CAACO,GAAG;AAChC;AACA;AACA,sBAAsBS,KAAK,IAAIA,KAAK,CAACc,KAAK,CAACC,MAAM,CAACmB,MAAM;AACxD;AACA;AACA,CAAC;AAACC,IAAA,GANIF,aAAa;AAQnB,MAAMG,YAAY,GAAGpD,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GAbID,YAAY;AAelB,MAAME,WAAW,GAAGtD,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GALID,WAAW;AAOjB,MAAME,YAAY,GAAGxD,MAAM,CAACO,GAAG;AAC/B,WAAWS,KAAK,IAAIA,KAAK,CAACc,KAAK,CAACC,MAAM,CAAC0B,KAAK;AAC5C;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANIF,YAAY;AAQlB,MAAMG,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAK,CAAC,GAAG5D,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,KAAK,EAAEW,QAAQ,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEFhF,SAAS,CAAC,MAAM;IACd,IAAI,CAAA8D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,MAAK,OAAO,EAAE;MAC1BE,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACnB,IAAI,CAAC,CAAC;EAEV,MAAMmB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACc,SAAS,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDlF,WAAW,CAACmF,WAAW,CAAC,CAAC,EACzBlF,mBAAmB,CAACmF,aAAa,CAAC,CAAC,CACpC,CAAC;MACFvB,QAAQ,CAACkB,SAAS,CAAC;MACnBhB,UAAU,CAACiB,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZnB,QAAQ,CAAC,UAAU,GAAGmB,GAAG,CAACC,OAAO,CAAC;IACpC,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjB,cAAc,CAAC,IAAI,CAAC;IACpBE,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,MAAM;MACZC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFT,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMoB,cAAc,GAAIC,UAAU,IAAK;IACrCnB,cAAc,CAACmB,UAAU,CAAC;IAC1BjB,WAAW,CAAC;MACVC,QAAQ,EAAEgB,UAAU,CAAChB,QAAQ;MAC7BC,KAAK,EAAEe,UAAU,CAACf,KAAK;MACvBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAEa,UAAU,CAACb,IAAI;MACrBC,iBAAiB,EAAEY,UAAU,CAACZ,iBAAiB,IAAI;IACrD,CAAC,CAAC;IACFT,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsB,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAIC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACjC,IAAI;QACF,MAAM7F,WAAW,CAAC8F,UAAU,CAACH,MAAM,CAAC;QACpC,MAAMb,QAAQ,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOO,GAAG,EAAE;QACZnB,QAAQ,CAAC,UAAU,GAAGmB,GAAG,CAACC,OAAO,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAI5B,WAAW,EAAE;QACf,MAAM6B,UAAU,GAAG;UAAE,GAAG3B;QAAS,CAAC;QAClC,IAAI,CAAC2B,UAAU,CAACvB,QAAQ,EAAE;UACxB,OAAOuB,UAAU,CAACvB,QAAQ;QAC5B;QACA,MAAM3E,WAAW,CAACmG,UAAU,CAAC9B,WAAW,CAAC+B,EAAE,EAAEF,UAAU,CAAC;MAC1D,CAAC,MAAM;QACL,MAAMlG,WAAW,CAACqG,UAAU,CAAC9B,QAAQ,CAAC;MACxC;MACAH,YAAY,CAAC,KAAK,CAAC;MACnB,MAAMU,QAAQ,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZnB,QAAQ,CAAC,UAAU,GAAGmB,GAAG,CAACC,OAAO,CAAC;IACpC;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAIC,QAAQ,IAAK;IACvC/B,WAAW,CAACgC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3B,iBAAiB,EAAE2B,IAAI,CAAC3B,iBAAiB,CAAC4B,QAAQ,CAACF,QAAQ,CAAC,GACxDC,IAAI,CAAC3B,iBAAiB,CAAC6B,MAAM,CAACN,EAAE,IAAIA,EAAE,KAAKG,QAAQ,CAAC,GACpD,CAAC,GAAGC,IAAI,CAAC3B,iBAAiB,EAAE0B,QAAQ;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,MAAK,OAAO,EAAE;IAC1B,oBACEzE,OAAA,CAACC,SAAS;MAAAuG,QAAA,eACRxG,OAAA,CAACmD,YAAY;QAAAqD,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEhB;EAEA,IAAI/C,OAAO,EAAE;IACX,oBACE7D,OAAA,CAACC,SAAS;MAAAuG,QAAA,eACRxG,OAAA;QAAAwG,QAAA,EAAK;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEhB;EAEA,oBACE5G,OAAA,CAACC,SAAS;IAAAuG,QAAA,gBACRxG,OAAA,CAACI,MAAM;MAAAoG,QAAA,gBACLxG,OAAA,CAACM,KAAK;QAAAkG,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnB5G,OAAA,CAACS,MAAM;QAACoG,OAAO,EAAEzB,gBAAiB;QAAAoB,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAERxD,KAAK,iBAAIpD,OAAA,CAACmD,YAAY;MAAAqD,QAAA,EAAEpD;IAAK;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAe,CAAC,eAE9C5G,OAAA,CAACc,KAAK;MAAA0F,QAAA,gBACJxG,OAAA;QAAAwG,QAAA,eACExG,OAAA;UAAAwG,QAAA,gBACExG,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX5G,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACZ5G,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX5G,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACX5G,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACf5G,OAAA,CAACiB,EAAE;YAAAuF,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACR5G,OAAA;QAAAwG,QAAA,EACG/C,KAAK,CAACqD,GAAG,CAACC,QAAQ;UAAA,IAAAC,qBAAA;UAAA,oBACjBhH,OAAA,CAACuB,EAAE;YAAAiF,QAAA,gBACDxG,OAAA,CAACoB,EAAE;cAAAoF,QAAA,EAAEO,QAAQ,CAACd;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtB5G,OAAA,CAACoB,EAAE;cAAAoF,QAAA,EAAEO,QAAQ,CAACzC;YAAQ;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5B5G,OAAA,CAACoB,EAAE;cAAAoF,QAAA,EAAEO,QAAQ,CAACxC;YAAK;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzB5G,OAAA,CAACoB,EAAE;cAAAoF,QAAA,EAAEO,QAAQ,CAACtC,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG;YAAM;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrD5G,OAAA,CAACoB,EAAE;cAAAoF,QAAA,EAAE,EAAAQ,qBAAA,GAAAD,QAAQ,CAACrC,iBAAiB,cAAAsC,qBAAA,uBAA1BA,qBAAA,CAA4BC,MAAM,KAAI;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD5G,OAAA,CAACoB,EAAE;cAAAoF,QAAA,eACDxG,OAAA,CAACiD,WAAW;gBAAAuD,QAAA,gBACVxG,OAAA,CAACS,MAAM;kBAACoG,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAAC0B,QAAQ,CAAE;kBAAAP,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3DG,QAAQ,CAACtC,IAAI,KAAK,OAAO,iBACxBzE,OAAA,CAACS,MAAM;kBACLG,OAAO,EAAC,QAAQ;kBAChBiG,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAACwB,QAAQ,CAACd,EAAE,CAAE;kBAAAO,QAAA,EAC9C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA,GAlBEG,QAAQ,CAACd,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBhB,CAAC;QAAA,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEP5C,SAAS,iBACRhE,OAAA,CAAC6B,KAAK;MAACgF,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,KAAK,CAAE;MAAAuC,QAAA,eACxCxG,OAAA,CAAC+B,YAAY;QAAC8E,OAAO,EAAGhB,CAAC,IAAKA,CAAC,CAACqB,eAAe,CAAC,CAAE;QAAAV,QAAA,gBAChDxG,OAAA;UAAAwG,QAAA,EAAKtC,WAAW,GAAG,MAAM,GAAG;QAAM;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxC5G,OAAA;UAAMmH,QAAQ,EAAEvB,YAAa;UAAAY,QAAA,gBAC3BxG,OAAA,CAACiC,SAAS;YAAAuE,QAAA,gBACRxG,OAAA,CAACmC,KAAK;cAAAqE,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClB5G,OAAA,CAACsC,KAAK;cACJ8E,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEjD,QAAQ,CAACE,QAAS;cACzBgD,QAAQ,EAAGzB,CAAC,IAAKxB,WAAW,CAACgC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE/B,QAAQ,EAAEuB,CAAC,CAAC0B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAC9EG,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ5G,OAAA,CAACiC,SAAS;YAAAuE,QAAA,gBACRxG,OAAA,CAACmC,KAAK;cAAAqE,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjB5G,OAAA,CAACsC,KAAK;cACJ8E,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEjD,QAAQ,CAACG,KAAM;cACtB+C,QAAQ,EAAGzB,CAAC,IAAKxB,WAAW,CAACgC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9B,KAAK,EAAEsB,CAAC,CAAC0B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAC3EG,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ5G,OAAA,CAACiC,SAAS;YAAAuE,QAAA,gBACRxG,OAAA,CAACmC,KAAK;cAAAqE,QAAA,GAAC,eAAG,EAACtC,WAAW,IAAI,WAAW;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C5G,OAAA,CAACsC,KAAK;cACJ8E,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEjD,QAAQ,CAACI,QAAS;cACzB8C,QAAQ,EAAGzB,CAAC,IAAKxB,WAAW,CAACgC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7B,QAAQ,EAAEqB,CAAC,CAAC0B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAC9EG,QAAQ,EAAE,CAACtD;YAAY;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ5G,OAAA,CAACiC,SAAS;YAAAuE,QAAA,gBACRxG,OAAA,CAACmC,KAAK;cAAAqE,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjB5G,OAAA,CAACyC,MAAM;cACL4E,KAAK,EAAEjD,QAAQ,CAACK,IAAK;cACrB6C,QAAQ,EAAGzB,CAAC,IAAKxB,WAAW,CAACgC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE5B,IAAI,EAAEoB,CAAC,CAAC0B,MAAM,CAACF;cAAM,CAAC,CAAC,CAAE;cAAAb,QAAA,gBAE1ExG,OAAA;gBAAQqH,KAAK,EAAC,MAAM;gBAAAb,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC5G,OAAA;gBAAQqH,KAAK,EAAC,OAAO;gBAAAb,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAEXxC,QAAQ,CAACK,IAAI,KAAK,MAAM,iBACvBzE,OAAA,CAACiC,SAAS;YAAAuE,QAAA,gBACRxG,OAAA,CAACmC,KAAK;cAAAqE,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnB5G,OAAA,CAAC4C,aAAa;cAAA4D,QAAA,EACX7C,OAAO,CAACmD,GAAG,CAACW,MAAM,iBACjBzH,OAAA,CAAC+C,YAAY;gBAAAyD,QAAA,gBACXxG,OAAA;kBACEoH,IAAI,EAAC,UAAU;kBACfM,OAAO,EAAEtD,QAAQ,CAACM,iBAAiB,CAAC4B,QAAQ,CAACmB,MAAM,CAACxB,EAAE,CAAE;kBACxDqB,QAAQ,EAAEA,CAAA,KAAMnB,kBAAkB,CAACsB,MAAM,CAACxB,EAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF5G,OAAA;kBAAAwG,QAAA,EAAQiB,MAAM,CAACE;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GANXa,MAAM,CAACxB,EAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOd,CACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACZ,eAED5G,OAAA,CAACiD,WAAW;YAAAuD,QAAA,gBACVxG,OAAA,CAACS,MAAM;cAAC2G,IAAI,EAAC,QAAQ;cAACP,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC,KAAK,CAAE;cAAAuC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrE5G,OAAA,CAACS,MAAM;cAAC2G,IAAI,EAAC,QAAQ;cAAAZ,QAAA,EAAEtC,WAAW,GAAG,IAAI,GAAG;YAAI;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACrD,EAAA,CA7OID,cAAc;EAAA,QACD1D,OAAO;AAAA;AAAAgI,IAAA,GADpBtE,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAnD,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAuE,IAAA;AAAAC,YAAA,CAAA1H,EAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}