{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:3001/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      const response = await api.get('/game-resources', {\n        params\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源分类\n  async getCategories() {\n    try {\n      const response = await api.get('/game-resources/categories');\n      return response.categories || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 检查更新\n  async checkUpdates() {\n    try {\n      const response = await api.post('/game-resources/check-updates');\n      return response.updates || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/update`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/repair`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: {\n          q: query,\n          ...filters\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  }\n};\nexport default gameResourceService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "gameResourceService", "getResources", "params", "get", "resources", "getCategories", "categories", "getResource", "resourceId", "resource", "toggleInstall", "post", "toggleEnable", "checkUpdates", "updates", "updateResource", "repairResource", "cleanCache", "batchOperation", "operation", "resourceIds", "getInstallProgress", "progress", "cancelInstall", "searchResources", "query", "filters", "q", "getRecommended", "getLatest", "limit", "getPopular", "rateResource", "rating", "comment", "getResourceRatings", "ratings"], "sources": ["D:/Test/Battle Launcher/src/services/gameResourceService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:3001/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000, // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      const response = await api.get('/game-resources', { params });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源分类\n  async getCategories() {\n    try {\n      const response = await api.get('/game-resources/categories');\n      return response.categories || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 检查更新\n  async checkUpdates() {\n    try {\n      const response = await api.post('/game-resources/check-updates');\n      return response.updates || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/update`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/repair`);\n      return response.resource;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: { q: query, ...filters }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default gameResourceService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EAAE;EAChBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMW,mBAAmB,GAAG;EAC1B;EACA,MAAMC,YAAYA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,iBAAiB,EAAE;QAAED;MAAO,CAAC,CAAC;MAC7D,OAAOV,QAAQ,CAACY,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMgB,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,4BAA4B,CAAC;MAC5D,OAAOX,QAAQ,CAACc,UAAU,IAAI,EAAE;IAClC,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkB,WAAWA,CAACC,UAAU,EAAE;IAC5B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,mBAAmBK,UAAU,EAAE,CAAC;MAC/D,OAAOhB,QAAQ,CAACiB,QAAQ;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMqB,aAAaA,CAACF,UAAU,EAAE;IAC9B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,iBAAiB,CAAC;MAC/E,OAAOhB,QAAQ,CAACiB,QAAQ;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMuB,YAAYA,CAACJ,UAAU,EAAE;IAC7B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,gBAAgB,CAAC;MAC9E,OAAOhB,QAAQ,CAACiB,QAAQ;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,+BAA+B,CAAC;MAChE,OAAOnB,QAAQ,CAACsB,OAAO,IAAI,EAAE;IAC/B,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM0B,cAAcA,CAACP,UAAU,EAAE;IAC/B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,SAAS,CAAC;MACvE,OAAOhB,QAAQ,CAACiB,QAAQ;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM2B,cAAcA,CAACR,UAAU,EAAE;IAC/B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,SAAS,CAAC;MACvE,OAAOhB,QAAQ,CAACiB,QAAQ;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM4B,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,6BAA6B,CAAC;MAC9D,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM6B,cAAcA,CAACC,SAAS,EAAEC,WAAW,EAAE;IAC3C,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,uBAAuB,EAAE;QACvDQ,SAAS;QACTC;MACF,CAAC,CAAC;MACF,OAAO5B,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMgC,kBAAkBA,CAACb,UAAU,EAAE;IACnC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,mBAAmBK,UAAU,WAAW,CAAC;MACxE,OAAOhB,QAAQ,CAAC8B,QAAQ;IAC1B,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkC,aAAaA,CAACf,UAAU,EAAE;IAC9B,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,SAAS,CAAC;MACvE,OAAOhB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMmC,eAAeA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,wBAAwB,EAAE;QACvDD,MAAM,EAAE;UAAEyB,CAAC,EAAEF,KAAK;UAAE,GAAGC;QAAQ;MACjC,CAAC,CAAC;MACF,OAAOlC,QAAQ,CAACY,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMuC,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,6BAA6B,CAAC;MAC7D,OAAOX,QAAQ,CAACY,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwC,SAASA,CAACC,KAAK,GAAG,EAAE,EAAE;IAC1B,IAAI;MACF,MAAMtC,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,wBAAwB,EAAE;QACvDD,MAAM,EAAE;UAAE4B;QAAM;MAClB,CAAC,CAAC;MACF,OAAOtC,QAAQ,CAACY,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM0C,UAAUA,CAACD,KAAK,GAAG,EAAE,EAAE;IAC3B,IAAI;MACF,MAAMtC,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,yBAAyB,EAAE;QACxDD,MAAM,EAAE;UAAE4B;QAAM;MAClB,CAAC,CAAC;MACF,OAAOtC,QAAQ,CAACY,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM2C,YAAYA,CAACxB,UAAU,EAAEyB,MAAM,EAAEC,OAAO,GAAG,EAAE,EAAE;IACnD,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAMhB,GAAG,CAACmC,IAAI,CAAC,mBAAmBH,UAAU,OAAO,EAAE;QACpEyB,MAAM;QACNC;MACF,CAAC,CAAC;MACF,OAAO1C,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM8C,kBAAkBA,CAAC3B,UAAU,EAAE;IACnC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,GAAG,CAAC,mBAAmBK,UAAU,UAAU,CAAC;MACvE,OAAOhB,QAAQ,CAAC4C,OAAO,IAAI,EAAE;IAC/B,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeW,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}