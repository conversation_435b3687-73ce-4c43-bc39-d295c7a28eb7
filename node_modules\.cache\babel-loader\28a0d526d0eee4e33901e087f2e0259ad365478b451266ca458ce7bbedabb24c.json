{"ast": null, "code": "// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslaToRgba({\n  hue,\n  saturation,\n  lightness,\n  alpha\n}) {\n  hue /= 360;\n  saturation /= 100;\n  lightness /= 100;\n  let red = 0;\n  let green = 0;\n  let blue = 0;\n  if (!saturation) {\n    red = green = blue = lightness;\n  } else {\n    const q = lightness < 0.5 ? lightness * (1 + saturation) : lightness + saturation - lightness * saturation;\n    const p = 2 * lightness - q;\n    red = hueToRgb(p, q, hue + 1 / 3);\n    green = hueToRgb(p, q, hue);\n    blue = hueToRgb(p, q, hue - 1 / 3);\n  }\n  return {\n    red: Math.round(red * 255),\n    green: Math.round(green * 255),\n    blue: Math.round(blue * 255),\n    alpha\n  };\n}\nexport { hslaToRgba };", "map": {"version": 3, "names": ["hueToRgb", "p", "q", "t", "hslaToRgba", "hue", "saturation", "lightness", "alpha", "red", "green", "blue", "Math", "round"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs"], "sourcesContent": ["// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\nexport { hslaToRgba };\n"], "mappings": "AAAA;AACA,SAASA,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACvB,IAAIA,CAAC,GAAG,CAAC,EACLA,CAAC,IAAI,CAAC;EACV,IAAIA,CAAC,GAAG,CAAC,EACLA,CAAC,IAAI,CAAC;EACV,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACT,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGE,CAAC;EAC9B,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EACT,OAAOD,CAAC;EACZ,IAAIC,CAAC,GAAG,CAAC,GAAG,CAAC,EACT,OAAOF,CAAC,GAAG,CAACC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC;EACxC,OAAOF,CAAC;AACZ;AACA,SAASG,UAAUA,CAAC;EAAEC,GAAG;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAM,CAAC,EAAE;EACvDH,GAAG,IAAI,GAAG;EACVC,UAAU,IAAI,GAAG;EACjBC,SAAS,IAAI,GAAG;EAChB,IAAIE,GAAG,GAAG,CAAC;EACX,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAI,CAACL,UAAU,EAAE;IACbG,GAAG,GAAGC,KAAK,GAAGC,IAAI,GAAGJ,SAAS;EAClC,CAAC,MACI;IACD,MAAML,CAAC,GAAGK,SAAS,GAAG,GAAG,GACnBA,SAAS,IAAI,CAAC,GAAGD,UAAU,CAAC,GAC5BC,SAAS,GAAGD,UAAU,GAAGC,SAAS,GAAGD,UAAU;IACrD,MAAML,CAAC,GAAG,CAAC,GAAGM,SAAS,GAAGL,CAAC;IAC3BO,GAAG,GAAGT,QAAQ,CAACC,CAAC,EAAEC,CAAC,EAAEG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACjCK,KAAK,GAAGV,QAAQ,CAACC,CAAC,EAAEC,CAAC,EAAEG,GAAG,CAAC;IAC3BM,IAAI,GAAGX,QAAQ,CAACC,CAAC,EAAEC,CAAC,EAAEG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EACtC;EACA,OAAO;IACHI,GAAG,EAAEG,IAAI,CAACC,KAAK,CAACJ,GAAG,GAAG,GAAG,CAAC;IAC1BC,KAAK,EAAEE,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,GAAG,CAAC;IAC9BC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,GAAG,CAAC;IAC5BH;EACJ,CAAC;AACL;AAEA,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}