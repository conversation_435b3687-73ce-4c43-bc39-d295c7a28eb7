{"ast": null, "code": "import { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\nclass DragGesture extends Feature {\n  constructor(node) {\n    super(node);\n    this.removeGroupControls = noop;\n    this.removeListeners = noop;\n    this.controls = new VisualElementDragControls(node);\n  }\n  mount() {\n    // If we've been provided a DragControls for manual control over the drag gesture,\n    // subscribe this component to it on mount.\n    const {\n      dragControls\n    } = this.node.getProps();\n    if (dragControls) {\n      this.removeGroupControls = dragControls.subscribe(this.controls);\n    }\n    this.removeListeners = this.controls.addListeners() || noop;\n  }\n  unmount() {\n    this.removeGroupControls();\n    this.removeListeners();\n  }\n}\nexport { DragGesture };", "map": {"version": 3, "names": ["Feature", "noop", "VisualElementDragControls", "DragGesture", "constructor", "node", "removeGroupControls", "removeListeners", "controls", "mount", "dragControls", "getProps", "subscribe", "addListeners", "unmount"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/gestures/drag/index.mjs"], "sourcesContent": ["import { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\n\nclass DragGesture extends Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = noop;\n        this.removeListeners = noop;\n        this.controls = new VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\nexport { DragGesture };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mCAAmC;AAC3D,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,yBAAyB,QAAQ,iCAAiC;AAE3E,MAAMC,WAAW,SAASH,OAAO,CAAC;EAC9BI,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACC,mBAAmB,GAAGL,IAAI;IAC/B,IAAI,CAACM,eAAe,GAAGN,IAAI;IAC3B,IAAI,CAACO,QAAQ,GAAG,IAAIN,yBAAyB,CAACG,IAAI,CAAC;EACvD;EACAI,KAAKA,CAAA,EAAG;IACJ;IACA;IACA,MAAM;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC;IAC7C,IAAID,YAAY,EAAE;MACd,IAAI,CAACJ,mBAAmB,GAAGI,YAAY,CAACE,SAAS,CAAC,IAAI,CAACJ,QAAQ,CAAC;IACpE;IACA,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,QAAQ,CAACK,YAAY,CAAC,CAAC,IAAIZ,IAAI;EAC/D;EACAa,OAAOA,CAAA,EAAG;IACN,IAAI,CAACR,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;AACJ;AAEA,SAASJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}