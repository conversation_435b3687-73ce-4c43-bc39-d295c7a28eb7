{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\pages\\\\StorePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useGameResources } from '../context/GameResourceContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StoreContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n_c = StoreContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 16px;\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0;\n`;\n_c3 = Title;\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n  align-items: center;\n`;\n_c4 = HeaderActions;\nconst ActionButton = styled(motion.button)`\n  padding: 8px 16px;\n  background: ${props => props.primary ? 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : 'rgba(255, 255, 255, 0.1)'};\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.primary ? 'linear-gradient(135deg, #ff8555 0%, #ff6b35 100%)' : 'rgba(255, 255, 255, 0.15)'};\n  }\n`;\n_c5 = ActionButton;\nconst FilterBar = styled.div`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 20px;\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n`;\n_c6 = FilterBar;\nconst FilterGroup = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c7 = FilterGroup;\nconst FilterLabel = styled.label`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n  white-space: nowrap;\n`;\n_c8 = FilterLabel;\nconst Select = styled.select`\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  option {\n    background: ${props => props.theme.colors.background.secondary};\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n_c9 = Select;\nconst SearchInput = styled.input`\n  padding: 8px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  width: 200px;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  &::placeholder {\n    color: ${props => props.theme.colors.text.tertiary};\n  }\n`;\n_c0 = SearchInput;\nconst TabContainer = styled.div`\n  display: flex;\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 8px;\n  overflow: hidden;\n`;\n_c1 = TabContainer;\nconst Tab = styled.button`\n  padding: 12px 24px;\n  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  border: none;\n  color: ${props => props.active ? 'white' : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.active ? props.theme.colors.primary : 'rgba(255, 255, 255, 0.05)'};\n    color: ${props => props.active ? 'white' : props.theme.colors.text.primary};\n  }\n`;\n_c10 = Tab;\nconst ProductGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n  flex: 1;\n  overflow-y: auto;\n  padding-right: 8px;\n`;\n_c11 = ProductGrid;\nconst ProductCard = styled(motion.div)`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    transform: translateY(-4px);\n    box-shadow: ${props => props.theme.shadows.xl};\n  }\n`;\n_c12 = ProductCard;\nconst ProductImage = styled.div`\n  width: 100%;\n  height: 200px;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: ${props => props.theme.colors.text.tertiary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  position: relative;\n  \n  &::before {\n    content: '🎮';\n    font-size: 48px;\n    opacity: 0.3;\n  }\n`;\n_c13 = ProductImage;\nconst ProductContent = styled.div`\n  padding: 20px;\n`;\n_c14 = ProductContent;\nconst ProductHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n`;\n_c15 = ProductHeader;\nconst ProductInfo = styled.div`\n  flex: 1;\n`;\n_c16 = ProductInfo;\nconst ProductName = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0 0 4px 0;\n  line-height: 1.3;\n`;\n_c17 = ProductName;\nconst ProductCategory = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  background: rgba(255, 255, 255, 0.1);\n  padding: 2px 8px;\n  border-radius: 12px;\n`;\n_c18 = ProductCategory;\nconst ProductPrice = styled.div`\n  text-align: right;\n`;\n_c19 = ProductPrice;\nconst Price = styled.div`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.primary};\n  margin-bottom: 4px;\n`;\n_c20 = Price;\nconst OriginalPrice = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.tertiary};\n  text-decoration: line-through;\n`;\n_c21 = OriginalPrice;\nconst DiscountBadge = styled.span`\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  background: ${props => props.theme.colors.status.error};\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: ${props => props.theme.fontSizes.xs};\n  font-weight: ${props => props.theme.fontWeights.bold};\n`;\n_c22 = DiscountBadge;\nconst ProductDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n  line-height: 1.5;\n  margin: 12px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n`;\n_c23 = ProductDescription;\nconst ProductFeatures = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  margin: 12px 0;\n`;\n_c24 = ProductFeatures;\nconst FeatureTag = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.secondary};\n  background: rgba(255, 255, 255, 0.05);\n  padding: 2px 6px;\n  border-radius: 8px;\n  border: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n_c25 = FeatureTag;\nconst ProductActions = styled.div`\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n`;\n_c26 = ProductActions;\nconst ProductButton = styled(motion.button)`\n  flex: 1;\n  padding: 10px 16px;\n  background: ${props => {\n  if (props.variant === 'primary') return 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)';\n  if (props.variant === 'success') return props.theme.colors.status.success;\n  return 'rgba(255, 255, 255, 0.1)';\n}};\n  border: 1px solid transparent;\n  border-radius: 6px;\n  color: white;\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: ${props => props.theme.shadows.md};\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n`;\n_c27 = ProductButton;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n  \n  &::after {\n    content: '';\n    width: 32px;\n    height: 32px;\n    border: 3px solid ${props => props.theme.colors.border.primary};\n    border-top: 3px solid ${props => props.theme.colors.primary};\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n`;\n_c28 = LoadingSpinner;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 60px 20px;\n  color: ${props => props.theme.colors.text.tertiary};\n  \n  h3 {\n    font-size: ${props => props.theme.fontSizes.lg};\n    margin-bottom: 8px;\n    color: ${props => props.theme.colors.text.secondary};\n  }\n  \n  p {\n    font-size: ${props => props.theme.fontSizes.sm};\n  }\n`;\n_c29 = EmptyState;\nconst StorePage = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('all');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('all');\n  const [sortBy, setSortBy] = useState('popular');\n  const [loading, setLoading] = useState(false);\n\n  // 模拟商店数据\n  const [storeProducts] = useState([{\n    id: 1,\n    name: 'F/A-18C Hornet',\n    category: 'aircraft-jet',\n    price: 79.99,\n    originalPrice: 89.99,\n    discount: 11,\n    description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\n    features: ['多用途', '舰载机', '精确制导武器', '空中加油'],\n    owned: false,\n    popular: true\n  }, {\n    id: 2,\n    name: 'A-10C II Tank Killer',\n    category: 'aircraft-jet',\n    price: 59.99,\n    description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\n    features: ['近距支援', 'GAU-8机炮', '装甲防护', 'Maverick导弹'],\n    owned: true,\n    popular: true\n  }, {\n    id: 3,\n    name: 'Persian Gulf Map',\n    category: 'terrain',\n    price: 49.99,\n    originalPrice: 59.99,\n    discount: 17,\n    description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\n    features: ['高精度地形', '真实机场', '城市建模', '海上平台'],\n    owned: false,\n    popular: false\n  }, {\n    id: 4,\n    name: 'F-16C Viper',\n    category: 'aircraft-jet',\n    price: 79.99,\n    description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\n    features: ['多用途', '敏捷机动', '现代航电', 'HARM导弹'],\n    owned: false,\n    popular: true\n  }, {\n    id: 5,\n    name: 'Syria Map',\n    category: 'terrain',\n    price: 49.99,\n    description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\n    features: ['中东地形', '多国机场', '复杂地形', '历史战场'],\n    owned: false,\n    popular: false\n  }, {\n    id: 6,\n    name: 'AH-64D Apache',\n    category: 'aircraft-heli',\n    price: 69.99,\n    description: '世界最先进的攻击直升机，具备强大的反装甲和火力支援能力。',\n    features: ['攻击直升机', 'Hellfire导弹', '链式机炮', '夜视系统'],\n    owned: false,\n    popular: true\n  }]);\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n  };\n  const handlePurchase = productId => {\n    // 模拟购买逻辑\n    console.log('购买产品:', productId);\n    alert('购买功能暂未实现，这是一个演示版本。');\n  };\n  const handleViewDetails = productId => {\n    // 模拟查看详情\n    console.log('查看详情:', productId);\n    alert('详情页面暂未实现。');\n  };\n  const filteredProducts = storeProducts.filter(product => {\n    // 标签页过滤\n    if (activeTab === 'owned' && !product.owned) return false;\n    if (activeTab === 'available' && product.owned) return false;\n    if (activeTab === 'popular' && !product.popular) return false;\n\n    // 分类过滤\n    if (categoryFilter !== 'all' && product.category !== categoryFilter) return false;\n\n    // 搜索过滤\n    if (searchTerm && !product.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;\n    return true;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'price-low':\n        return a.price - b.price;\n      case 'price-high':\n        return b.price - a.price;\n      case 'name':\n        return a.name.localeCompare(b.name);\n      default:\n        // popular\n        return b.popular - a.popular;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(StoreContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u8BD5\\u73A9\\u548C\\u8D2D\\u4E70\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeaderActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => window.open('https://www.digitalcombatsimulator.com/en/shop/', '_blank'),\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\uD83C\\uDF10 \\u5B98\\u65B9\\u5546\\u5E97\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          primary: true,\n          onClick: () => alert('购物车功能暂未实现'),\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\uD83D\\uDED2 \\u8D2D\\u7269\\u8F66 (0)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterBar, {\n      children: [/*#__PURE__*/_jsxDEV(TabContainer, {\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'all',\n          onClick: () => handleTabChange('all'),\n          children: \"\\u5168\\u90E8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'popular',\n          onClick: () => handleTabChange('popular'),\n          children: \"\\u70ED\\u95E8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'owned',\n          onClick: () => handleTabChange('owned'),\n          children: \"\\u5DF2\\u62E5\\u6709\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          active: activeTab === 'available',\n          onClick: () => handleTabChange('available'),\n          children: \"\\u53EF\\u8D2D\\u4E70\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n        children: [/*#__PURE__*/_jsxDEV(FilterLabel, {\n          children: \"\\u5206\\u7C7B:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: categoryFilter,\n          onChange: e => setCategoryFilter(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"\\u5168\\u90E8\\u5206\\u7C7B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"aircraft-jet\",\n            children: \"\\u55B7\\u6C14\\u53D1\\u52A8\\u673A\\u98DE\\u673A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"aircraft-heli\",\n            children: \"\\u76F4\\u5347\\u673A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"terrain\",\n            children: \"\\u5730\\u5F62\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"campaign\",\n            children: \"\\u6218\\u5F79\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n        children: [/*#__PURE__*/_jsxDEV(FilterLabel, {\n          children: \"\\u6392\\u5E8F:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: sortBy,\n          onChange: e => setSortBy(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"popular\",\n            children: \"\\u70ED\\u95E8\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"price-low\",\n            children: \"\\u4EF7\\u683C\\u4ECE\\u4F4E\\u5230\\u9AD8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"price-high\",\n            children: \"\\u4EF7\\u683C\\u4ECE\\u9AD8\\u5230\\u4F4E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"name\",\n            children: \"\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterGroup, {\n        children: [/*#__PURE__*/_jsxDEV(FilterLabel, {\n          children: \"\\u641C\\u7D22:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchInput, {\n          type: \"text\",\n          placeholder: \"\\u641C\\u7D22\\u4EA7\\u54C1...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 9\n    }, this) : filteredProducts.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u4EA7\\u54C1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u5C1D\\u8BD5\\u8C03\\u6574\\u7B5B\\u9009\\u6761\\u4EF6\\u6216\\u641C\\u7D22\\u5173\\u952E\\u8BCD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ProductGrid, {\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: filteredProducts.map((product, index) => /*#__PURE__*/_jsxDEV(ProductCard, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.3,\n            delay: index * 0.05\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProductImage, {\n            children: product.discount && /*#__PURE__*/_jsxDEV(DiscountBadge, {\n              children: [\"-\", product.discount, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ProductContent, {\n            children: [/*#__PURE__*/_jsxDEV(ProductHeader, {\n              children: [/*#__PURE__*/_jsxDEV(ProductInfo, {\n                children: [/*#__PURE__*/_jsxDEV(ProductName, {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ProductCategory, {\n                  children: [product.category === 'aircraft-jet' && '喷气发动机飞机', product.category === 'aircraft-heli' && '直升机', product.category === 'terrain' && '地形', product.category === 'campaign' && '战役']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ProductPrice, {\n                children: [/*#__PURE__*/_jsxDEV(Price, {\n                  children: [\"$\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 23\n                }, this), product.originalPrice && /*#__PURE__*/_jsxDEV(OriginalPrice, {\n                  children: [\"$\", product.originalPrice]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ProductDescription, {\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ProductFeatures, {\n              children: product.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(FeatureTag, {\n                children: feature\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ProductActions, {\n              children: product.owned ? /*#__PURE__*/_jsxDEV(ProductButton, {\n                variant: \"success\",\n                disabled: true,\n                children: \"\\u2713 \\u5DF2\\u62E5\\u6709\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ProductButton, {\n                  onClick: () => handleViewDetails(product.id),\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(ProductButton, {\n                  variant: \"primary\",\n                  onClick: () => handlePurchase(product.id),\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: \"\\u8D2D\\u4E70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 17\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 454,\n    columnNumber: 5\n  }, this);\n};\n_s(StorePage, \"aSR0KyqTDviHa9hlaINc8zH7frw=\");\n_c30 = StorePage;\nexport default StorePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30;\n$RefreshReg$(_c, \"StoreContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"HeaderActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"FilterBar\");\n$RefreshReg$(_c7, \"FilterGroup\");\n$RefreshReg$(_c8, \"FilterLabel\");\n$RefreshReg$(_c9, \"Select\");\n$RefreshReg$(_c0, \"SearchInput\");\n$RefreshReg$(_c1, \"TabContainer\");\n$RefreshReg$(_c10, \"Tab\");\n$RefreshReg$(_c11, \"ProductGrid\");\n$RefreshReg$(_c12, \"ProductCard\");\n$RefreshReg$(_c13, \"ProductImage\");\n$RefreshReg$(_c14, \"ProductContent\");\n$RefreshReg$(_c15, \"ProductHeader\");\n$RefreshReg$(_c16, \"ProductInfo\");\n$RefreshReg$(_c17, \"ProductName\");\n$RefreshReg$(_c18, \"ProductCategory\");\n$RefreshReg$(_c19, \"ProductPrice\");\n$RefreshReg$(_c20, \"Price\");\n$RefreshReg$(_c21, \"OriginalPrice\");\n$RefreshReg$(_c22, \"DiscountBadge\");\n$RefreshReg$(_c23, \"ProductDescription\");\n$RefreshReg$(_c24, \"ProductFeatures\");\n$RefreshReg$(_c25, \"FeatureTag\");\n$RefreshReg$(_c26, \"ProductActions\");\n$RefreshReg$(_c27, \"ProductButton\");\n$RefreshReg$(_c28, \"LoadingSpinner\");\n$RefreshReg$(_c29, \"EmptyState\");\n$RefreshReg$(_c30, \"StorePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "motion", "AnimatePresence", "useGameResources", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StoreContainer", "div", "_c", "Header", "_c2", "Title", "h1", "props", "theme", "fontSizes", "fontWeights", "bold", "colors", "text", "primary", "_c3", "HeaderActions", "_c4", "ActionButton", "button", "border", "sm", "_c5", "FilterBar", "_c6", "FilterGroup", "_c7", "Filter<PERSON>abel", "label", "secondary", "_c8", "Select", "select", "background", "_c9", "SearchInput", "input", "tertiary", "_c0", "TabContainer", "_c1", "Tab", "active", "medium", "normal", "_c10", "ProductGrid", "_c11", "ProductCard", "shadows", "xl", "_c12", "ProductImage", "_c13", "ProductContent", "_c14", "ProductHeader", "_c15", "ProductInfo", "_c16", "ProductName", "h3", "lg", "semibold", "_c17", "ProductCategory", "span", "xs", "_c18", "ProductPrice", "_c19", "Price", "_c20", "OriginalPrice", "_c21", "DiscountBadge", "status", "error", "_c22", "ProductDescription", "p", "_c23", "ProductFeatures", "_c24", "FeatureTag", "_c25", "ProductActions", "_c26", "ProductButton", "variant", "success", "md", "_c27", "LoadingSpinner", "_c28", "EmptyState", "_c29", "StorePage", "_s", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "categoryFilter", "setCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "sortBy", "setSortBy", "loading", "setLoading", "storeProducts", "id", "name", "category", "price", "originalPrice", "discount", "description", "features", "owned", "popular", "handleTabChange", "tab", "handlePurchase", "productId", "console", "log", "alert", "handleViewDetails", "filteredProducts", "filter", "product", "toLowerCase", "includes", "sort", "a", "b", "localeCompare", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "open", "whileHover", "scale", "whileTap", "value", "onChange", "e", "target", "type", "placeholder", "length", "map", "index", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "delay", "feature", "idx", "disabled", "_c30", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/pages/StorePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport styled from 'styled-components';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { useGameResources } from '../context/GameResourceContext';\r\n\r\nconst StoreContainer = styled.div`\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24px;\r\n`;\r\n\r\nconst Header = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 16px;\r\n`;\r\n\r\nconst Title = styled.h1`\r\n  font-size: ${props => props.theme.fontSizes['2xl']};\r\n  font-weight: ${props => props.theme.fontWeights.bold};\r\n  color: ${props => props.theme.colors.text.primary};\r\n  margin: 0;\r\n`;\r\n\r\nconst HeaderActions = styled.div`\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n`;\r\n\r\nconst ActionButton = styled(motion.button)`\r\n  padding: 8px 16px;\r\n  background: ${props => props.primary ? \r\n    'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : \r\n    'rgba(255, 255, 255, 0.1)'\r\n  };\r\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\r\n  border-radius: 6px;\r\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    background: ${props => props.primary ? \r\n      'linear-gradient(135deg, #ff8555 0%, #ff6b35 100%)' : \r\n      'rgba(255, 255, 255, 0.15)'\r\n    };\r\n  }\r\n`;\r\n\r\nconst FilterBar = styled.div`\r\n  background: rgba(45, 45, 45, 0.8);\r\n  border: 1px solid ${props => props.theme.colors.border.primary};\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  display: flex;\r\n  gap: 16px;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n`;\r\n\r\nconst FilterGroup = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n`;\r\n\r\nconst FilterLabel = styled.label`\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  color: ${props => props.theme.colors.text.secondary};\r\n  white-space: nowrap;\r\n`;\r\n\r\nconst Select = styled.select`\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid ${props => props.theme.colors.border.primary};\r\n  border-radius: 6px;\r\n  color: ${props => props.theme.colors.text.primary};\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  cursor: pointer;\r\n  \r\n  &:focus {\r\n    outline: none;\r\n    border-color: ${props => props.theme.colors.primary};\r\n  }\r\n  \r\n  option {\r\n    background: ${props => props.theme.colors.background.secondary};\r\n    color: ${props => props.theme.colors.text.primary};\r\n  }\r\n`;\r\n\r\nconst SearchInput = styled.input`\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid ${props => props.theme.colors.border.primary};\r\n  border-radius: 6px;\r\n  color: ${props => props.theme.colors.text.primary};\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  width: 200px;\r\n  \r\n  &:focus {\r\n    outline: none;\r\n    border-color: ${props => props.theme.colors.primary};\r\n  }\r\n  \r\n  &::placeholder {\r\n    color: ${props => props.theme.colors.text.tertiary};\r\n  }\r\n`;\r\n\r\nconst TabContainer = styled.div`\r\n  display: flex;\r\n  background: rgba(45, 45, 45, 0.8);\r\n  border: 1px solid ${props => props.theme.colors.border.primary};\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n`;\r\n\r\nconst Tab = styled.button`\r\n  padding: 12px 24px;\r\n  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};\r\n  border: none;\r\n  color: ${props => props.active ? 'white' : props.theme.colors.text.secondary};\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    background: ${props => props.active ? props.theme.colors.primary : 'rgba(255, 255, 255, 0.05)'};\r\n    color: ${props => props.active ? 'white' : props.theme.colors.text.primary};\r\n  }\r\n`;\r\n\r\nconst ProductGrid = styled.div`\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 20px;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding-right: 8px;\r\n`;\r\n\r\nconst ProductCard = styled(motion.div)`\r\n  background: rgba(45, 45, 45, 0.8);\r\n  border: 1px solid ${props => props.theme.colors.border.primary};\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    border-color: ${props => props.theme.colors.primary};\r\n    transform: translateY(-4px);\r\n    box-shadow: ${props => props.theme.shadows.xl};\r\n  }\r\n`;\r\n\r\nconst ProductImage = styled.div`\r\n  width: 100%;\r\n  height: 200px;\r\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: ${props => props.theme.colors.text.tertiary};\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  position: relative;\r\n  \r\n  &::before {\r\n    content: '🎮';\r\n    font-size: 48px;\r\n    opacity: 0.3;\r\n  }\r\n`;\r\n\r\nconst ProductContent = styled.div`\r\n  padding: 20px;\r\n`;\r\n\r\nconst ProductHeader = styled.div`\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 12px;\r\n`;\r\n\r\nconst ProductInfo = styled.div`\r\n  flex: 1;\r\n`;\r\n\r\nconst ProductName = styled.h3`\r\n  font-size: ${props => props.theme.fontSizes.lg};\r\n  font-weight: ${props => props.theme.fontWeights.semibold};\r\n  color: ${props => props.theme.colors.text.primary};\r\n  margin: 0 0 4px 0;\r\n  line-height: 1.3;\r\n`;\r\n\r\nconst ProductCategory = styled.span`\r\n  font-size: ${props => props.theme.fontSizes.xs};\r\n  color: ${props => props.theme.colors.text.tertiary};\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n`;\r\n\r\nconst ProductPrice = styled.div`\r\n  text-align: right;\r\n`;\r\n\r\nconst Price = styled.div`\r\n  font-size: ${props => props.theme.fontSizes.xl};\r\n  font-weight: ${props => props.theme.fontWeights.bold};\r\n  color: ${props => props.theme.colors.primary};\r\n  margin-bottom: 4px;\r\n`;\r\n\r\nconst OriginalPrice = styled.div`\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  color: ${props => props.theme.colors.text.tertiary};\r\n  text-decoration: line-through;\r\n`;\r\n\r\nconst DiscountBadge = styled.span`\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  background: ${props => props.theme.colors.status.error};\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: ${props => props.theme.fontSizes.xs};\r\n  font-weight: ${props => props.theme.fontWeights.bold};\r\n`;\r\n\r\nconst ProductDescription = styled.p`\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  color: ${props => props.theme.colors.text.secondary};\r\n  line-height: 1.5;\r\n  margin: 12px 0;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n`;\r\n\r\nconst ProductFeatures = styled.div`\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  margin: 12px 0;\r\n`;\r\n\r\nconst FeatureTag = styled.span`\r\n  font-size: ${props => props.theme.fontSizes.xs};\r\n  color: ${props => props.theme.colors.text.secondary};\r\n  background: rgba(255, 255, 255, 0.05);\r\n  padding: 2px 6px;\r\n  border-radius: 8px;\r\n  border: 1px solid ${props => props.theme.colors.border.secondary};\r\n`;\r\n\r\nconst ProductActions = styled.div`\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-top: 16px;\r\n`;\r\n\r\nconst ProductButton = styled(motion.button)`\r\n  flex: 1;\r\n  padding: 10px 16px;\r\n  background: ${props => {\r\n    if (props.variant === 'primary') return 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)';\r\n    if (props.variant === 'success') return props.theme.colors.status.success;\r\n    return 'rgba(255, 255, 255, 0.1)';\r\n  }};\r\n  border: 1px solid transparent;\r\n  border-radius: 6px;\r\n  color: white;\r\n  font-size: ${props => props.theme.fontSizes.sm};\r\n  font-weight: ${props => props.theme.fontWeights.medium};\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: ${props => props.theme.shadows.md};\r\n  }\r\n  \r\n  &:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n  }\r\n`;\r\n\r\nconst LoadingSpinner = styled.div`\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n  \r\n  &::after {\r\n    content: '';\r\n    width: 32px;\r\n    height: 32px;\r\n    border: 3px solid ${props => props.theme.colors.border.primary};\r\n    border-top: 3px solid ${props => props.theme.colors.primary};\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n  }\r\n`;\r\n\r\nconst EmptyState = styled.div`\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: ${props => props.theme.colors.text.tertiary};\r\n  \r\n  h3 {\r\n    font-size: ${props => props.theme.fontSizes.lg};\r\n    margin-bottom: 8px;\r\n    color: ${props => props.theme.colors.text.secondary};\r\n  }\r\n  \r\n  p {\r\n    font-size: ${props => props.theme.fontSizes.sm};\r\n  }\r\n`;\r\n\r\nconst StorePage = () => {\r\n  const [activeTab, setActiveTab] = useState('all');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [categoryFilter, setCategoryFilter] = useState('all');\r\n  const [sortBy, setSortBy] = useState('popular');\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // 模拟商店数据\r\n  const [storeProducts] = useState([\r\n    {\r\n      id: 1,\r\n      name: 'F/A-18C Hornet',\r\n      category: 'aircraft-jet',\r\n      price: 79.99,\r\n      originalPrice: 89.99,\r\n      discount: 11,\r\n      description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\r\n      features: ['多用途', '舰载机', '精确制导武器', '空中加油'],\r\n      owned: false,\r\n      popular: true\r\n    },\r\n    {\r\n      id: 2,\r\n      name: 'A-10C II Tank Killer',\r\n      category: 'aircraft-jet',\r\n      price: 59.99,\r\n      description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\r\n      features: ['近距支援', 'GAU-8机炮', '装甲防护', 'Maverick导弹'],\r\n      owned: true,\r\n      popular: true\r\n    },\r\n    {\r\n      id: 3,\r\n      name: 'Persian Gulf Map',\r\n      category: 'terrain',\r\n      price: 49.99,\r\n      originalPrice: 59.99,\r\n      discount: 17,\r\n      description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\r\n      features: ['高精度地形', '真实机场', '城市建模', '海上平台'],\r\n      owned: false,\r\n      popular: false\r\n    },\r\n    {\r\n      id: 4,\r\n      name: 'F-16C Viper',\r\n      category: 'aircraft-jet',\r\n      price: 79.99,\r\n      description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\r\n      features: ['多用途', '敏捷机动', '现代航电', 'HARM导弹'],\r\n      owned: false,\r\n      popular: true\r\n    },\r\n    {\r\n      id: 5,\r\n      name: 'Syria Map',\r\n      category: 'terrain',\r\n      price: 49.99,\r\n      description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\r\n      features: ['中东地形', '多国机场', '复杂地形', '历史战场'],\r\n      owned: false,\r\n      popular: false\r\n    },\r\n    {\r\n      id: 6,\r\n      name: 'AH-64D Apache',\r\n      category: 'aircraft-heli',\r\n      price: 69.99,\r\n      description: '世界最先进的攻击直升机，具备强大的反装甲和火力支援能力。',\r\n      features: ['攻击直升机', 'Hellfire导弹', '链式机炮', '夜视系统'],\r\n      owned: false,\r\n      popular: true\r\n    }\r\n  ]);\r\n\r\n  const handleTabChange = (tab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n  const handlePurchase = (productId) => {\r\n    // 模拟购买逻辑\r\n    console.log('购买产品:', productId);\r\n    alert('购买功能暂未实现，这是一个演示版本。');\r\n  };\r\n\r\n  const handleViewDetails = (productId) => {\r\n    // 模拟查看详情\r\n    console.log('查看详情:', productId);\r\n    alert('详情页面暂未实现。');\r\n  };\r\n\r\n  const filteredProducts = storeProducts.filter(product => {\r\n    // 标签页过滤\r\n    if (activeTab === 'owned' && !product.owned) return false;\r\n    if (activeTab === 'available' && product.owned) return false;\r\n    if (activeTab === 'popular' && !product.popular) return false;\r\n    \r\n    // 分类过滤\r\n    if (categoryFilter !== 'all' && product.category !== categoryFilter) return false;\r\n    \r\n    // 搜索过滤\r\n    if (searchTerm && !product.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;\r\n    \r\n    return true;\r\n  }).sort((a, b) => {\r\n    switch (sortBy) {\r\n      case 'price-low':\r\n        return a.price - b.price;\r\n      case 'price-high':\r\n        return b.price - a.price;\r\n      case 'name':\r\n        return a.name.localeCompare(b.name);\r\n      default: // popular\r\n        return b.popular - a.popular;\r\n    }\r\n  });\r\n\r\n  return (\r\n    <StoreContainer>\r\n      <Header>\r\n        <Title>试玩和购买</Title>\r\n        <HeaderActions>\r\n          <ActionButton\r\n            onClick={() => window.open('https://www.digitalcombatsimulator.com/en/shop/', '_blank')}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n          >\r\n            🌐 官方商店\r\n          </ActionButton>\r\n          <ActionButton\r\n            primary\r\n            onClick={() => alert('购物车功能暂未实现')}\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n          >\r\n            🛒 购物车 (0)\r\n          </ActionButton>\r\n        </HeaderActions>\r\n      </Header>\r\n\r\n      <FilterBar>\r\n        <TabContainer>\r\n          <Tab \r\n            active={activeTab === 'all'} \r\n            onClick={() => handleTabChange('all')}\r\n          >\r\n            全部\r\n          </Tab>\r\n          <Tab \r\n            active={activeTab === 'popular'} \r\n            onClick={() => handleTabChange('popular')}\r\n          >\r\n            热门\r\n          </Tab>\r\n          <Tab \r\n            active={activeTab === 'owned'} \r\n            onClick={() => handleTabChange('owned')}\r\n          >\r\n            已拥有\r\n          </Tab>\r\n          <Tab \r\n            active={activeTab === 'available'} \r\n            onClick={() => handleTabChange('available')}\r\n          >\r\n            可购买\r\n          </Tab>\r\n        </TabContainer>\r\n\r\n        <FilterGroup>\r\n          <FilterLabel>分类:</FilterLabel>\r\n          <Select\r\n            value={categoryFilter}\r\n            onChange={(e) => setCategoryFilter(e.target.value)}\r\n          >\r\n            <option value=\"all\">全部分类</option>\r\n            <option value=\"aircraft-jet\">喷气发动机飞机</option>\r\n            <option value=\"aircraft-heli\">直升机</option>\r\n            <option value=\"terrain\">地形</option>\r\n            <option value=\"campaign\">战役</option>\r\n          </Select>\r\n        </FilterGroup>\r\n\r\n        <FilterGroup>\r\n          <FilterLabel>排序:</FilterLabel>\r\n          <Select\r\n            value={sortBy}\r\n            onChange={(e) => setSortBy(e.target.value)}\r\n          >\r\n            <option value=\"popular\">热门度</option>\r\n            <option value=\"price-low\">价格从低到高</option>\r\n            <option value=\"price-high\">价格从高到低</option>\r\n            <option value=\"name\">名称</option>\r\n          </Select>\r\n        </FilterGroup>\r\n\r\n        <FilterGroup>\r\n          <FilterLabel>搜索:</FilterLabel>\r\n          <SearchInput\r\n            type=\"text\"\r\n            placeholder=\"搜索产品...\"\r\n            value={searchTerm}\r\n            onChange={(e) => setSearchTerm(e.target.value)}\r\n          />\r\n        </FilterGroup>\r\n      </FilterBar>\r\n\r\n      {loading ? (\r\n        <LoadingSpinner />\r\n      ) : filteredProducts.length === 0 ? (\r\n        <EmptyState>\r\n          <h3>没有找到产品</h3>\r\n          <p>尝试调整筛选条件或搜索关键词</p>\r\n        </EmptyState>\r\n      ) : (\r\n        <ProductGrid>\r\n          <AnimatePresence>\r\n            {filteredProducts.map((product, index) => (\r\n              <ProductCard\r\n                key={product.id}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -20 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n              >\r\n                <ProductImage>\r\n                  {product.discount && (\r\n                    <DiscountBadge>-{product.discount}%</DiscountBadge>\r\n                  )}\r\n                </ProductImage>\r\n                \r\n                <ProductContent>\r\n                  <ProductHeader>\r\n                    <ProductInfo>\r\n                      <ProductName>{product.name}</ProductName>\r\n                      <ProductCategory>\r\n                        {product.category === 'aircraft-jet' && '喷气发动机飞机'}\r\n                        {product.category === 'aircraft-heli' && '直升机'}\r\n                        {product.category === 'terrain' && '地形'}\r\n                        {product.category === 'campaign' && '战役'}\r\n                      </ProductCategory>\r\n                    </ProductInfo>\r\n                    <ProductPrice>\r\n                      <Price>${product.price}</Price>\r\n                      {product.originalPrice && (\r\n                        <OriginalPrice>${product.originalPrice}</OriginalPrice>\r\n                      )}\r\n                    </ProductPrice>\r\n                  </ProductHeader>\r\n\r\n                  <ProductDescription>\r\n                    {product.description}\r\n                  </ProductDescription>\r\n\r\n                  <ProductFeatures>\r\n                    {product.features.map((feature, idx) => (\r\n                      <FeatureTag key={idx}>{feature}</FeatureTag>\r\n                    ))}\r\n                  </ProductFeatures>\r\n\r\n                  <ProductActions>\r\n                    {product.owned ? (\r\n                      <ProductButton\r\n                        variant=\"success\"\r\n                        disabled\r\n                      >\r\n                        ✓ 已拥有\r\n                      </ProductButton>\r\n                    ) : (\r\n                      <>\r\n                        <ProductButton\r\n                          onClick={() => handleViewDetails(product.id)}\r\n                          whileHover={{ scale: 1.02 }}\r\n                          whileTap={{ scale: 0.98 }}\r\n                        >\r\n                          查看详情\r\n                        </ProductButton>\r\n                        <ProductButton\r\n                          variant=\"primary\"\r\n                          onClick={() => handlePurchase(product.id)}\r\n                          whileHover={{ scale: 1.02 }}\r\n                          whileTap={{ scale: 0.98 }}\r\n                        >\r\n                          购买\r\n                        </ProductButton>\r\n                      </>\r\n                    )}\r\n                  </ProductActions>\r\n                </ProductContent>\r\n              </ProductCard>\r\n            ))}\r\n          </AnimatePresence>\r\n        </ProductGrid>\r\n      )}\r\n    </StoreContainer>\r\n  );\r\n};\r\n\r\nexport default StorePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,cAAc,GAAGR,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,MAAM,GAAGX,MAAM,CAACS,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,MAAM;AAQZ,MAAME,KAAK,GAAGb,MAAM,CAACc,EAAE;AACvB,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBF,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACC,IAAI;AACtD,WAAWJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA,CAAC;AAACC,GAAA,GALIV,KAAK;AAOX,MAAMW,aAAa,GAAGxB,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAG1B,MAAM,CAACC,MAAM,CAAC0B,MAAM,CAAC;AAC1C;AACA,gBAAgBZ,KAAK,IAAIA,KAAK,CAACO,OAAO,GAClC,mDAAmD,GACnD,0BAA0B;AAC9B,sBACsBP,KAAK,IAAIA,KAAK,CAACO,OAAO,GAAG,aAAa,GAAGP,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChG;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACO,OAAO,GAAG,OAAO,GAAGP,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AAC7E,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA,kBAAkBd,KAAK,IAAIA,KAAK,CAACO,OAAO,GAClC,mDAAmD,GACnD,2BAA2B;AACjC;AACA,CACC;AAACQ,GAAA,GAnBIJ,YAAY;AAqBlB,MAAMK,SAAS,GAAG/B,MAAM,CAACS,GAAG;AAC5B;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GATID,SAAS;AAWf,MAAME,WAAW,GAAGjC,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAJID,WAAW;AAMjB,MAAME,WAAW,GAAGnC,MAAM,CAACoC,KAAK;AAChC,eAAerB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AACrD;AACA,CAAC;AAACC,GAAA,GAJIH,WAAW;AAMjB,MAAMI,MAAM,GAAGvC,MAAM,CAACwC,MAAM;AAC5B;AACA;AACA,sBAAsBzB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AACvD;AACA;AACA;AACA,kBAAkBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACqB,UAAU,CAACJ,SAAS;AAClE,aAAatB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACrD;AACA,CAAC;AAACoB,GAAA,GAlBIH,MAAM;AAoBZ,MAAMI,WAAW,GAAG3C,MAAM,CAAC4C,KAAK;AAChC;AACA;AACA,sBAAsB7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AACvD;AACA;AACA;AACA,aAAaP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwB,QAAQ;AACtD;AACA,CAAC;AAACC,GAAA,GAjBIH,WAAW;AAmBjB,MAAMI,YAAY,GAAG/C,MAAM,CAACS,GAAG;AAC/B;AACA;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA;AACA,CAAC;AAAC0B,GAAA,GANID,YAAY;AAQlB,MAAME,GAAG,GAAGjD,MAAM,CAAC2B,MAAM;AACzB;AACA,gBAAgBZ,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO,GAAG,aAAa;AAClF;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAG,OAAO,GAAGnC,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AAC9E,eAAetB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,iBAAiBd,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACE,WAAW,CAACiC,MAAM,GAAGpC,KAAK,CAACC,KAAK,CAACE,WAAW,CAACkC,MAAM;AACxG;AACA;AACA;AACA;AACA,kBAAkBrC,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAGnC,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO,GAAG,2BAA2B;AAClG,aAAaP,KAAK,IAAIA,KAAK,CAACmC,MAAM,GAAG,OAAO,GAAGnC,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AAC9E;AACA,CAAC;AAAC+B,IAAA,GAdIJ,GAAG;AAgBT,MAAMK,WAAW,GAAGtD,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GAPID,WAAW;AASjB,MAAME,WAAW,GAAGxD,MAAM,CAACC,MAAM,CAACQ,GAAG,CAAC;AACtC;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA,oBAAoBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AACvD;AACA,kBAAkBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACyC,OAAO,CAACC,EAAE;AACjD;AACA,CAAC;AAACC,IAAA,GAZIH,WAAW;AAcjB,MAAMI,YAAY,GAAG5D,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,WAAWM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwB,QAAQ;AACpD,eAAe9B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAhBID,YAAY;AAkBlB,MAAME,cAAc,GAAG9D,MAAM,CAACS,GAAG;AACjC;AACA,CAAC;AAACsD,IAAA,GAFID,cAAc;AAIpB,MAAME,aAAa,GAAGhE,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACwD,IAAA,GALID,aAAa;AAOnB,MAAME,WAAW,GAAGlE,MAAM,CAACS,GAAG;AAC9B;AACA,CAAC;AAAC0D,IAAA,GAFID,WAAW;AAIjB,MAAME,WAAW,GAAGpE,MAAM,CAACqE,EAAE;AAC7B,eAAetD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACqD,EAAE;AAChD,iBAAiBvD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACqD,QAAQ;AAC1D,WAAWxD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA;AACA,CAAC;AAACkD,IAAA,GANIJ,WAAW;AAQjB,MAAMK,eAAe,GAAGzE,MAAM,CAAC0E,IAAI;AACnC,eAAe3D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC0D,EAAE;AAChD,WAAW5D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwB,QAAQ;AACpD;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GANIH,eAAe;AAQrB,MAAMI,YAAY,GAAG7E,MAAM,CAACS,GAAG;AAC/B;AACA,CAAC;AAACqE,IAAA,GAFID,YAAY;AAIlB,MAAME,KAAK,GAAG/E,MAAM,CAACS,GAAG;AACxB,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACyC,EAAE;AAChD,iBAAiB3C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACC,IAAI;AACtD,WAAWJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AAC9C;AACA,CAAC;AAAC0D,IAAA,GALID,KAAK;AAOX,MAAME,aAAa,GAAGjF,MAAM,CAACS,GAAG;AAChC,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwB,QAAQ;AACpD;AACA,CAAC;AAACqC,IAAA,GAJID,aAAa;AAMnB,MAAME,aAAa,GAAGnF,MAAM,CAAC0E,IAAI;AACjC;AACA;AACA;AACA,gBAAgB3D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACgE,MAAM,CAACC,KAAK;AACxD;AACA;AACA;AACA,eAAetE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC0D,EAAE;AAChD,iBAAiB5D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACC,IAAI;AACtD,CAAC;AAACmE,IAAA,GAVIH,aAAa;AAYnB,MAAMI,kBAAkB,GAAGvF,MAAM,CAACwF,CAAC;AACnC,eAAezE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GATIF,kBAAkB;AAWxB,MAAMG,eAAe,GAAG1F,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACkF,IAAA,GALID,eAAe;AAOrB,MAAME,UAAU,GAAG5F,MAAM,CAAC0E,IAAI;AAC9B,eAAe3D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC0D,EAAE;AAChD,WAAW5D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AACrD;AACA;AACA;AACA,sBAAsBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACS,SAAS;AAClE,CAAC;AAACwD,IAAA,GAPID,UAAU;AAShB,MAAME,cAAc,GAAG9F,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACsF,IAAA,GAJID,cAAc;AAMpB,MAAME,aAAa,GAAGhG,MAAM,CAACC,MAAM,CAAC0B,MAAM,CAAC;AAC3C;AACA;AACA,gBAAgBZ,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACkF,OAAO,KAAK,SAAS,EAAE,OAAO,mDAAmD;EAC3F,IAAIlF,KAAK,CAACkF,OAAO,KAAK,SAAS,EAAE,OAAOlF,KAAK,CAACC,KAAK,CAACI,MAAM,CAACgE,MAAM,CAACc,OAAO;EACzE,OAAO,0BAA0B;AACnC,CAAC;AACH;AACA;AACA;AACA,eAAenF,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,iBAAiBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACiC,MAAM;AACxD;AACA;AACA;AACA;AACA;AACA,kBAAkBpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACyC,OAAO,CAAC0C,EAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAzBIJ,aAAa;AA2BnB,MAAMK,cAAc,GAAGrG,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAClE,4BAA4BP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AAC/D;AACA;AACA;AACA,CAAC;AAACgF,IAAA,GAfID,cAAc;AAiBpB,MAAME,UAAU,GAAGvG,MAAM,CAACS,GAAG;AAC7B;AACA;AACA,WAAWM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwB,QAAQ;AACpD;AACA;AACA,iBAAiB9B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACqD,EAAE;AAClD;AACA,aAAavD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AACvD;AACA;AACA;AACA,iBAAiBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAClD;AACA,CAAC;AAAC2E,IAAA,GAdID,UAAU;AAgBhB,MAAME,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+G,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiH,cAAc,EAAEC,iBAAiB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmH,MAAM,EAAEC,SAAS,CAAC,GAAGpH,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAACqH,OAAO,EAAEC,UAAU,CAAC,GAAGtH,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACuH,aAAa,CAAC,GAAGvH,QAAQ,CAAC,CAC/B;IACEwH,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,kCAAkC;IAC/CC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC1CC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,sBAAsB;IAC5BC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,KAAK;IACZG,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC;IACnDC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,kBAAkB;IACxBC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,KAAK;IACZC,aAAa,EAAE,KAAK;IACpBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3CC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE,KAAK;IACZG,WAAW,EAAE,iCAAiC;IAC9CC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;IAC3CC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,KAAK;IACZG,WAAW,EAAE,8BAA8B;IAC3CC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC1CC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,KAAK;IACZG,WAAW,EAAE,8BAA8B;IAC3CC,QAAQ,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,CAAC;IACjDC,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE;EACX,CAAC,CACF,CAAC;EAEF,MAAMC,eAAe,GAAIC,GAAG,IAAK;IAC/BrB,YAAY,CAACqB,GAAG,CAAC;EACnB,CAAC;EAED,MAAMC,cAAc,GAAIC,SAAS,IAAK;IACpC;IACAC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,SAAS,CAAC;IAC/BG,KAAK,CAAC,oBAAoB,CAAC;EAC7B,CAAC;EAED,MAAMC,iBAAiB,GAAIJ,SAAS,IAAK;IACvC;IACAC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,SAAS,CAAC;IAC/BG,KAAK,CAAC,WAAW,CAAC;EACpB,CAAC;EAED,MAAME,gBAAgB,GAAGnB,aAAa,CAACoB,MAAM,CAACC,OAAO,IAAI;IACvD;IACA,IAAI/B,SAAS,KAAK,OAAO,IAAI,CAAC+B,OAAO,CAACZ,KAAK,EAAE,OAAO,KAAK;IACzD,IAAInB,SAAS,KAAK,WAAW,IAAI+B,OAAO,CAACZ,KAAK,EAAE,OAAO,KAAK;IAC5D,IAAInB,SAAS,KAAK,SAAS,IAAI,CAAC+B,OAAO,CAACX,OAAO,EAAE,OAAO,KAAK;;IAE7D;IACA,IAAIhB,cAAc,KAAK,KAAK,IAAI2B,OAAO,CAAClB,QAAQ,KAAKT,cAAc,EAAE,OAAO,KAAK;;IAEjF;IACA,IAAIF,UAAU,IAAI,CAAC6B,OAAO,CAACnB,IAAI,CAACoB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,UAAU,CAAC8B,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IAE9F,OAAO,IAAI;EACb,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAChB,QAAQ9B,MAAM;MACZ,KAAK,WAAW;QACd,OAAO6B,CAAC,CAACrB,KAAK,GAAGsB,CAAC,CAACtB,KAAK;MAC1B,KAAK,YAAY;QACf,OAAOsB,CAAC,CAACtB,KAAK,GAAGqB,CAAC,CAACrB,KAAK;MAC1B,KAAK,MAAM;QACT,OAAOqB,CAAC,CAACvB,IAAI,CAACyB,aAAa,CAACD,CAAC,CAACxB,IAAI,CAAC;MACrC;QAAS;QACP,OAAOwB,CAAC,CAAChB,OAAO,GAAGe,CAAC,CAACf,OAAO;IAChC;EACF,CAAC,CAAC;EAEF,oBACE1H,OAAA,CAACG,cAAc;IAAAyI,QAAA,gBACb5I,OAAA,CAACM,MAAM;MAAAsI,QAAA,gBACL5I,OAAA,CAACQ,KAAK;QAAAoI,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACpBhJ,OAAA,CAACmB,aAAa;QAAAyH,QAAA,gBACZ5I,OAAA,CAACqB,YAAY;UACX4H,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,IAAI,CAAC,iDAAiD,EAAE,QAAQ,CAAE;UACxFC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAT,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfhJ,OAAA,CAACqB,YAAY;UACXJ,OAAO;UACPgI,OAAO,EAAEA,CAAA,KAAMhB,KAAK,CAAC,WAAW,CAAE;UAClCmB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAT,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEThJ,OAAA,CAAC0B,SAAS;MAAAkH,QAAA,gBACR5I,OAAA,CAAC0C,YAAY;QAAAkG,QAAA,gBACX5I,OAAA,CAAC4C,GAAG;UACFC,MAAM,EAAEyD,SAAS,KAAK,KAAM;UAC5B2C,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,KAAK,CAAE;UAAAiB,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhJ,OAAA,CAAC4C,GAAG;UACFC,MAAM,EAAEyD,SAAS,KAAK,SAAU;UAChC2C,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,SAAS,CAAE;UAAAiB,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhJ,OAAA,CAAC4C,GAAG;UACFC,MAAM,EAAEyD,SAAS,KAAK,OAAQ;UAC9B2C,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,OAAO,CAAE;UAAAiB,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhJ,OAAA,CAAC4C,GAAG;UACFC,MAAM,EAAEyD,SAAS,KAAK,WAAY;UAClC2C,OAAO,EAAEA,CAAA,KAAMtB,eAAe,CAAC,WAAW,CAAE;UAAAiB,QAAA,EAC7C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEfhJ,OAAA,CAAC4B,WAAW;QAAAgH,QAAA,gBACV5I,OAAA,CAAC8B,WAAW;UAAA8G,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9BhJ,OAAA,CAACkC,MAAM;UACLqH,KAAK,EAAE7C,cAAe;UACtB8C,QAAQ,EAAGC,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAX,QAAA,gBAEnD5I,OAAA;YAAQuJ,KAAK,EAAC,KAAK;YAAAX,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjChJ,OAAA;YAAQuJ,KAAK,EAAC,cAAc;YAAAX,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7ChJ,OAAA;YAAQuJ,KAAK,EAAC,eAAe;YAAAX,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ChJ,OAAA;YAAQuJ,KAAK,EAAC,SAAS;YAAAX,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACnChJ,OAAA;YAAQuJ,KAAK,EAAC,UAAU;YAAAX,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEdhJ,OAAA,CAAC4B,WAAW;QAAAgH,QAAA,gBACV5I,OAAA,CAAC8B,WAAW;UAAA8G,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9BhJ,OAAA,CAACkC,MAAM;UACLqH,KAAK,EAAE3C,MAAO;UACd4C,QAAQ,EAAGC,CAAC,IAAK5C,SAAS,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAX,QAAA,gBAE3C5I,OAAA;YAAQuJ,KAAK,EAAC,SAAS;YAAAX,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpChJ,OAAA;YAAQuJ,KAAK,EAAC,WAAW;YAAAX,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzChJ,OAAA;YAAQuJ,KAAK,EAAC,YAAY;YAAAX,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ChJ,OAAA;YAAQuJ,KAAK,EAAC,MAAM;YAAAX,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEdhJ,OAAA,CAAC4B,WAAW;QAAAgH,QAAA,gBACV5I,OAAA,CAAC8B,WAAW;UAAA8G,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9BhJ,OAAA,CAACsC,WAAW;UACVqH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,6BAAS;UACrBL,KAAK,EAAE/C,UAAW;UAClBgD,QAAQ,EAAGC,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEXlC,OAAO,gBACN9G,OAAA,CAACgG,cAAc;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAChBb,gBAAgB,CAAC0B,MAAM,KAAK,CAAC,gBAC/B7J,OAAA,CAACkG,UAAU;MAAA0C,QAAA,gBACT5I,OAAA;QAAA4I,QAAA,EAAI;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACfhJ,OAAA;QAAA4I,QAAA,EAAG;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,gBAEbhJ,OAAA,CAACiD,WAAW;MAAA2F,QAAA,eACV5I,OAAA,CAACH,eAAe;QAAA+I,QAAA,EACbT,gBAAgB,CAAC2B,GAAG,CAAC,CAACzB,OAAO,EAAE0B,KAAK,kBACnC/J,OAAA,CAACmD,WAAW;UAEV6G,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAER,KAAK,GAAG;UAAK,CAAE;UAAAnB,QAAA,gBAEnD5I,OAAA,CAACuD,YAAY;YAAAqF,QAAA,EACVP,OAAO,CAACf,QAAQ,iBACftH,OAAA,CAAC8E,aAAa;cAAA8D,QAAA,GAAC,GAAC,EAACP,OAAO,CAACf,QAAQ,EAAC,GAAC;YAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe;UACnD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,eAEfhJ,OAAA,CAACyD,cAAc;YAAAmF,QAAA,gBACb5I,OAAA,CAAC2D,aAAa;cAAAiF,QAAA,gBACZ5I,OAAA,CAAC6D,WAAW;gBAAA+E,QAAA,gBACV5I,OAAA,CAAC+D,WAAW;kBAAA6E,QAAA,EAAEP,OAAO,CAACnB;gBAAI;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAc,CAAC,eACzChJ,OAAA,CAACoE,eAAe;kBAAAwE,QAAA,GACbP,OAAO,CAAClB,QAAQ,KAAK,cAAc,IAAI,SAAS,EAChDkB,OAAO,CAAClB,QAAQ,KAAK,eAAe,IAAI,KAAK,EAC7CkB,OAAO,CAAClB,QAAQ,KAAK,SAAS,IAAI,IAAI,EACtCkB,OAAO,CAAClB,QAAQ,KAAK,UAAU,IAAI,IAAI;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACdhJ,OAAA,CAACwE,YAAY;gBAAAoE,QAAA,gBACX5I,OAAA,CAAC0E,KAAK;kBAAAkE,QAAA,GAAC,GAAC,EAACP,OAAO,CAACjB,KAAK;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC9BX,OAAO,CAAChB,aAAa,iBACpBrH,OAAA,CAAC4E,aAAa;kBAAAgE,QAAA,GAAC,GAAC,EAACP,OAAO,CAAChB,aAAa;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEhBhJ,OAAA,CAACkF,kBAAkB;cAAA0D,QAAA,EAChBP,OAAO,CAACd;YAAW;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAErBhJ,OAAA,CAACqF,eAAe;cAAAuD,QAAA,EACbP,OAAO,CAACb,QAAQ,CAACsC,GAAG,CAAC,CAACU,OAAO,EAAEC,GAAG,kBACjCzK,OAAA,CAACuF,UAAU;gBAAAqD,QAAA,EAAY4B;cAAO,GAAbC,GAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAuB,CAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC,eAElBhJ,OAAA,CAACyF,cAAc;cAAAmD,QAAA,EACZP,OAAO,CAACZ,KAAK,gBACZzH,OAAA,CAAC2F,aAAa;gBACZC,OAAO,EAAC,SAAS;gBACjB8E,QAAQ;gBAAA9B,QAAA,EACT;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,gBAEhBhJ,OAAA,CAAAE,SAAA;gBAAA0I,QAAA,gBACE5I,OAAA,CAAC2F,aAAa;kBACZsD,OAAO,EAAEA,CAAA,KAAMf,iBAAiB,CAACG,OAAO,CAACpB,EAAE,CAAE;kBAC7CmC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAT,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC,eAChBhJ,OAAA,CAAC2F,aAAa;kBACZC,OAAO,EAAC,SAAS;kBACjBqD,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACQ,OAAO,CAACpB,EAAE,CAAE;kBAC1CmC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAT,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA,eAChB;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GArEZX,OAAO,CAACpB,EAAE;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEJ,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACd;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC3C,EAAA,CAvSID,SAAS;AAAAuE,IAAA,GAATvE,SAAS;AAySf,eAAeA,SAAS;AAAC,IAAA/F,EAAA,EAAAE,GAAA,EAAAW,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAwE,IAAA;AAAAC,YAAA,CAAAvK,EAAA;AAAAuK,YAAA,CAAArK,GAAA;AAAAqK,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAAnJ,GAAA;AAAAmJ,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAA/I,GAAA;AAAA+I,YAAA,CAAA3I,GAAA;AAAA2I,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA5H,IAAA;AAAA4H,YAAA,CAAA1H,IAAA;AAAA0H,YAAA,CAAAtH,IAAA;AAAAsH,YAAA,CAAApH,IAAA;AAAAoH,YAAA,CAAAlH,IAAA;AAAAkH,YAAA,CAAAhH,IAAA;AAAAgH,YAAA,CAAA9G,IAAA;AAAA8G,YAAA,CAAAzG,IAAA;AAAAyG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAnG,IAAA;AAAAmG,YAAA,CAAAjG,IAAA;AAAAiG,YAAA,CAAA/F,IAAA;AAAA+F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}