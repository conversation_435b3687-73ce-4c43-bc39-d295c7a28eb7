{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // 初始化时检查本地存储的token\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const token = localStorage.getItem('dcs_token');\n        if (token) {\n          const userData = await authService.verifyToken(token);\n          setUser(userData);\n        }\n      } catch (error) {\n        console.error('Token verification failed:', error);\n        localStorage.removeItem('dcs_token');\n      } finally {\n        setLoading(false);\n      }\n    };\n    initAuth();\n  }, []);\n\n  // 登录\n  const login = async credentials => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await authService.login(credentials);\n      const {\n        user: userData,\n        token\n      } = response;\n      localStorage.setItem('dcs_token', token);\n      setUser(userData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '登录失败，请检查用户名和密码';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 注册\n  const register = async userData => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await authService.register(userData);\n      const {\n        user: newUser,\n        token\n      } = response;\n      localStorage.setItem('dcs_token', token);\n      setUser(newUser);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '注册失败，请稍后重试';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出\n  const logout = () => {\n    localStorage.removeItem('dcs_token');\n    setUser(null);\n    setError(null);\n  };\n\n  // 更新用户资料\n  const updateProfile = async profileData => {\n    try {\n      setLoading(true);\n      setError(null);\n      const updatedUser = await authService.updateProfile(profileData);\n      setUser(updatedUser);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '更新资料失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 修改密码\n  const changePassword = async passwordData => {\n    try {\n      setLoading(true);\n      setError(null);\n      await authService.changePassword(passwordData);\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const errorMessage = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '修改密码失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 清除错误\n  const clearError = () => {\n    setError(null);\n  };\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"PA9FxEY9xSNRrsSqaLtbYei52Hs=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "error", "setError", "initAuth", "token", "localStorage", "getItem", "userData", "verifyToken", "console", "removeItem", "login", "credentials", "response", "setItem", "success", "_error$response", "_error$response$data", "errorMessage", "data", "message", "register", "newUser", "_error$response2", "_error$response2$data", "logout", "updateProfile", "profileData", "updatedUser", "_error$response3", "_error$response3$data", "changePassword", "passwordData", "_error$response4", "_error$response4$data", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport authService from '../services/authService';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // 初始化时检查本地存储的token\n  useEffect(() => {\n    const initAuth = async () => {\n      try {\n        const token = localStorage.getItem('dcs_token');\n        if (token) {\n          const userData = await authService.verifyToken(token);\n          setUser(userData);\n        }\n      } catch (error) {\n        console.error('Token verification failed:', error);\n        localStorage.removeItem('dcs_token');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initAuth();\n  }, []);\n\n  // 登录\n  const login = async (credentials) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await authService.login(credentials);\n      const { user: userData, token } = response;\n      \n      localStorage.setItem('dcs_token', token);\n      setUser(userData);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '登录失败，请检查用户名和密码';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 注册\n  const register = async (userData) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await authService.register(userData);\n      const { user: newUser, token } = response;\n      \n      localStorage.setItem('dcs_token', token);\n      setUser(newUser);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '注册失败，请稍后重试';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出\n  const logout = () => {\n    localStorage.removeItem('dcs_token');\n    setUser(null);\n    setError(null);\n  };\n\n  // 更新用户资料\n  const updateProfile = async (profileData) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const updatedUser = await authService.updateProfile(profileData);\n      setUser(updatedUser);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '更新资料失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 修改密码\n  const changePassword = async (passwordData) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      await authService.changePassword(passwordData);\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '修改密码失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 清除错误\n  const clearError = () => {\n    setError(null);\n  };\n\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    changePassword,\n    clearError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,WAAW,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QAC/C,IAAIF,KAAK,EAAE;UACT,MAAMG,QAAQ,GAAG,MAAMrB,WAAW,CAACsB,WAAW,CAACJ,KAAK,CAAC;UACrDN,OAAO,CAACS,QAAQ,CAAC;QACnB;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDI,YAAY,CAACK,UAAU,CAAC,WAAW,CAAC;MACtC,CAAC,SAAS;QACRV,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMW,QAAQ,GAAG,MAAM3B,WAAW,CAACyB,KAAK,CAACC,WAAW,CAAC;MACrD,MAAM;QAAEf,IAAI,EAAEU,QAAQ;QAAEH;MAAM,CAAC,GAAGS,QAAQ;MAE1CR,YAAY,CAACS,OAAO,CAAC,WAAW,EAAEV,KAAK,CAAC;MACxCN,OAAO,CAACS,QAAQ,CAAC;MAEjB,OAAO;QAAEQ,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAe,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAf,KAAK,CAACY,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,gBAAgB;MACtElB,QAAQ,CAACgB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEd,KAAK,EAAEiB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqB,QAAQ,GAAG,MAAOd,QAAQ,IAAK;IACnC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMW,QAAQ,GAAG,MAAM3B,WAAW,CAACmC,QAAQ,CAACd,QAAQ,CAAC;MACrD,MAAM;QAAEV,IAAI,EAAEyB,OAAO;QAAElB;MAAM,CAAC,GAAGS,QAAQ;MAEzCR,YAAY,CAACS,OAAO,CAAC,WAAW,EAAEV,KAAK,CAAC;MACxCN,OAAO,CAACwB,OAAO,CAAC;MAEhB,OAAO;QAAEP,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAsB,gBAAA,EAAAC,qBAAA;MACd,MAAMN,YAAY,GAAG,EAAAK,gBAAA,GAAAtB,KAAK,CAACY,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,YAAY;MAClElB,QAAQ,CAACgB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEd,KAAK,EAAEiB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyB,MAAM,GAAGA,CAAA,KAAM;IACnBpB,YAAY,CAACK,UAAU,CAAC,WAAW,CAAC;IACpCZ,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,MAAMwB,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM0B,WAAW,GAAG,MAAM1C,WAAW,CAACwC,aAAa,CAACC,WAAW,CAAC;MAChE7B,OAAO,CAAC8B,WAAW,CAAC;MAEpB,OAAO;QAAEb,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACd,MAAMZ,YAAY,GAAG,EAAAW,gBAAA,GAAA5B,KAAK,CAACY,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,QAAQ;MAC9DlB,QAAQ,CAACgB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEd,KAAK,EAAEiB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+B,cAAc,GAAG,MAAOC,YAAY,IAAK;IAC7C,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMhB,WAAW,CAAC6C,cAAc,CAACC,YAAY,CAAC;MAE9C,OAAO;QAAEjB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOd,KAAK,EAAE;MAAA,IAAAgC,gBAAA,EAAAC,qBAAA;MACd,MAAMhB,YAAY,GAAG,EAAAe,gBAAA,GAAAhC,KAAK,CAACY,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,IAAI,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAI,QAAQ;MAC9DlB,QAAQ,CAACgB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEd,KAAK,EAAEiB;MAAa,CAAC;IAChD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,UAAU,GAAGA,CAAA,KAAM;IACvBjC,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMkC,KAAK,GAAG;IACZvC,IAAI;IACJE,OAAO;IACPE,KAAK;IACLU,KAAK;IACLU,QAAQ;IACRI,MAAM;IACNC,aAAa;IACbK,cAAc;IACdI;EACF,CAAC;EAED,oBACE/C,OAAA,CAACC,WAAW,CAACgD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzC,QAAA,EAChCA;EAAQ;IAAA2C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC7C,GAAA,CAvIWF,YAAY;AAAAgD,EAAA,GAAZhD,YAAY;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}