{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\components\\\\layout\\\\TitleBar.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TitleBarContainer = styled.div`\n  height: ${props => props.theme.components.titleBar.height};\n  background: rgba(26, 26, 26, 0.95);\n  border-bottom: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  -webkit-app-region: drag;\n  user-select: none;\n  backdrop-filter: blur(10px);\n`;\n_c = TitleBarContainer;\nconst TitleSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c2 = TitleSection;\nconst AppIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  color: white;\n`;\n_c3 = AppIcon;\nconst AppTitle = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n`;\n_c4 = AppTitle;\nconst WindowControls = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1px;\n  -webkit-app-region: no-drag;\n`;\n_c5 = WindowControls;\nconst ControlButton = styled(motion.button)`\n  width: 46px;\n  height: 32px;\n  border: none;\n  background: transparent;\n  color: ${props => props.theme.colors.text.secondary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  font-size: 10px;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: ${props => {\n  if (props.$variant === 'close') return '#e81123';\n  return 'rgba(255, 255, 255, 0.1)';\n}};\n    color: ${props => props.theme.colors.text.primary};\n  }\n  \n  &:active {\n    background: ${props => {\n  if (props.$variant === 'close') return '#c50e1f';\n  return 'rgba(255, 255, 255, 0.2)';\n}};\n  }\n`;\n_c6 = ControlButton;\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.secondary};\n  -webkit-app-region: no-drag;\n`;\n_c7 = UserInfo;\nconst UserAvatar = styled.div`\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: white;\n`;\n_c8 = UserAvatar;\nconst TitleBar = () => {\n  const handleMinimize = () => {\n    if (window.electronAPI) {\n      window.electronAPI.minimize();\n    }\n  };\n  const handleMaximize = () => {\n    if (window.electronAPI) {\n      window.electronAPI.maximize();\n    }\n  };\n  const handleClose = () => {\n    if (window.electronAPI) {\n      window.electronAPI.close();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(TitleBarContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TitleSection, {\n      children: [/*#__PURE__*/_jsxDEV(AppIcon, {\n        children: \"D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AppTitle, {\n        children: \"DCS World \\u542F\\u52A8\\u5668\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n      children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n        children: \"U\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"zansimple\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WindowControls, {\n      children: [/*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: handleMinimize,\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: \"\\u2500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        onClick: handleMaximize,\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: \"\\u2610\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ControlButton, {\n        $variant: \"close\",\n        onClick: handleClose,\n        whileHover: {\n          scale: 1.1\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_c9 = TitleBar;\nexport default TitleBar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"TitleBarContainer\");\n$RefreshReg$(_c2, \"TitleSection\");\n$RefreshReg$(_c3, \"AppIcon\");\n$RefreshReg$(_c4, \"AppTitle\");\n$RefreshReg$(_c5, \"WindowControls\");\n$RefreshReg$(_c6, \"ControlButton\");\n$RefreshReg$(_c7, \"UserInfo\");\n$RefreshReg$(_c8, \"UserAvatar\");\n$RefreshReg$(_c9, \"TitleBar\");", "map": {"version": 3, "names": ["React", "styled", "motion", "jsxDEV", "_jsxDEV", "TitleBarContainer", "div", "props", "theme", "components", "titleBar", "height", "colors", "border", "primary", "_c", "TitleSection", "_c2", "AppIcon", "_c3", "AppTitle", "fontSizes", "sm", "fontWeights", "medium", "text", "_c4", "WindowControls", "_c5", "ControlButton", "button", "secondary", "$variant", "_c6", "UserInfo", "xs", "_c7", "UserAvatar", "_c8", "TitleBar", "handleMinimize", "window", "electronAPI", "minimize", "handleMaximize", "maximize", "handleClose", "close", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "whileHover", "scale", "whileTap", "_c9", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/components/layout/TitleBar.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\n\nconst TitleBarContainer = styled.div`\n  height: ${props => props.theme.components.titleBar.height};\n  background: rgba(26, 26, 26, 0.95);\n  border-bottom: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 16px;\n  -webkit-app-region: drag;\n  user-select: none;\n  backdrop-filter: blur(10px);\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst AppIcon = styled.div`\n  width: 20px;\n  height: 20px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n  color: white;\n`;\n\nconst AppTitle = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n`;\n\nconst WindowControls = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1px;\n  -webkit-app-region: no-drag;\n`;\n\nconst ControlButton = styled(motion.button)`\n  width: 46px;\n  height: 32px;\n  border: none;\n  background: transparent;\n  color: ${props => props.theme.colors.text.secondary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  font-size: 10px;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: ${props => {\n      if (props.$variant === 'close') return '#e81123';\n      return 'rgba(255, 255, 255, 0.1)';\n    }};\n    color: ${props => props.theme.colors.text.primary};\n  }\n  \n  &:active {\n    background: ${props => {\n      if (props.$variant === 'close') return '#c50e1f';\n      return 'rgba(255, 255, 255, 0.2)';\n    }};\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.secondary};\n  -webkit-app-region: no-drag;\n`;\n\nconst UserAvatar = styled.div`\n  width: 20px;\n  height: 20px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: white;\n`;\n\nconst TitleBar = () => {\n  const handleMinimize = () => {\n    if (window.electronAPI) {\n      window.electronAPI.minimize();\n    }\n  };\n\n  const handleMaximize = () => {\n    if (window.electronAPI) {\n      window.electronAPI.maximize();\n    }\n  };\n\n  const handleClose = () => {\n    if (window.electronAPI) {\n      window.electronAPI.close();\n    }\n  };\n\n  return (\n    <TitleBarContainer>\n      <TitleSection>\n        <AppIcon>D</AppIcon>\n        <AppTitle>DCS World 启动器</AppTitle>\n      </TitleSection>\n      \n      <UserInfo>\n        <UserAvatar>U</UserAvatar>\n        <span>zansimple</span>\n      </UserInfo>\n      \n      <WindowControls>\n        <ControlButton\n          onClick={handleMinimize}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          ─\n        </ControlButton>\n        \n        <ControlButton\n          onClick={handleMaximize}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          ☐\n        </ControlButton>\n        \n        <ControlButton\n          $variant=\"close\"\n          onClick={handleClose}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          ✕\n        </ControlButton>\n      </WindowControls>\n    </TitleBarContainer>\n  );\n};\n\nexport default TitleBar;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,iBAAiB,GAAGJ,MAAM,CAACK,GAAG;AACpC,YAAYC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,UAAU,CAACC,QAAQ,CAACC,MAAM;AAC3D;AACA,6BAA6BJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,MAAM,CAACC,OAAO;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAXIV,iBAAiB;AAavB,MAAMW,YAAY,GAAGf,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,YAAY;AAMlB,MAAME,OAAO,GAAGjB,MAAM,CAACK,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAXID,OAAO;AAab,MAAME,QAAQ,GAAGnB,MAAM,CAACK,GAAG;AAC3B,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACC,EAAE;AAChD,iBAAiBf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,MAAM;AACxD,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACa,IAAI,CAACX,OAAO;AACnD,CAAC;AAACY,GAAA,GAJIN,QAAQ;AAMd,MAAMO,cAAc,GAAG1B,MAAM,CAACK,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAG5B,MAAM,CAACC,MAAM,CAAC4B,MAAM,CAAC;AAC3C;AACA;AACA;AACA;AACA,WAAWvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACa,IAAI,CAACM,SAAS;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBxB,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACyB,QAAQ,KAAK,OAAO,EAAE,OAAO,SAAS;EAChD,OAAO,0BAA0B;AACnC,CAAC;AACL,aAAazB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACa,IAAI,CAACX,OAAO;AACrD;AACA;AACA;AACA,kBAAkBP,KAAK,IAAI;EACrB,IAAIA,KAAK,CAACyB,QAAQ,KAAK,OAAO,EAAE,OAAO,SAAS;EAChD,OAAO,0BAA0B;AACnC,CAAC;AACL;AACA,CAAC;AAACC,GAAA,GA3BIJ,aAAa;AA6BnB,MAAMK,QAAQ,GAAGjC,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACa,SAAS,CAACc,EAAE;AAChD,WAAW5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACa,IAAI,CAACM,SAAS;AACrD;AACA,CAAC;AAACK,GAAA,GAPIF,QAAQ;AASd,MAAMG,UAAU,GAAGpC,MAAM,CAACK,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,GAAA,GAXID,UAAU;AAahB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,MAAM,CAACC,WAAW,EAAE;MACtBD,MAAM,CAACC,WAAW,CAACC,QAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIH,MAAM,CAACC,WAAW,EAAE;MACtBD,MAAM,CAACC,WAAW,CAACG,QAAQ,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIL,MAAM,CAACC,WAAW,EAAE;MACtBD,MAAM,CAACC,WAAW,CAACK,KAAK,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACE3C,OAAA,CAACC,iBAAiB;IAAA2C,QAAA,gBAChB5C,OAAA,CAACY,YAAY;MAAAgC,QAAA,gBACX5C,OAAA,CAACc,OAAO;QAAA8B,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACpBhD,OAAA,CAACgB,QAAQ;QAAA4B,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAEfhD,OAAA,CAAC8B,QAAQ;MAAAc,QAAA,gBACP5C,OAAA,CAACiC,UAAU;QAAAW,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC1BhD,OAAA;QAAA4C,QAAA,EAAM;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAEXhD,OAAA,CAACuB,cAAc;MAAAqB,QAAA,gBACb5C,OAAA,CAACyB,aAAa;QACZwB,OAAO,EAAEb,cAAe;QACxBc,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAP,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBhD,OAAA,CAACyB,aAAa;QACZwB,OAAO,EAAET,cAAe;QACxBU,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAP,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBhD,OAAA,CAACyB,aAAa;QACZG,QAAQ,EAAC,OAAO;QAChBqB,OAAO,EAAEP,WAAY;QACrBQ,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAP,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAExB,CAAC;AAACK,GAAA,GA3DIlB,QAAQ;AA6Dd,eAAeA,QAAQ;AAAC,IAAAxB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAmB,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}