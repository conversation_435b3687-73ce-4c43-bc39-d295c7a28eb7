{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\context\\\\GameResourceContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport gameResourceService from '../services/gameResourceService';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameResourceContext = /*#__PURE__*/createContext();\nexport const useGameResources = () => {\n  _s();\n  const context = useContext(GameResourceContext);\n  if (!context) {\n    throw new Error('useGameResources must be used within a GameResourceProvider');\n  }\n  return context;\n};\n_s(useGameResources, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const GameResourceProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    user\n  } = useAuth();\n  const [resources, setResources] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [filter, setFilter] = useState({\n    category: 'all',\n    status: 'all',\n    // all, installed, available\n    search: ''\n  });\n\n  // 获取游戏资源列表\n  const fetchResources = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      let data;\n      if (user && user.role === 'admin') {\n        // 管理员可以看到所有模组\n        data = await gameResourceService.getResources();\n      } else if (user && user.authorizedModules) {\n        // 普通用户只能看到授权的模组\n        data = await gameResourceService.getAuthorizedModules(user.authorizedModules);\n      } else {\n        // 未登录用户看不到任何模组\n        data = [];\n      }\n      setResources(data);\n    } catch (error) {\n      console.error('Failed to fetch resources:', error);\n      setError('获取游戏资源失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取资源分类\n  const fetchCategories = async () => {\n    try {\n      const data = await gameResourceService.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    if (user) {\n      fetchResources();\n      fetchCategories();\n    } else {\n      setResources([]);\n    }\n  }, [user]);\n\n  // 安装/卸载资源\n  const toggleInstall = async resourceId => {\n    try {\n      setLoading(true);\n      const updatedResource = await gameResourceService.toggleInstall(resourceId);\n      setResources(prev => prev.map(resource => resource.id === resourceId ? updatedResource : resource));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '操作失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 启用/禁用资源\n  const toggleEnable = async resourceId => {\n    try {\n      const updatedResource = await gameResourceService.toggleEnable(resourceId);\n      setResources(prev => prev.map(resource => resource.id === resourceId ? updatedResource : resource));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '操作失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // 检查更新\n  const checkUpdates = async () => {\n    try {\n      setLoading(true);\n      const updates = await gameResourceService.checkUpdates();\n\n      // 更新有更新的资源状态\n      setResources(prev => prev.map(resource => {\n        const update = updates.find(u => u.id === resource.id);\n        return update ? {\n          ...resource,\n          hasUpdate: true,\n          latestVersion: update.version\n        } : resource;\n      }));\n      return {\n        success: true,\n        updates\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '检查更新失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新资源\n  const updateResource = async resourceId => {\n    try {\n      setLoading(true);\n      const updatedResource = await gameResourceService.updateResource(resourceId);\n      setResources(prev => prev.map(resource => resource.id === resourceId ? updatedResource : resource));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      const errorMessage = ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '更新失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 修复资源\n  const repairResource = async resourceId => {\n    try {\n      setLoading(true);\n      const repairedResource = await gameResourceService.repairResource(resourceId);\n      setResources(prev => prev.map(resource => resource.id === resourceId ? repairedResource : resource));\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      const errorMessage = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || '修复失败';\n      setError(errorMessage);\n      return {\n        success: false,\n        error: errorMessage\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 过滤资源\n  const filteredResources = resources.filter(resource => {\n    // 分类过滤\n    if (filter.category !== 'all' && resource.category !== filter.category) {\n      return false;\n    }\n\n    // 状态过滤\n    if (filter.status === 'installed' && !resource.installed) {\n      return false;\n    }\n    if (filter.status === 'available' && resource.installed) {\n      return false;\n    }\n\n    // 搜索过滤\n    if (filter.search && !resource.name.toLowerCase().includes(filter.search.toLowerCase())) {\n      return false;\n    }\n    return true;\n  });\n\n  // 获取统计信息\n  const getStats = () => {\n    const total = resources.length;\n    const installed = resources.filter(r => r.installed).length;\n    const enabled = resources.filter(r => r.installed && r.enabled).length;\n    const hasUpdates = resources.filter(r => r.hasUpdate).length;\n    return {\n      total,\n      installed,\n      enabled,\n      hasUpdates\n    };\n  };\n\n  // 清除错误\n  const clearError = () => {\n    setError(null);\n  };\n  const value = {\n    resources: filteredResources,\n    allResources: resources,\n    categories,\n    loading,\n    error,\n    filter,\n    setFilter,\n    fetchResources,\n    toggleInstall,\n    toggleEnable,\n    checkUpdates,\n    updateResource,\n    repairResource,\n    getStats,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(GameResourceContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_s2(GameResourceProvider, \"s9sN6HQ7hTmcUCyW0Z+1YaGW6Tc=\", false, function () {\n  return [useAuth];\n});\n_c = GameResourceProvider;\nvar _c;\n$RefreshReg$(_c, \"GameResourceProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "gameResourceService", "useAuth", "jsxDEV", "_jsxDEV", "GameResourceContext", "useGameResources", "_s", "context", "Error", "GameResourceProvider", "children", "_s2", "user", "resources", "setResources", "categories", "setCategories", "loading", "setLoading", "error", "setError", "filter", "setFilter", "category", "status", "search", "fetchResources", "data", "role", "getResources", "authorizedModules", "getAuthorizedModules", "console", "fetchCategories", "getCategories", "toggleInstall", "resourceId", "updatedResource", "prev", "map", "resource", "id", "success", "_error$response", "_error$response$data", "errorMessage", "response", "message", "toggleEnable", "_error$response2", "_error$response2$data", "checkUpdates", "updates", "update", "find", "u", "hasUpdate", "latestVersion", "version", "_error$response3", "_error$response3$data", "updateResource", "_error$response4", "_error$response4$data", "repairResource", "repairedResource", "_error$response5", "_error$response5$data", "filteredResources", "installed", "name", "toLowerCase", "includes", "getStats", "total", "length", "r", "enabled", "hasUpdates", "clearError", "value", "allResources", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/context/GameResourceContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport gameResourceService from '../services/gameResourceService';\nimport { useAuth } from './AuthContext';\n\nconst GameResourceContext = createContext();\n\nexport const useGameResources = () => {\n  const context = useContext(GameResourceContext);\n  if (!context) {\n    throw new Error('useGameResources must be used within a GameResourceProvider');\n  }\n  return context;\n};\n\nexport const GameResourceProvider = ({ children }) => {\n  const { user } = useAuth();\n  const [resources, setResources] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [filter, setFilter] = useState({\n    category: 'all',\n    status: 'all', // all, installed, available\n    search: ''\n  });\n\n  // 获取游戏资源列表\n  const fetchResources = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      let data;\n      if (user && user.role === 'admin') {\n        // 管理员可以看到所有模组\n        data = await gameResourceService.getResources();\n      } else if (user && user.authorizedModules) {\n        // 普通用户只能看到授权的模组\n        data = await gameResourceService.getAuthorizedModules(user.authorizedModules);\n      } else {\n        // 未登录用户看不到任何模组\n        data = [];\n      }\n      \n      setResources(data);\n    } catch (error) {\n      console.error('Failed to fetch resources:', error);\n      setError('获取游戏资源失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取资源分类\n  const fetchCategories = async () => {\n    try {\n      const data = await gameResourceService.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    if (user) {\n      fetchResources();\n      fetchCategories();\n    } else {\n      setResources([]);\n    }\n  }, [user]);\n\n  // 安装/卸载资源\n  const toggleInstall = async (resourceId) => {\n    try {\n      setLoading(true);\n      \n      const updatedResource = await gameResourceService.toggleInstall(resourceId);\n      \n      setResources(prev => \n        prev.map(resource => \n          resource.id === resourceId ? updatedResource : resource\n        )\n      );\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '操作失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 启用/禁用资源\n  const toggleEnable = async (resourceId) => {\n    try {\n      const updatedResource = await gameResourceService.toggleEnable(resourceId);\n      \n      setResources(prev => \n        prev.map(resource => \n          resource.id === resourceId ? updatedResource : resource\n        )\n      );\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '操作失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // 检查更新\n  const checkUpdates = async () => {\n    try {\n      setLoading(true);\n      \n      const updates = await gameResourceService.checkUpdates();\n      \n      // 更新有更新的资源状态\n      setResources(prev => \n        prev.map(resource => {\n          const update = updates.find(u => u.id === resource.id);\n          return update ? { ...resource, hasUpdate: true, latestVersion: update.version } : resource;\n        })\n      );\n      \n      return { success: true, updates };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '检查更新失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新资源\n  const updateResource = async (resourceId) => {\n    try {\n      setLoading(true);\n      \n      const updatedResource = await gameResourceService.updateResource(resourceId);\n      \n      setResources(prev => \n        prev.map(resource => \n          resource.id === resourceId ? updatedResource : resource\n        )\n      );\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '更新失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 修复资源\n  const repairResource = async (resourceId) => {\n    try {\n      setLoading(true);\n      \n      const repairedResource = await gameResourceService.repairResource(resourceId);\n      \n      setResources(prev => \n        prev.map(resource => \n          resource.id === resourceId ? repairedResource : resource\n        )\n      );\n      \n      return { success: true };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || '修复失败';\n      setError(errorMessage);\n      return { success: false, error: errorMessage };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 过滤资源\n  const filteredResources = resources.filter(resource => {\n    // 分类过滤\n    if (filter.category !== 'all' && resource.category !== filter.category) {\n      return false;\n    }\n    \n    // 状态过滤\n    if (filter.status === 'installed' && !resource.installed) {\n      return false;\n    }\n    if (filter.status === 'available' && resource.installed) {\n      return false;\n    }\n    \n    // 搜索过滤\n    if (filter.search && !resource.name.toLowerCase().includes(filter.search.toLowerCase())) {\n      return false;\n    }\n    \n    return true;\n  });\n\n  // 获取统计信息\n  const getStats = () => {\n    const total = resources.length;\n    const installed = resources.filter(r => r.installed).length;\n    const enabled = resources.filter(r => r.installed && r.enabled).length;\n    const hasUpdates = resources.filter(r => r.hasUpdate).length;\n    \n    return { total, installed, enabled, hasUpdates };\n  };\n\n  // 清除错误\n  const clearError = () => {\n    setError(null);\n  };\n\n  const value = {\n    resources: filteredResources,\n    allResources: resources,\n    categories,\n    loading,\n    error,\n    filter,\n    setFilter,\n    fetchResources,\n    toggleInstall,\n    toggleEnable,\n    checkUpdates,\n    updateResource,\n    repairResource,\n    getStats,\n    clearError\n  };\n\n  return (\n    <GameResourceContext.Provider value={value}>\n      {children}\n    </GameResourceContext.Provider>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,mBAAmB,gBAAGR,aAAa,CAAC,CAAC;AAE3C,OAAO,MAAMS,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,OAAO,GAAGV,UAAU,CAACO,mBAAmB,CAAC;EAC/C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;EAChF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,gBAAgB;AAQ7B,OAAO,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACpD,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC;IACnCyB,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,KAAK;IAAE;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIO,IAAI;MACR,IAAIf,IAAI,IAAIA,IAAI,CAACgB,IAAI,KAAK,OAAO,EAAE;QACjC;QACAD,IAAI,GAAG,MAAM3B,mBAAmB,CAAC6B,YAAY,CAAC,CAAC;MACjD,CAAC,MAAM,IAAIjB,IAAI,IAAIA,IAAI,CAACkB,iBAAiB,EAAE;QACzC;QACAH,IAAI,GAAG,MAAM3B,mBAAmB,CAAC+B,oBAAoB,CAACnB,IAAI,CAACkB,iBAAiB,CAAC;MAC/E,CAAC,MAAM;QACL;QACAH,IAAI,GAAG,EAAE;MACX;MAEAb,YAAY,CAACa,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,UAAU,CAAC;IACtB,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMN,IAAI,GAAG,MAAM3B,mBAAmB,CAACkC,aAAa,CAAC,CAAC;MACtDlB,aAAa,CAACW,IAAI,CAAC;IACrB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACApB,SAAS,CAAC,MAAM;IACd,IAAIa,IAAI,EAAE;MACRc,cAAc,CAAC,CAAC;MAChBO,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACLnB,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMuB,aAAa,GAAG,MAAOC,UAAU,IAAK;IAC1C,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMmB,eAAe,GAAG,MAAMrC,mBAAmB,CAACmC,aAAa,CAACC,UAAU,CAAC;MAE3EtB,YAAY,CAACwB,IAAI,IACfA,IAAI,CAACC,GAAG,CAACC,QAAQ,IACfA,QAAQ,CAACC,EAAE,KAAKL,UAAU,GAAGC,eAAe,GAAGG,QACjD,CACF,CAAC;MAED,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA,IAAAwB,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAxB,KAAK,CAAC2B,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhB,IAAI,cAAAiB,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,MAAM;MAC5D3B,QAAQ,CAACyB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE0B;MAAa,CAAC;IAChD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAG,MAAOZ,UAAU,IAAK;IACzC,IAAI;MACF,MAAMC,eAAe,GAAG,MAAMrC,mBAAmB,CAACgD,YAAY,CAACZ,UAAU,CAAC;MAE1EtB,YAAY,CAACwB,IAAI,IACfA,IAAI,CAACC,GAAG,CAACC,QAAQ,IACfA,QAAQ,CAACC,EAAE,KAAKL,UAAU,GAAGC,eAAe,GAAGG,QACjD,CACF,CAAC;MAED,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA9B,KAAK,CAAC2B,QAAQ,cAAAG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtB,IAAI,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAI,MAAM;MAC5D3B,QAAQ,CAACyB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE0B;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFjC,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMkC,OAAO,GAAG,MAAMpD,mBAAmB,CAACmD,YAAY,CAAC,CAAC;;MAExD;MACArC,YAAY,CAACwB,IAAI,IACfA,IAAI,CAACC,GAAG,CAACC,QAAQ,IAAI;QACnB,MAAMa,MAAM,GAAGD,OAAO,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKD,QAAQ,CAACC,EAAE,CAAC;QACtD,OAAOY,MAAM,GAAG;UAAE,GAAGb,QAAQ;UAAEgB,SAAS,EAAE,IAAI;UAAEC,aAAa,EAAEJ,MAAM,CAACK;QAAQ,CAAC,GAAGlB,QAAQ;MAC5F,CAAC,CACH,CAAC;MAED,OAAO;QAAEE,OAAO,EAAE,IAAI;QAAEU;MAAQ,CAAC;IACnC,CAAC,CAAC,OAAOjC,KAAK,EAAE;MAAA,IAAAwC,gBAAA,EAAAC,qBAAA;MACd,MAAMf,YAAY,GAAG,EAAAc,gBAAA,GAAAxC,KAAK,CAAC2B,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI,QAAQ;MAC9D3B,QAAQ,CAACyB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE0B;MAAa,CAAC;IAChD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2C,cAAc,GAAG,MAAOzB,UAAU,IAAK;IAC3C,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMmB,eAAe,GAAG,MAAMrC,mBAAmB,CAAC6D,cAAc,CAACzB,UAAU,CAAC;MAE5EtB,YAAY,CAACwB,IAAI,IACfA,IAAI,CAACC,GAAG,CAACC,QAAQ,IACfA,QAAQ,CAACC,EAAE,KAAKL,UAAU,GAAGC,eAAe,GAAGG,QACjD,CACF,CAAC;MAED,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA,IAAA2C,gBAAA,EAAAC,qBAAA;MACd,MAAMlB,YAAY,GAAG,EAAAiB,gBAAA,GAAA3C,KAAK,CAAC2B,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBhB,OAAO,KAAI,MAAM;MAC5D3B,QAAQ,CAACyB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE0B;MAAa,CAAC;IAChD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8C,cAAc,GAAG,MAAO5B,UAAU,IAAK;IAC3C,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM+C,gBAAgB,GAAG,MAAMjE,mBAAmB,CAACgE,cAAc,CAAC5B,UAAU,CAAC;MAE7EtB,YAAY,CAACwB,IAAI,IACfA,IAAI,CAACC,GAAG,CAACC,QAAQ,IACfA,QAAQ,CAACC,EAAE,KAAKL,UAAU,GAAG6B,gBAAgB,GAAGzB,QAClD,CACF,CAAC;MAED,OAAO;QAAEE,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MAAA,IAAA+C,gBAAA,EAAAC,qBAAA;MACd,MAAMtB,YAAY,GAAG,EAAAqB,gBAAA,GAAA/C,KAAK,CAAC2B,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvC,IAAI,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBpB,OAAO,KAAI,MAAM;MAC5D3B,QAAQ,CAACyB,YAAY,CAAC;MACtB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE0B;MAAa,CAAC;IAChD,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGvD,SAAS,CAACQ,MAAM,CAACmB,QAAQ,IAAI;IACrD;IACA,IAAInB,MAAM,CAACE,QAAQ,KAAK,KAAK,IAAIiB,QAAQ,CAACjB,QAAQ,KAAKF,MAAM,CAACE,QAAQ,EAAE;MACtE,OAAO,KAAK;IACd;;IAEA;IACA,IAAIF,MAAM,CAACG,MAAM,KAAK,WAAW,IAAI,CAACgB,QAAQ,CAAC6B,SAAS,EAAE;MACxD,OAAO,KAAK;IACd;IACA,IAAIhD,MAAM,CAACG,MAAM,KAAK,WAAW,IAAIgB,QAAQ,CAAC6B,SAAS,EAAE;MACvD,OAAO,KAAK;IACd;;IAEA;IACA,IAAIhD,MAAM,CAACI,MAAM,IAAI,CAACe,QAAQ,CAAC8B,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,MAAM,CAACI,MAAM,CAAC8C,WAAW,CAAC,CAAC,CAAC,EAAE;MACvF,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,KAAK,GAAG7D,SAAS,CAAC8D,MAAM;IAC9B,MAAMN,SAAS,GAAGxD,SAAS,CAACQ,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAACP,SAAS,CAAC,CAACM,MAAM;IAC3D,MAAME,OAAO,GAAGhE,SAAS,CAACQ,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAACP,SAAS,IAAIO,CAAC,CAACC,OAAO,CAAC,CAACF,MAAM;IACtE,MAAMG,UAAU,GAAGjE,SAAS,CAACQ,MAAM,CAACuD,CAAC,IAAIA,CAAC,CAACpB,SAAS,CAAC,CAACmB,MAAM;IAE5D,OAAO;MAAED,KAAK;MAAEL,SAAS;MAAEQ,OAAO;MAAEC;IAAW,CAAC;EAClD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB3D,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM4D,KAAK,GAAG;IACZnE,SAAS,EAAEuD,iBAAiB;IAC5Ba,YAAY,EAAEpE,SAAS;IACvBE,UAAU;IACVE,OAAO;IACPE,KAAK;IACLE,MAAM;IACNC,SAAS;IACTI,cAAc;IACdS,aAAa;IACba,YAAY;IACZG,YAAY;IACZU,cAAc;IACdG,cAAc;IACdS,QAAQ;IACRM;EACF,CAAC;EAED,oBACE5E,OAAA,CAACC,mBAAmB,CAAC8E,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAtE,QAAA,EACxCA;EAAQ;IAAAyE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACmB,CAAC;AAEnC,CAAC;AAAC3E,GAAA,CAzOWF,oBAAoB;EAAA,QACdR,OAAO;AAAA;AAAAsF,EAAA,GADb9E,oBAAoB;AAAA,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}