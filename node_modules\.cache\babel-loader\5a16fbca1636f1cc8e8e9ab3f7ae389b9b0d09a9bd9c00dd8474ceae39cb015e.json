{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n  const distanceFromOrigin = point - originPoint;\n  const scaled = scale * distanceFromOrigin;\n  return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n  if (boxScale !== undefined) {\n    point = scalePoint(point, boxScale, originPoint);\n  }\n  return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n  axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n  axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, {\n  x,\n  y\n}) {\n  applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n  applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n  const treeLength = treePath.length;\n  if (!treeLength) return;\n  // Reset the treeScale\n  treeScale.x = treeScale.y = 1;\n  let node;\n  let delta;\n  for (let i = 0; i < treeLength; i++) {\n    node = treePath[i];\n    delta = node.projectionDelta;\n    /**\n     * TODO: Prefer to remove this, but currently we have motion components with\n     * display: contents in Framer.\n     */\n    const instance = node.instance;\n    if (instance && instance.style && instance.style.display === \"contents\") {\n      continue;\n    }\n    if (isSharedTransition && node.options.layoutScroll && node.scroll && node !== node.root) {\n      transformBox(box, {\n        x: -node.scroll.offset.x,\n        y: -node.scroll.offset.y\n      });\n    }\n    if (delta) {\n      // Incoporate each ancestor's scale into a culmulative treeScale for this component\n      treeScale.x *= delta.x.scale;\n      treeScale.y *= delta.y.scale;\n      // Apply each ancestor's calculated delta into this component's recorded layout box\n      applyBoxDelta(box, delta);\n    }\n    if (isSharedTransition && hasTransform(node.latestValues)) {\n      transformBox(box, node.latestValues);\n    }\n  }\n  /**\n   * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n   * This will help reduce useless scales getting rendered.\n   */\n  treeScale.x = snapToDefault(treeScale.x);\n  treeScale.y = snapToDefault(treeScale.y);\n}\nfunction snapToDefault(scale) {\n  if (Number.isInteger(scale)) return scale;\n  return scale > 1.0000000000001 || scale < 0.999999999999 ? scale : 1;\n}\nfunction translateAxis(axis, distance) {\n  axis.min = axis.min + distance;\n  axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, transforms, [key, scaleKey, originKey]) {\n  const axisOrigin = transforms[originKey] !== undefined ? transforms[originKey] : 0.5;\n  const originPoint = mix(axis.min, axis.max, axisOrigin);\n  // Apply the axis delta to the final axis\n  applyAxisDelta(axis, transforms[key], transforms[scaleKey], originPoint, transforms.scale);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n  transformAxis(box.x, transform, xKeys);\n  transformAxis(box.y, transform, yKeys);\n}\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };", "map": {"version": 3, "names": ["mix", "hasTransform", "scalePoint", "point", "scale", "originPoint", "distanceFromOrigin", "scaled", "applyPointDelta", "translate", "boxScale", "undefined", "applyAxis<PERSON><PERSON><PERSON>", "axis", "min", "max", "applyBoxDelta", "box", "x", "y", "applyTreeDeltas", "treeScale", "treePath", "isSharedTransition", "tree<PERSON>ength", "length", "node", "delta", "i", "projectionDel<PERSON>", "instance", "style", "display", "options", "layoutScroll", "scroll", "root", "transformBox", "offset", "latestValues", "snapToDefault", "Number", "isInteger", "translateAxis", "distance", "transformAxis", "transforms", "key", "scaleKey", "<PERSON><PERSON><PERSON>", "axisOrigin", "xKeys", "y<PERSON><PERSON><PERSON>", "transform"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const instance = node.instance;\n        if (instance &&\n            instance.style &&\n            instance.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && hasTransform(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    treeScale.x = snapToDefault(treeScale.x);\n    treeScale.y = snapToDefault(treeScale.y);\n}\nfunction snapToDefault(scale) {\n    if (Number.isInteger(scale))\n        return scale;\n    return scale > 1.0000000000001 || scale < 0.999999999999 ? scale : 1;\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, transforms, [key, scaleKey, originKey]) {\n    const axisOrigin = transforms[originKey] !== undefined ? transforms[originKey] : 0.5;\n    const originPoint = mix(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, transforms[key], transforms[scaleKey], originPoint, transforms.scale);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform, xKeys);\n    transformAxis(box.y, transform, yKeys);\n}\n\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AACzC,SAASC,YAAY,QAAQ,4BAA4B;;AAEzD;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,EAAE;EAC3C,MAAMC,kBAAkB,GAAGH,KAAK,GAAGE,WAAW;EAC9C,MAAME,MAAM,GAAGH,KAAK,GAAGE,kBAAkB;EACzC,OAAOD,WAAW,GAAGE,MAAM;AAC/B;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACL,KAAK,EAAEM,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,EAAE;EACrE,IAAIA,QAAQ,KAAKC,SAAS,EAAE;IACxBR,KAAK,GAAGD,UAAU,CAACC,KAAK,EAAEO,QAAQ,EAAEL,WAAW,CAAC;EACpD;EACA,OAAOH,UAAU,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,CAAC,GAAGI,SAAS;AAC5D;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAACC,IAAI,EAAEJ,SAAS,GAAG,CAAC,EAAEL,KAAK,GAAG,CAAC,EAAEC,WAAW,EAAEK,QAAQ,EAAE;EAC3EG,IAAI,CAACC,GAAG,GAAGN,eAAe,CAACK,IAAI,CAACC,GAAG,EAAEL,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,CAAC;EAC7EG,IAAI,CAACE,GAAG,GAAGP,eAAe,CAACK,IAAI,CAACE,GAAG,EAAEN,SAAS,EAAEL,KAAK,EAAEC,WAAW,EAAEK,QAAQ,CAAC;AACjF;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAACC,GAAG,EAAE;EAAEC,CAAC;EAAEC;AAAE,CAAC,EAAE;EAClCP,cAAc,CAACK,GAAG,CAACC,CAAC,EAAEA,CAAC,CAACT,SAAS,EAAES,CAAC,CAACd,KAAK,EAAEc,CAAC,CAACb,WAAW,CAAC;EAC1DO,cAAc,CAACK,GAAG,CAACE,CAAC,EAAEA,CAAC,CAACV,SAAS,EAAEU,CAAC,CAACf,KAAK,EAAEe,CAAC,CAACd,WAAW,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,eAAeA,CAACH,GAAG,EAAEI,SAAS,EAAEC,QAAQ,EAAEC,kBAAkB,GAAG,KAAK,EAAE;EAC3E,MAAMC,UAAU,GAAGF,QAAQ,CAACG,MAAM;EAClC,IAAI,CAACD,UAAU,EACX;EACJ;EACAH,SAAS,CAACH,CAAC,GAAGG,SAAS,CAACF,CAAC,GAAG,CAAC;EAC7B,IAAIO,IAAI;EACR,IAAIC,KAAK;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,UAAU,EAAEI,CAAC,EAAE,EAAE;IACjCF,IAAI,GAAGJ,QAAQ,CAACM,CAAC,CAAC;IAClBD,KAAK,GAAGD,IAAI,CAACG,eAAe;IAC5B;AACR;AACA;AACA;IACQ,MAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IAC9B,IAAIA,QAAQ,IACRA,QAAQ,CAACC,KAAK,IACdD,QAAQ,CAACC,KAAK,CAACC,OAAO,KAAK,UAAU,EAAE;MACvC;IACJ;IACA,IAAIT,kBAAkB,IAClBG,IAAI,CAACO,OAAO,CAACC,YAAY,IACzBR,IAAI,CAACS,MAAM,IACXT,IAAI,KAAKA,IAAI,CAACU,IAAI,EAAE;MACpBC,YAAY,CAACpB,GAAG,EAAE;QACdC,CAAC,EAAE,CAACQ,IAAI,CAACS,MAAM,CAACG,MAAM,CAACpB,CAAC;QACxBC,CAAC,EAAE,CAACO,IAAI,CAACS,MAAM,CAACG,MAAM,CAACnB;MAC3B,CAAC,CAAC;IACN;IACA,IAAIQ,KAAK,EAAE;MACP;MACAN,SAAS,CAACH,CAAC,IAAIS,KAAK,CAACT,CAAC,CAACd,KAAK;MAC5BiB,SAAS,CAACF,CAAC,IAAIQ,KAAK,CAACR,CAAC,CAACf,KAAK;MAC5B;MACAY,aAAa,CAACC,GAAG,EAAEU,KAAK,CAAC;IAC7B;IACA,IAAIJ,kBAAkB,IAAItB,YAAY,CAACyB,IAAI,CAACa,YAAY,CAAC,EAAE;MACvDF,YAAY,CAACpB,GAAG,EAAES,IAAI,CAACa,YAAY,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACIlB,SAAS,CAACH,CAAC,GAAGsB,aAAa,CAACnB,SAAS,CAACH,CAAC,CAAC;EACxCG,SAAS,CAACF,CAAC,GAAGqB,aAAa,CAACnB,SAAS,CAACF,CAAC,CAAC;AAC5C;AACA,SAASqB,aAAaA,CAACpC,KAAK,EAAE;EAC1B,IAAIqC,MAAM,CAACC,SAAS,CAACtC,KAAK,CAAC,EACvB,OAAOA,KAAK;EAChB,OAAOA,KAAK,GAAG,eAAe,IAAIA,KAAK,GAAG,cAAc,GAAGA,KAAK,GAAG,CAAC;AACxE;AACA,SAASuC,aAAaA,CAAC9B,IAAI,EAAE+B,QAAQ,EAAE;EACnC/B,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACC,GAAG,GAAG8B,QAAQ;EAC9B/B,IAAI,CAACE,GAAG,GAAGF,IAAI,CAACE,GAAG,GAAG6B,QAAQ;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAChC,IAAI,EAAEiC,UAAU,EAAE,CAACC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAAC,EAAE;EACjE,MAAMC,UAAU,GAAGJ,UAAU,CAACG,SAAS,CAAC,KAAKtC,SAAS,GAAGmC,UAAU,CAACG,SAAS,CAAC,GAAG,GAAG;EACpF,MAAM5C,WAAW,GAAGL,GAAG,CAACa,IAAI,CAACC,GAAG,EAAED,IAAI,CAACE,GAAG,EAAEmC,UAAU,CAAC;EACvD;EACAtC,cAAc,CAACC,IAAI,EAAEiC,UAAU,CAACC,GAAG,CAAC,EAAED,UAAU,CAACE,QAAQ,CAAC,EAAE3C,WAAW,EAAEyC,UAAU,CAAC1C,KAAK,CAAC;AAC9F;AACA;AACA;AACA;AACA,MAAM+C,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC;AACA;AACA;AACA,SAASf,YAAYA,CAACpB,GAAG,EAAEoC,SAAS,EAAE;EAClCR,aAAa,CAAC5B,GAAG,CAACC,CAAC,EAAEmC,SAAS,EAAEF,KAAK,CAAC;EACtCN,aAAa,CAAC5B,GAAG,CAACE,CAAC,EAAEkC,SAAS,EAAED,KAAK,CAAC;AAC1C;AAEA,SAASxC,cAAc,EAAEI,aAAa,EAAER,eAAe,EAAEY,eAAe,EAAElB,UAAU,EAAE2C,aAAa,EAAER,YAAY,EAAEM,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}