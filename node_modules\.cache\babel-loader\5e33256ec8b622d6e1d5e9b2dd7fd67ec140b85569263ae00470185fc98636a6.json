{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from './context/AuthContext';\n\n// 页面组件\nimport LoginPage from './pages/LoginPage';\nimport HomePage from './pages/HomePage';\nimport ModulesPage from './pages/ModulesPage';\nimport SettingsPage from './pages/SettingsPage';\nimport StorePage from './pages/StorePage';\n\n// 布局组件\nimport Layout from './components/layout/Layout';\nimport TitleBar from './components/layout/TitleBar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  overflow: hidden;\n`;\n_c = AppContainer;\nconst MainContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c2 = MainContent;\nfunction App() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          color: '#ff6b35',\n          fontSize: '16px'\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TitleBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: user ? /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 40\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/modules\",\n            element: /*#__PURE__*/_jsxDEV(ModulesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/store\",\n            element: /*#__PURE__*/_jsxDEV(StorePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 40\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "styled", "useAuth", "LoginPage", "HomePage", "ModulesPage", "SettingsPage", "StorePage", "Layout", "TitleBar", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "MainContent", "_c2", "App", "_s", "user", "loading", "children", "style", "display", "alignItems", "justifyContent", "height", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c3", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from './context/AuthContext';\n\n// 页面组件\nimport LoginPage from './pages/LoginPage';\nimport HomePage from './pages/HomePage';\nimport ModulesPage from './pages/ModulesPage';\nimport SettingsPage from './pages/SettingsPage';\nimport StorePage from './pages/StorePage';\n\n// 布局组件\nimport Layout from './components/layout/Layout';\nimport TitleBar from './components/layout/TitleBar';\n\nconst AppContainer = styled.div`\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  overflow: hidden;\n`;\n\nconst MainContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nfunction App() {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <AppContainer>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          color: '#ff6b35',\n          fontSize: '16px'\n        }}>\n          正在加载...\n        </div>\n      </AppContainer>\n    );\n  }\n\n  return (\n    <AppContainer>\n      <TitleBar />\n      <MainContent>\n        {user ? (\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<HomePage />} />\n              <Route path=\"/modules\" element={<ModulesPage />} />\n              <Route path=\"/store\" element={<StorePage />} />\n              <Route path=\"/settings\" element={<SettingsPage />} />\n              <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n            </Routes>\n          </Layout>\n        ) : (\n          <Routes>\n            <Route path=\"/login\" element={<LoginPage />} />\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n          </Routes>\n        )}\n      </MainContent>\n    </AppContainer>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,uBAAuB;;AAE/C;AACA,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;;AAEzC;AACA,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,QAAQ,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,YAAY,GAAGX,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,YAAY;AASlB,MAAMG,WAAW,GAAGd,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAJID,WAAW;AAMjB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAEnC,IAAIkB,OAAO,EAAE;IACX,oBACET,OAAA,CAACC,YAAY;MAAAS,QAAA,eACXV,OAAA;QAAKW,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,MAAM;UACdC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE;QACZ,CAAE;QAAAP,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAEnB;EAEA,oBACErB,OAAA,CAACC,YAAY;IAAAS,QAAA,gBACXV,OAAA,CAACF,QAAQ;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZrB,OAAA,CAACI,WAAW;MAAAM,QAAA,EACTF,IAAI,gBACHR,OAAA,CAACH,MAAM;QAAAa,QAAA,eACLV,OAAA,CAACb,MAAM;UAAAuB,QAAA,gBACLV,OAAA,CAACZ,KAAK;YAACkC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEvB,OAAA,CAACP,QAAQ;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCrB,OAAA,CAACZ,KAAK;YAACkC,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEvB,OAAA,CAACN,WAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDrB,OAAA,CAACZ,KAAK;YAACkC,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEvB,OAAA,CAACJ,SAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CrB,OAAA,CAACZ,KAAK;YAACkC,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEvB,OAAA,CAACL,YAAY;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDrB,OAAA,CAACZ,KAAK;YAACkC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEvB,OAAA,CAACX,QAAQ;cAACmC,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAETrB,OAAA,CAACb,MAAM;QAAAuB,QAAA,gBACLV,OAAA,CAACZ,KAAK;UAACkC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEvB,OAAA,CAACR,SAAS;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CrB,OAAA,CAACZ,KAAK;UAACkC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEvB,OAAA,CAACX,QAAQ;YAACmC,EAAE,EAAC,QAAQ;YAACC,OAAO;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACd,EAAA,CA3CQD,GAAG;EAAA,QACgBf,OAAO;AAAA;AAAAmC,GAAA,GAD1BpB,GAAG;AA6CZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAxB,EAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}