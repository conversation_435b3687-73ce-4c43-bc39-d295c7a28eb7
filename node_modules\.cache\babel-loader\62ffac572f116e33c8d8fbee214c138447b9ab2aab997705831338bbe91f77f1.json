{"ast": null, "code": "import { circOut } from '../../easing/circ.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { percent, px } from '../../value/types/numbers/units.mjs';\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = value => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = value => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n  if (shouldCrossfadeOpacity) {\n    target.opacity = mix(0,\n    // TODO Reinstate this if only child\n    lead.opacity !== undefined ? lead.opacity : 1, easeCrossfadeIn(progress));\n    target.opacityExit = mix(follow.opacity !== undefined ? follow.opacity : 1, 0, easeCrossfadeOut(progress));\n  } else if (isOnlyMember) {\n    target.opacity = mix(follow.opacity !== undefined ? follow.opacity : 1, lead.opacity !== undefined ? lead.opacity : 1, progress);\n  }\n  /**\n   * Mix border radius\n   */\n  for (let i = 0; i < numBorders; i++) {\n    const borderLabel = `border${borders[i]}Radius`;\n    let followRadius = getRadius(follow, borderLabel);\n    let leadRadius = getRadius(lead, borderLabel);\n    if (followRadius === undefined && leadRadius === undefined) continue;\n    followRadius || (followRadius = 0);\n    leadRadius || (leadRadius = 0);\n    const canMix = followRadius === 0 || leadRadius === 0 || isPx(followRadius) === isPx(leadRadius);\n    if (canMix) {\n      target[borderLabel] = Math.max(mix(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n      if (percent.test(leadRadius) || percent.test(followRadius)) {\n        target[borderLabel] += \"%\";\n      }\n    } else {\n      target[borderLabel] = leadRadius;\n    }\n  }\n  /**\n   * Mix rotation\n   */\n  if (follow.rotate || lead.rotate) {\n    target.rotate = mix(follow.rotate || 0, lead.rotate || 0, progress);\n  }\n}\nfunction getRadius(values, radiusName) {\n  return values[radiusName] !== undefined ? values[radiusName] : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = compress(0, 0.5, circOut);\nconst easeCrossfadeOut = compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n  return p => {\n    // Could replace ifs with clamp\n    if (p < min) return 0;\n    if (p > max) return 1;\n    return easing(progress(min, max, p));\n  };\n}\nexport { mixValues };", "map": {"version": 3, "names": ["circOut", "progress", "mix", "noop", "percent", "px", "borders", "numBorders", "length", "asNumber", "value", "parseFloat", "isPx", "test", "mixValues", "target", "follow", "lead", "shouldCrossfadeOpacity", "isOnlyMember", "opacity", "undefined", "easeCrossfadeIn", "opacityExit", "easeCrossfadeOut", "i", "borderLabel", "followRadius", "getRadius", "leadRadius", "canMix", "Math", "max", "rotate", "values", "radiusName", "borderRadius", "compress", "min", "easing", "p"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs"], "sourcesContent": ["import { circOut } from '../../easing/circ.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { percent, px } from '../../value/types/numbers/units.mjs';\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = mix(0, \n        // TODO Reinstate this if only child\n        lead.opacity !== undefined ? lead.opacity : 1, easeCrossfadeIn(progress));\n        target.opacityExit = mix(follow.opacity !== undefined ? follow.opacity : 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = mix(follow.opacity !== undefined ? follow.opacity : 1, lead.opacity !== undefined ? lead.opacity : 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max(mix(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (percent.test(leadRadius) || percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = mix(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = compress(0, 0.5, circOut);\nconst easeCrossfadeOut = compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing(progress(min, max, p));\n    };\n}\n\nexport { mixValues };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,OAAO,EAAEC,EAAE,QAAQ,qCAAqC;AAEjE,MAAMC,OAAO,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC;AACpE,MAAMC,UAAU,GAAGD,OAAO,CAACE,MAAM;AACjC,MAAMC,QAAQ,GAAIC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACD,KAAK,CAAC,GAAGA,KAAK;AACjF,MAAME,IAAI,GAAIF,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIL,EAAE,CAACQ,IAAI,CAACH,KAAK,CAAC;AACnE,SAASI,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEhB,QAAQ,EAAEiB,sBAAsB,EAAEC,YAAY,EAAE;EACrF,IAAID,sBAAsB,EAAE;IACxBH,MAAM,CAACK,OAAO,GAAGlB,GAAG,CAAC,CAAC;IACtB;IACAe,IAAI,CAACG,OAAO,KAAKC,SAAS,GAAGJ,IAAI,CAACG,OAAO,GAAG,CAAC,EAAEE,eAAe,CAACrB,QAAQ,CAAC,CAAC;IACzEc,MAAM,CAACQ,WAAW,GAAGrB,GAAG,CAACc,MAAM,CAACI,OAAO,KAAKC,SAAS,GAAGL,MAAM,CAACI,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEI,gBAAgB,CAACvB,QAAQ,CAAC,CAAC;EAC9G,CAAC,MACI,IAAIkB,YAAY,EAAE;IACnBJ,MAAM,CAACK,OAAO,GAAGlB,GAAG,CAACc,MAAM,CAACI,OAAO,KAAKC,SAAS,GAAGL,MAAM,CAACI,OAAO,GAAG,CAAC,EAAEH,IAAI,CAACG,OAAO,KAAKC,SAAS,GAAGJ,IAAI,CAACG,OAAO,GAAG,CAAC,EAAEnB,QAAQ,CAAC;EACpI;EACA;AACJ;AACA;EACI,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,UAAU,EAAEkB,CAAC,EAAE,EAAE;IACjC,MAAMC,WAAW,GAAG,SAASpB,OAAO,CAACmB,CAAC,CAAC,QAAQ;IAC/C,IAAIE,YAAY,GAAGC,SAAS,CAACZ,MAAM,EAAEU,WAAW,CAAC;IACjD,IAAIG,UAAU,GAAGD,SAAS,CAACX,IAAI,EAAES,WAAW,CAAC;IAC7C,IAAIC,YAAY,KAAKN,SAAS,IAAIQ,UAAU,KAAKR,SAAS,EACtD;IACJM,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC;IAClCE,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC;IAC9B,MAAMC,MAAM,GAAGH,YAAY,KAAK,CAAC,IAC7BE,UAAU,KAAK,CAAC,IAChBjB,IAAI,CAACe,YAAY,CAAC,KAAKf,IAAI,CAACiB,UAAU,CAAC;IAC3C,IAAIC,MAAM,EAAE;MACRf,MAAM,CAACW,WAAW,CAAC,GAAGK,IAAI,CAACC,GAAG,CAAC9B,GAAG,CAACO,QAAQ,CAACkB,YAAY,CAAC,EAAElB,QAAQ,CAACoB,UAAU,CAAC,EAAE5B,QAAQ,CAAC,EAAE,CAAC,CAAC;MAC9F,IAAIG,OAAO,CAACS,IAAI,CAACgB,UAAU,CAAC,IAAIzB,OAAO,CAACS,IAAI,CAACc,YAAY,CAAC,EAAE;QACxDZ,MAAM,CAACW,WAAW,CAAC,IAAI,GAAG;MAC9B;IACJ,CAAC,MACI;MACDX,MAAM,CAACW,WAAW,CAAC,GAAGG,UAAU;IACpC;EACJ;EACA;AACJ;AACA;EACI,IAAIb,MAAM,CAACiB,MAAM,IAAIhB,IAAI,CAACgB,MAAM,EAAE;IAC9BlB,MAAM,CAACkB,MAAM,GAAG/B,GAAG,CAACc,MAAM,CAACiB,MAAM,IAAI,CAAC,EAAEhB,IAAI,CAACgB,MAAM,IAAI,CAAC,EAAEhC,QAAQ,CAAC;EACvE;AACJ;AACA,SAAS2B,SAASA,CAACM,MAAM,EAAEC,UAAU,EAAE;EACnC,OAAOD,MAAM,CAACC,UAAU,CAAC,KAAKd,SAAS,GACjCa,MAAM,CAACC,UAAU,CAAC,GAClBD,MAAM,CAACE,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMd,eAAe,GAAGe,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAErC,OAAO,CAAC;AACjD,MAAMwB,gBAAgB,GAAGa,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAElC,IAAI,CAAC;AAClD,SAASkC,QAAQA,CAACC,GAAG,EAAEN,GAAG,EAAEO,MAAM,EAAE;EAChC,OAAQC,CAAC,IAAK;IACV;IACA,IAAIA,CAAC,GAAGF,GAAG,EACP,OAAO,CAAC;IACZ,IAAIE,CAAC,GAAGR,GAAG,EACP,OAAO,CAAC;IACZ,OAAOO,MAAM,CAACtC,QAAQ,CAACqC,GAAG,EAAEN,GAAG,EAAEQ,CAAC,CAAC,CAAC;EACxC,CAAC;AACL;AAEA,SAAS1B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}