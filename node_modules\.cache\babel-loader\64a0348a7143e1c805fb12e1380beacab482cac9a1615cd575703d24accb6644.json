{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n_c = SidebarContainer;\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c2 = SidebarHeader;\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c3 = Logo;\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c4 = LogoText;\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n_c5 = Navigation;\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n_c6 = NavItem;\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n_c7 = NavLink;\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n_c8 = NavIcon;\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c9 = NavText;\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n_c0 = NavBadge;\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n_c1 = SidebarFooter;\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c10 = UserProfile;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c11 = UserAvatar;\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n_c12 = UserInfo;\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n_c13 = UserName;\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n_c14 = UserStatus;\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n_c15 = ToggleButton;\nconst Sidebar = ({\n  collapsed,\n  onToggle\n}) => {\n  _s();\n  var _user$username;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    getStats\n  } = useGameResources();\n  const stats = getStats();\n  const navigationItems = [{\n    path: '/',\n    icon: '🏠',\n    label: '主页',\n    badge: null\n  }, {\n    path: '/modules',\n    icon: '📦',\n    label: '游戏文件',\n    badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n  }, {\n    path: '/store',\n    icon: '🛒',\n    label: '试玩和购买',\n    badge: null\n  }, {\n    path: '/settings',\n    icon: '⚙️',\n    label: '系统选项',\n    badge: null\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  const handleUserClick = () => {\n    // 可以打开用户菜单或导航到用户设置页面\n    console.log('User profile clicked');\n  };\n  return /*#__PURE__*/_jsxDEV(SidebarContainer, {\n    collapsed: collapsed,\n    initial: {\n      x: -240\n    },\n    animate: {\n      x: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        collapsed: collapsed,\n        children: \"DCS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n        whileHover: {\n          scale: 1.02\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        children: /*#__PURE__*/_jsxDEV(NavLink, {\n          active: location.pathname === item.path,\n          onClick: () => handleNavigation(item.path),\n          children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavText, {\n            collapsed: collapsed,\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), item.badge && /*#__PURE__*/_jsxDEV(NavBadge, {\n            collapsed: collapsed,\n            children: item.badge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)\n      }, item.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidebarFooter, {\n      children: /*#__PURE__*/_jsxDEV(UserProfile, {\n        onClick: handleUserClick,\n        children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n          children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          collapsed: collapsed,\n          children: [/*#__PURE__*/_jsxDEV(UserName, {\n            children: (user === null || user === void 0 ? void 0 : user.username) || 'Guest'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserStatus, {\n            children: \"\\u5728\\u7EBF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n      onClick: onToggle,\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: collapsed ? '→' : '←'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"f/TFxXB2hVOLSqwg3WD3c2HKcaQ=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useGameResources];\n});\n_c16 = Sidebar;\nexport default Sidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"SidebarContainer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"Navigation\");\n$RefreshReg$(_c6, \"NavItem\");\n$RefreshReg$(_c7, \"NavLink\");\n$RefreshReg$(_c8, \"NavIcon\");\n$RefreshReg$(_c9, \"NavText\");\n$RefreshReg$(_c0, \"NavBadge\");\n$RefreshReg$(_c1, \"SidebarFooter\");\n$RefreshReg$(_c10, \"UserProfile\");\n$RefreshReg$(_c11, \"UserAvatar\");\n$RefreshReg$(_c12, \"UserInfo\");\n$RefreshReg$(_c13, \"UserName\");\n$RefreshReg$(_c14, \"UserStatus\");\n$RefreshReg$(_c15, \"ToggleButton\");\n$RefreshReg$(_c16, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useLocation", "useNavigate", "styled", "motion", "useAuth", "useGameResources", "jsxDEV", "_jsxDEV", "SidebarContainer", "aside", "props", "collapsed", "theme", "components", "sidebar", "collapsedWidth", "width", "colors", "border", "primary", "_c", "SidebarHeader", "div", "secondary", "_c2", "Logo", "_c3", "LogoText", "fontSizes", "lg", "fontWeights", "bold", "text", "_c4", "Navigation", "nav", "_c5", "NavItem", "_c6", "NavLink", "button", "active", "sm", "medium", "normal", "_c7", "NavIcon", "span", "_c8", "NavText", "_c9", "NavBadge", "_c0", "SidebarFooter", "_c1", "UserProfile", "_c10", "UserAvatar", "_c11", "UserInfo", "_c12", "UserName", "_c13", "UserStatus", "xs", "tertiary", "_c14", "ToggleButton", "background", "_c15", "Sidebar", "onToggle", "_s", "_user$username", "location", "navigate", "user", "logout", "getStats", "stats", "navigationItems", "path", "icon", "label", "badge", "hasUpdates", "handleNavigation", "handleUserClick", "console", "log", "initial", "x", "animate", "transition", "duration", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "whileHover", "scale", "whileTap", "pathname", "onClick", "username", "char<PERSON>t", "toUpperCase", "_c16", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/components/layout/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\n\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n\nconst Sidebar = ({ collapsed, onToggle }) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { getStats } = useGameResources();\n  \n  const stats = getStats();\n\n  const navigationItems = [\n    {\n      path: '/',\n      icon: '🏠',\n      label: '主页',\n      badge: null\n    },\n    {\n      path: '/modules',\n      icon: '📦',\n      label: '游戏文件',\n      badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n    },\n    {\n      path: '/store',\n      icon: '🛒',\n      label: '试玩和购买',\n      badge: null\n    },\n    {\n      path: '/settings',\n      icon: '⚙️',\n      label: '系统选项',\n      badge: null\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  const handleUserClick = () => {\n    // 可以打开用户菜单或导航到用户设置页面\n    console.log('User profile clicked');\n  };\n\n  return (\n    <SidebarContainer\n      collapsed={collapsed}\n      initial={{ x: -240 }}\n      animate={{ x: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <SidebarHeader>\n        <Logo>D</Logo>\n        <LogoText collapsed={collapsed}>DCS</LogoText>\n      </SidebarHeader>\n\n      <Navigation>\n        {navigationItems.map((item) => (\n          <NavItem\n            key={item.path}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <NavLink\n              active={location.pathname === item.path}\n              onClick={() => handleNavigation(item.path)}\n            >\n              <NavIcon>{item.icon}</NavIcon>\n              <NavText collapsed={collapsed}>{item.label}</NavText>\n              {item.badge && (\n                <NavBadge collapsed={collapsed}>{item.badge}</NavBadge>\n              )}\n            </NavLink>\n          </NavItem>\n        ))}\n      </Navigation>\n\n      <SidebarFooter>\n        <UserProfile onClick={handleUserClick}>\n          <UserAvatar>\n            {user?.username?.charAt(0).toUpperCase() || 'U'}\n          </UserAvatar>\n          <UserInfo collapsed={collapsed}>\n            <UserName>{user?.username || 'Guest'}</UserName>\n            <UserStatus>在线</UserStatus>\n          </UserInfo>\n        </UserProfile>\n      </SidebarFooter>\n\n      <ToggleButton\n        onClick={onToggle}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {collapsed ? '→' : '←'}\n      </ToggleButton>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,gBAAgB,GAAGN,MAAM,CAACC,MAAM,CAACM,KAAK,CAAC;AAC7C,WAAWC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACC,cAAc,GAAGL,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACE,KAAK;AAC1H;AACA;AACA,4BAA4BN,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AACtE;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIZ,gBAAgB;AAWtB,MAAMa,aAAa,GAAGnB,MAAM,CAACoB,GAAG;AAChC;AACA,6BAA6BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACzE;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,aAAa;AAQnB,MAAMI,IAAI,GAAGvB,MAAM,CAACoB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAZID,IAAI;AAcV,MAAME,QAAQ,GAAGzB,MAAM,CAACoB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACC,EAAE;AAChD,iBAAiBnB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACC,IAAI;AACtD,WAAWrB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GARIN,QAAQ;AAUd,MAAMO,UAAU,GAAGhC,MAAM,CAACiC,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,OAAO,GAAGnC,MAAM,CAACC,MAAM,CAACmB,GAAG,CAAC;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,OAAO;AAMb,MAAME,OAAO,GAAGrC,MAAM,CAACsC,MAAM;AAC7B;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG,yBAAyB,GAAG,aAAa;AACjF;AACA,2BAA2B/B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAG,aAAa;AAC7F,WAAWT,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAGT,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACjG,eAAeb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM,GAAGjC,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACc,MAAM;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAalC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACrD;AACA,CAAC;AAAC0B,GAAA,GApBIN,OAAO;AAsBb,MAAMO,OAAO,GAAG5C,MAAM,CAAC6C,IAAI;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,OAAO;AAOb,MAAMG,OAAO,GAAG/C,MAAM,CAAC6C,IAAI;AAC3B,aAAarC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA,CAAC;AAACuC,GAAA,GALID,OAAO;AAOb,MAAME,QAAQ,GAAGjD,MAAM,CAAC6C,IAAI;AAC5B,gBAAgBrC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA,CAAC;AAACyC,GAAA,GATID,QAAQ;AAWd,MAAME,aAAa,GAAGnD,MAAM,CAACoB,GAAG;AAChC;AACA,0BAA0BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACtE,CAAC;AAAC+B,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGrD,MAAM,CAACoB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAbID,WAAW;AAejB,MAAME,UAAU,GAAGvD,MAAM,CAACoB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAZID,UAAU;AAchB,MAAME,QAAQ,GAAGzD,MAAM,CAACoB,GAAG;AAC3B,aAAaZ,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA,CAAC;AAACiD,IAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAG3D,MAAM,CAACoB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM;AACxD,WAAWjC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD;AACA,CAAC;AAAC2C,IAAA,GALID,QAAQ;AAOd,MAAME,UAAU,GAAG7D,MAAM,CAACoB,GAAG;AAC7B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACoC,EAAE;AAChD,WAAWtD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACiC,QAAQ;AACpD;AACA,CAAC;AAACC,IAAA,GAJIH,UAAU;AAMhB,MAAMI,YAAY,GAAGjE,MAAM,CAACC,MAAM,CAACqC,MAAM,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmD,UAAU,CAAC7C,SAAS;AAChE,sBAAsBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA,WAAWT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACrD;AACA,oBAAoBT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACvD;AACA,CAAC;AAACkD,IAAA,GAvBIF,YAAY;AAyBlB,MAAMG,OAAO,GAAGA,CAAC;EAAE3D,SAAS;EAAE4D;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC3C,MAAMC,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAC9B,MAAM2E,QAAQ,GAAG1E,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2E,IAAI;IAAEC;EAAO,CAAC,GAAGzE,OAAO,CAAC,CAAC;EAClC,MAAM;IAAE0E;EAAS,CAAC,GAAGzE,gBAAgB,CAAC,CAAC;EAEvC,MAAM0E,KAAK,GAAGD,QAAQ,CAAC,CAAC;EAExB,MAAME,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAEL,KAAK,CAACM,UAAU,GAAG,CAAC,GAAGN,KAAK,CAACM,UAAU,GAAG;EACnD,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,gBAAgB,GAAIL,IAAI,IAAK;IACjCN,QAAQ,CAACM,IAAI,CAAC;EAChB,CAAC;EAED,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC,CAAC;EAED,oBACElF,OAAA,CAACC,gBAAgB;IACfG,SAAS,EAAEA,SAAU;IACrB+E,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9BxF,OAAA,CAACc,aAAa;MAAA0E,QAAA,gBACZxF,OAAA,CAACkB,IAAI;QAAAsE,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACd5F,OAAA,CAACoB,QAAQ;QAAChB,SAAS,EAAEA,SAAU;QAAAoF,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEhB5F,OAAA,CAAC2B,UAAU;MAAA6D,QAAA,EACRf,eAAe,CAACoB,GAAG,CAAEC,IAAI,iBACxB9F,OAAA,CAAC8B,OAAO;QAENiE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAR,QAAA,eAE1BxF,OAAA,CAACgC,OAAO;UACNE,MAAM,EAAEiC,QAAQ,CAAC+B,QAAQ,KAAKJ,IAAI,CAACpB,IAAK;UACxCyB,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACe,IAAI,CAACpB,IAAI,CAAE;UAAAc,QAAA,gBAE3CxF,OAAA,CAACuC,OAAO;YAAAiD,QAAA,EAAEM,IAAI,CAACnB;UAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9B5F,OAAA,CAAC0C,OAAO;YAACtC,SAAS,EAAEA,SAAU;YAAAoF,QAAA,EAAEM,IAAI,CAAClB;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACpDE,IAAI,CAACjB,KAAK,iBACT7E,OAAA,CAAC4C,QAAQ;YAACxC,SAAS,EAAEA,SAAU;YAAAoF,QAAA,EAAEM,IAAI,CAACjB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC,GAbLE,IAAI,CAACpB,IAAI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEb5F,OAAA,CAAC8C,aAAa;MAAA0C,QAAA,eACZxF,OAAA,CAACgD,WAAW;QAACmD,OAAO,EAAEnB,eAAgB;QAAAQ,QAAA,gBACpCxF,OAAA,CAACkD,UAAU;UAAAsC,QAAA,EACR,CAAAnB,IAAI,aAAJA,IAAI,wBAAAH,cAAA,GAAJG,IAAI,CAAE+B,QAAQ,cAAAlC,cAAA,uBAAdA,cAAA,CAAgBmC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACb5F,OAAA,CAACoD,QAAQ;UAAChD,SAAS,EAAEA,SAAU;UAAAoF,QAAA,gBAC7BxF,OAAA,CAACsD,QAAQ;YAAAkC,QAAA,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,QAAQ,KAAI;UAAO;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChD5F,OAAA,CAACwD,UAAU;YAAAgC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEhB5F,OAAA,CAAC4D,YAAY;MACXuC,OAAO,EAAEnC,QAAS;MAClB+B,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MAAAR,QAAA,EAExBpF,SAAS,GAAG,GAAG,GAAG;IAAG;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEvB,CAAC;AAAC3B,EAAA,CAlGIF,OAAO;EAAA,QACMtE,WAAW,EACXC,WAAW,EACHG,OAAO,EACXC,gBAAgB;AAAA;AAAAyG,IAAA,GAJjCxC,OAAO;AAoGb,eAAeA,OAAO;AAAC,IAAAlD,EAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAyC,IAAA;AAAAC,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}