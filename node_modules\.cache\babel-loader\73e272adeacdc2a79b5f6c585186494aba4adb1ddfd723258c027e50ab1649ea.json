{"ast": null, "code": "import { createMotionProxy } from './motion-proxy.mjs';\nimport { createDomMotionConfig } from './utils/create-config.mjs';\n\n/**\n * @public\n */\nconst m = createMotionProxy(createDomMotionConfig);\nexport { m };", "map": {"version": 3, "names": ["createMotionProxy", "createDomMotionConfig", "m"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/render/dom/motion-minimal.mjs"], "sourcesContent": ["import { createMotionProxy } from './motion-proxy.mjs';\nimport { createDomMotionConfig } from './utils/create-config.mjs';\n\n/**\n * @public\n */\nconst m = createMotionProxy(createDomMotionConfig);\n\nexport { m };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,qBAAqB,QAAQ,2BAA2B;;AAEjE;AACA;AACA;AACA,MAAMC,CAAC,GAAGF,iBAAiB,CAACC,qBAAqB,CAAC;AAElD,SAASC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}