{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useGameResources } from '../context/GameResourceContext';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n_c = HomeContainer;\nconst HeroSection = styled(motion.div)`\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(74, 158, 255, 0.1) 100%);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 32px;\n  position: relative;\n  overflow: hidden;\n`;\n_c2 = HeroSection;\nconst HeroBackground = styled.div`\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 50%;\n  height: 100%;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><path d=\"M50,150 Q200,50 350,150 Q200,250 50,150\" fill=\"none\" stroke=\"%23ff6b35\" stroke-width=\"2\" opacity=\"0.3\"/><circle cx=\"100\" cy=\"100\" r=\"3\" fill=\"%23ff6b35\" opacity=\"0.5\"/><circle cx=\"300\" cy=\"200\" r=\"2\" fill=\"%234a9eff\" opacity=\"0.5\"/></svg>') center/cover;\n  opacity: 0.1;\n`;\n_c3 = HeroBackground;\nconst HeroContent = styled.div`\n  position: relative;\n  z-index: 1;\n`;\n_c4 = HeroContent;\nconst HeroTitle = styled.h1`\n  font-size: ${props => props.theme.fontSizes['3xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 16px;\n  background: linear-gradient(135deg, #ff6b35 0%, #4a9eff 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c5 = HeroTitle;\nconst HeroSubtitle = styled.p`\n  font-size: ${props => props.theme.fontSizes.lg};\n  color: ${props => props.theme.colors.text.secondary};\n  margin-bottom: 24px;\n  line-height: 1.6;\n`;\n_c6 = HeroSubtitle;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n`;\n_c7 = ActionButtons;\nconst ActionButton = styled(motion.button)`\n  padding: 12px 24px;\n  background: ${props => props.primary ? 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : 'rgba(255, 255, 255, 0.1)'};\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\n  border-radius: 8px;\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.primary ? props.theme.shadows.glow : props.theme.shadows.md};\n  }\n`;\n_c8 = ActionButton;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n`;\n_c9 = StatsGrid;\nconst StatCard = styled(motion.div)`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 24px;\n  text-align: center;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    transform: translateY(-4px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n_c0 = StatCard;\nconst StatIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, ${props => props.color || '#ff6b35'} 0%, ${props => props.colorEnd || '#ff8555'} 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  margin: 0 auto 16px;\n`;\n_c1 = StatIcon;\nconst StatValue = styled.div`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n_c10 = StatValue;\nconst StatLabel = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n_c11 = StatLabel;\nconst NewsSection = styled.div`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 24px;\n`;\n_c12 = NewsSection;\nconst SectionTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &::before {\n    content: '';\n    width: 4px;\n    height: 24px;\n    background: linear-gradient(135deg, #ff6b35 0%, #4a9eff 100%);\n    border-radius: 2px;\n  }\n`;\n_c13 = SectionTitle;\nconst NewsItem = styled(motion.div)`\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.05);\n    border-color: ${props => props.theme.colors.border.primary};\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c14 = NewsItem;\nconst NewsTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n_c15 = NewsTitle;\nconst NewsDate = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  margin-right: 12px;\n`;\n_c16 = NewsDate;\nconst NewsDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n  line-height: 1.5;\n`;\n_c17 = NewsDescription;\nconst HomePage = () => {\n  _s();\n  const {\n    getStats\n  } = useGameResources();\n  const {\n    user\n  } = useAuth();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const stats = getStats();\n\n  // 更新时间\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n  const newsItems = [{\n    id: 1,\n    title: '🚁 为您的飞行器添加新的模拟体验！',\n    description: '了解即将到来的更新内容，投入真实的全球战场。',\n    date: '10.27 20:25'\n  }, {\n    id: 2,\n    title: 'DCS战役 F-4E: 北境守卫者之\"鬼怪崛起\"',\n    description: '1985年一场看似突然的苏联入侵，为联合国方面带来毁灭和攻击，对西欧发动全面入侵。',\n    date: '10.25 15:30'\n  }, {\n    id: 3,\n    title: 'DCS战役 P-47D: 狼群II: 霸王',\n    description: '体验二战时期最激烈的空战，驾驶P-47D雷电战斗机参与诺曼底登陆战役。',\n    date: '10.23 12:15'\n  }];\n  const handleStartDCS = () => {\n    console.log('启动DCS World');\n    // 这里可以添加启动DCS的逻辑\n  };\n  const handleOpenModules = () => {\n    window.location.href = '/modules';\n  };\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(HeroSection, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: [/*#__PURE__*/_jsxDEV(HeroBackground, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeroContent, {\n        children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n          children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.username) || 'Pilot', \"\\uFF01\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n          children: \"\\u51C6\\u5907\\u597D\\u8FDB\\u5165\\u6570\\u5B57\\u6218\\u6597\\u6A21\\u62DF\\u5668\\u7684\\u4E16\\u754C\\u4E86\\u5417\\uFF1F\\u63A2\\u7D22\\u771F\\u5B9E\\u7684\\u519B\\u7528\\u98DE\\u884C\\u5668\\uFF0C\\u4F53\\u9A8C\\u6700\\u903C\\u771F\\u7684\\u98DE\\u884C\\u6A21\\u62DF\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n          children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n            primary: true,\n            onClick: handleStartDCS,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\uD83D\\uDE80 \\u542F\\u52A8DCS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: handleOpenModules,\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\uD83D\\uDCE6 \\u7BA1\\u7406\\u6A21\\u7EC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        whileHover: {\n          y: -4\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"#4caf50\",\n          colorEnd: \"#66bb6a\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: stats.installed\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"\\u5DF2\\u5B89\\u88C5\\u6A21\\u7EC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        whileHover: {\n          y: -4\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"#2196f3\",\n          colorEnd: \"#42a5f5\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: stats.enabled\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"\\u5DF2\\u542F\\u7528\\u6A21\\u7EC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        whileHover: {\n          y: -4\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"#ff9800\",\n          colorEnd: \"#ffb74d\",\n          children: \"\\uD83D\\uDD04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: stats.hasUpdates\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"\\u5F85\\u66F4\\u65B0\\u6A21\\u7EC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        whileHover: {\n          y: -4\n        },\n        transition: {\n          duration: 0.3\n        },\n        children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n          color: \"#9c27b0\",\n          colorEnd: \"#ba68c8\",\n          children: \"\\uD83D\\uDD52\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n          children: currentTime.toLocaleTimeString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n          children: \"\\u5F53\\u524D\\u65F6\\u95F4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NewsSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u6700\\u65B0\\u6D88\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), newsItems.map((item, index) => /*#__PURE__*/_jsxDEV(NewsItem, {\n        initial: {\n          opacity: 0,\n          x: -20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.4,\n          delay: index * 0.1\n        },\n        whileHover: {\n          x: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(NewsTitle, {\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(NewsDate, {\n            children: item.date\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NewsDescription, {\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"y7lylQb936Muy+v7xnuItMIqqD8=\", false, function () {\n  return [useGameResources, useAuth];\n});\n_c18 = HomePage;\nexport default HomePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"HeroSection\");\n$RefreshReg$(_c3, \"HeroBackground\");\n$RefreshReg$(_c4, \"HeroContent\");\n$RefreshReg$(_c5, \"HeroTitle\");\n$RefreshReg$(_c6, \"HeroSubtitle\");\n$RefreshReg$(_c7, \"ActionButtons\");\n$RefreshReg$(_c8, \"ActionButton\");\n$RefreshReg$(_c9, \"StatsGrid\");\n$RefreshReg$(_c0, \"StatCard\");\n$RefreshReg$(_c1, \"StatIcon\");\n$RefreshReg$(_c10, \"StatValue\");\n$RefreshReg$(_c11, \"StatLabel\");\n$RefreshReg$(_c12, \"NewsSection\");\n$RefreshReg$(_c13, \"SectionTitle\");\n$RefreshReg$(_c14, \"NewsItem\");\n$RefreshReg$(_c15, \"NewsTitle\");\n$RefreshReg$(_c16, \"NewsDate\");\n$RefreshReg$(_c17, \"NewsDescription\");\n$RefreshReg$(_c18, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "motion", "useGameResources", "useAuth", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "HeroSection", "props", "theme", "colors", "border", "primary", "_c2", "HeroBackground", "_c3", "Hero<PERSON><PERSON><PERSON>", "_c4", "<PERSON><PERSON><PERSON><PERSON>", "h1", "fontSizes", "fontWeights", "bold", "text", "_c5", "HeroSubtitle", "p", "lg", "secondary", "_c6", "ActionButtons", "_c7", "ActionButton", "button", "md", "medium", "shadows", "glow", "_c8", "StatsGrid", "_c9", "StatCard", "_c0", "StatIcon", "color", "colorEnd", "_c1", "StatValue", "_c10", "StatLabel", "sm", "_c11", "NewsSection", "_c12", "SectionTitle", "h2", "xl", "semibold", "_c13", "NewsItem", "_c14", "NewsTitle", "h3", "_c15", "NewsDate", "span", "xs", "tertiary", "_c16", "NewsDescription", "_c17", "HomePage", "_s", "getStats", "user", "currentTime", "setCurrentTime", "Date", "stats", "timer", "setInterval", "clearInterval", "newsItems", "id", "title", "description", "date", "handleStartDCS", "console", "log", "handleOpenModules", "window", "location", "href", "children", "initial", "opacity", "y", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "username", "onClick", "whileHover", "scale", "whileTap", "installed", "enabled", "hasUpdates", "toLocaleTimeString", "map", "item", "index", "x", "delay", "_c18", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useGameResources } from '../context/GameResourceContext';\nimport { useAuth } from '../context/AuthContext';\n\nconst HomeContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n\nconst HeroSection = styled(motion.div)`\n  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(74, 158, 255, 0.1) 100%);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 32px;\n  position: relative;\n  overflow: hidden;\n`;\n\nconst HeroBackground = styled.div`\n  position: absolute;\n  top: 0;\n  right: 0;\n  width: 50%;\n  height: 100%;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><path d=\"M50,150 Q200,50 350,150 Q200,250 50,150\" fill=\"none\" stroke=\"%23ff6b35\" stroke-width=\"2\" opacity=\"0.3\"/><circle cx=\"100\" cy=\"100\" r=\"3\" fill=\"%23ff6b35\" opacity=\"0.5\"/><circle cx=\"300\" cy=\"200\" r=\"2\" fill=\"%234a9eff\" opacity=\"0.5\"/></svg>') center/cover;\n  opacity: 0.1;\n`;\n\nconst HeroContent = styled.div`\n  position: relative;\n  z-index: 1;\n`;\n\nconst HeroTitle = styled.h1`\n  font-size: ${props => props.theme.fontSizes['3xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 16px;\n  background: linear-gradient(135deg, #ff6b35 0%, #4a9eff 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst HeroSubtitle = styled.p`\n  font-size: ${props => props.theme.fontSizes.lg};\n  color: ${props => props.theme.colors.text.secondary};\n  margin-bottom: 24px;\n  line-height: 1.6;\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n`;\n\nconst ActionButton = styled(motion.button)`\n  padding: 12px 24px;\n  background: ${props => props.primary ? \n    'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : \n    'rgba(255, 255, 255, 0.1)'\n  };\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\n  border-radius: 8px;\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: ${props => props.primary ? props.theme.shadows.glow : props.theme.shadows.md};\n  }\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n`;\n\nconst StatCard = styled(motion.div)`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 24px;\n  text-align: center;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: ${props => props.theme.colors.primary};\n    transform: translateY(-4px);\n    box-shadow: ${props => props.theme.shadows.lg};\n  }\n`;\n\nconst StatIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, ${props => props.color || '#ff6b35'} 0%, ${props => props.colorEnd || '#ff8555'} 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 24px;\n  margin: 0 auto 16px;\n`;\n\nconst StatValue = styled.div`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n\nconst StatLabel = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n\nconst NewsSection = styled.div`\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 24px;\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 20px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &::before {\n    content: '';\n    width: 4px;\n    height: 24px;\n    background: linear-gradient(135deg, #ff6b35 0%, #4a9eff 100%);\n    border-radius: 2px;\n  }\n`;\n\nconst NewsItem = styled(motion.div)`\n  padding: 16px;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid transparent;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.05);\n    border-color: ${props => props.theme.colors.border.primary};\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst NewsTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n\nconst NewsDate = styled.span`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  margin-right: 12px;\n`;\n\nconst NewsDescription = styled.p`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n  line-height: 1.5;\n`;\n\nconst HomePage = () => {\n  const { getStats } = useGameResources();\n  const { user } = useAuth();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  \n  const stats = getStats();\n\n  // 更新时间\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    \n    return () => clearInterval(timer);\n  }, []);\n\n  const newsItems = [\n    {\n      id: 1,\n      title: '🚁 为您的飞行器添加新的模拟体验！',\n      description: '了解即将到来的更新内容，投入真实的全球战场。',\n      date: '10.27 20:25'\n    },\n    {\n      id: 2,\n      title: 'DCS战役 F-4E: 北境守卫者之\"鬼怪崛起\"',\n      description: '1985年一场看似突然的苏联入侵，为联合国方面带来毁灭和攻击，对西欧发动全面入侵。',\n      date: '10.25 15:30'\n    },\n    {\n      id: 3,\n      title: 'DCS战役 P-47D: 狼群II: 霸王',\n      description: '体验二战时期最激烈的空战，驾驶P-47D雷电战斗机参与诺曼底登陆战役。',\n      date: '10.23 12:15'\n    }\n  ];\n\n  const handleStartDCS = () => {\n    console.log('启动DCS World');\n    // 这里可以添加启动DCS的逻辑\n  };\n\n  const handleOpenModules = () => {\n    window.location.href = '/modules';\n  };\n\n  return (\n    <HomeContainer>\n      <HeroSection\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <HeroBackground />\n        <HeroContent>\n          <HeroTitle>欢迎回来，{user?.username || 'Pilot'}！</HeroTitle>\n          <HeroSubtitle>\n            准备好进入数字战斗模拟器的世界了吗？探索真实的军用飞行器，体验最逼真的飞行模拟。\n          </HeroSubtitle>\n          <ActionButtons>\n            <ActionButton\n              primary\n              onClick={handleStartDCS}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              🚀 启动DCS\n            </ActionButton>\n            <ActionButton\n              onClick={handleOpenModules}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              📦 管理模组\n            </ActionButton>\n          </ActionButtons>\n        </HeroContent>\n      </HeroSection>\n\n      <StatsGrid>\n        <StatCard\n          whileHover={{ y: -4 }}\n          transition={{ duration: 0.3 }}\n        >\n          <StatIcon color=\"#4caf50\" colorEnd=\"#66bb6a\">📦</StatIcon>\n          <StatValue>{stats.installed}</StatValue>\n          <StatLabel>已安装模组</StatLabel>\n        </StatCard>\n        \n        <StatCard\n          whileHover={{ y: -4 }}\n          transition={{ duration: 0.3 }}\n        >\n          <StatIcon color=\"#2196f3\" colorEnd=\"#42a5f5\">✅</StatIcon>\n          <StatValue>{stats.enabled}</StatValue>\n          <StatLabel>已启用模组</StatLabel>\n        </StatCard>\n        \n        <StatCard\n          whileHover={{ y: -4 }}\n          transition={{ duration: 0.3 }}\n        >\n          <StatIcon color=\"#ff9800\" colorEnd=\"#ffb74d\">🔄</StatIcon>\n          <StatValue>{stats.hasUpdates}</StatValue>\n          <StatLabel>待更新模组</StatLabel>\n        </StatCard>\n        \n        <StatCard\n          whileHover={{ y: -4 }}\n          transition={{ duration: 0.3 }}\n        >\n          <StatIcon color=\"#9c27b0\" colorEnd=\"#ba68c8\">🕒</StatIcon>\n          <StatValue>{currentTime.toLocaleTimeString()}</StatValue>\n          <StatLabel>当前时间</StatLabel>\n        </StatCard>\n      </StatsGrid>\n\n      <NewsSection>\n        <SectionTitle>最新消息</SectionTitle>\n        {newsItems.map((item, index) => (\n          <NewsItem\n            key={item.id}\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.4, delay: index * 0.1 }}\n            whileHover={{ x: 8 }}\n          >\n            <NewsTitle>{item.title}</NewsTitle>\n            <div>\n              <NewsDate>{item.date}</NewsDate>\n              <NewsDescription>{item.description}</NewsDescription>\n            </div>\n          </NewsItem>\n        ))}\n      </NewsSection>\n    </HomeContainer>\n  );\n};\n\nexport default HomePage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGN,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,aAAa;AAQnB,MAAMG,WAAW,GAAGT,MAAM,CAACC,MAAM,CAACM,GAAG,CAAC;AACtC;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIN,WAAW;AASjB,MAAMO,cAAc,GAAGhB,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GARID,cAAc;AAUpB,MAAME,WAAW,GAAGlB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA,CAAC;AAACY,GAAA,GAHID,WAAW;AAKjB,MAAME,SAAS,GAAGpB,MAAM,CAACqB,EAAE;AAC3B,eAAeX,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,WAAW,CAACC,IAAI;AACtD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACX,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GATIN,SAAS;AAWf,MAAMO,YAAY,GAAG3B,MAAM,CAAC4B,CAAC;AAC7B,eAAelB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAACO,EAAE;AAChD,WAAWnB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACK,SAAS;AACrD;AACA;AACA,CAAC;AAACC,GAAA,GALIJ,YAAY;AAOlB,MAAMK,aAAa,GAAGhC,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGlC,MAAM,CAACC,MAAM,CAACkC,MAAM,CAAC;AAC1C;AACA,gBAAgBzB,KAAK,IAAIA,KAAK,CAACI,OAAO,GAClC,mDAAmD,GACnD,0BAA0B;AAC9B,sBACsBJ,KAAK,IAAIA,KAAK,CAACI,OAAO,GAAG,aAAa,GAAGJ,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChG;AACA,WAAWJ,KAAK,IAAIA,KAAK,CAACI,OAAO,GAAG,OAAO,GAAGJ,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACX,OAAO;AAC7E,eAAeJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAACc,EAAE;AAChD,iBAAiB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,WAAW,CAACc,MAAM;AACxD;AACA;AACA;AACA;AACA;AACA,kBAAkB3B,KAAK,IAAIA,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAACC,KAAK,CAAC2B,OAAO,CAACC,IAAI,GAAG7B,KAAK,CAACC,KAAK,CAAC2B,OAAO,CAACF,EAAE;AAC5F;AACA,CAAC;AAACI,GAAA,GAlBIN,YAAY;AAoBlB,MAAMO,SAAS,GAAGzC,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACmC,GAAA,GAJID,SAAS;AAMf,MAAME,QAAQ,GAAG3C,MAAM,CAACC,MAAM,CAACM,GAAG,CAAC;AACnC;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,OAAO;AACvD;AACA,kBAAkBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC2B,OAAO,CAACT,EAAE;AACjD;AACA,CAAC;AAACe,GAAA,GAbID,QAAQ;AAed,MAAME,QAAQ,GAAG7C,MAAM,CAACO,GAAG;AAC3B;AACA;AACA,wCAAwCG,KAAK,IAAIA,KAAK,CAACoC,KAAK,IAAI,SAAS,QAAQpC,KAAK,IAAIA,KAAK,CAACqC,QAAQ,IAAI,SAAS;AACrH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIH,QAAQ;AAYd,MAAMI,SAAS,GAAGjD,MAAM,CAACO,GAAG;AAC5B,eAAeG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,WAAW,CAACC,IAAI;AACtD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACX,OAAO;AACnD;AACA,CAAC;AAACoC,IAAA,GALID,SAAS;AAOf,MAAME,SAAS,GAAGnD,MAAM,CAACO,GAAG;AAC5B,eAAeG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAAC8B,EAAE;AAChD,WAAW1C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACK,SAAS;AACrD,CAAC;AAACuB,IAAA,GAHIF,SAAS;AAKf,MAAMG,WAAW,GAAGtD,MAAM,CAACO,GAAG;AAC9B;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA;AACA,CAAC;AAACyC,IAAA,GALID,WAAW;AAOjB,MAAME,YAAY,GAAGxD,MAAM,CAACyD,EAAE;AAC9B,eAAe/C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAACoC,EAAE;AAChD,iBAAiBhD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,WAAW,CAACoC,QAAQ;AAC1D,WAAWjD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACX,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GAhBIJ,YAAY;AAkBlB,MAAMK,QAAQ,GAAG7D,MAAM,CAACC,MAAM,CAACM,GAAG,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBG,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgD,IAAA,GAjBID,QAAQ;AAmBd,MAAME,SAAS,GAAG/D,MAAM,CAACgE,EAAE;AAC3B,eAAetD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAACc,EAAE;AAChD,iBAAiB1B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACY,WAAW,CAACc,MAAM;AACxD,WAAW3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACX,OAAO;AACnD;AACA,CAAC;AAACmD,IAAA,GALIF,SAAS;AAOf,MAAMG,QAAQ,GAAGlE,MAAM,CAACmE,IAAI;AAC5B,eAAezD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAAC8C,EAAE;AAChD,WAAW1D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAAC4C,QAAQ;AACpD;AACA,CAAC;AAACC,IAAA,GAJIJ,QAAQ;AAMd,MAAMK,eAAe,GAAGvE,MAAM,CAAC4B,CAAC;AAChC,eAAelB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACW,SAAS,CAAC8B,EAAE;AAChD,WAAW1C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACa,IAAI,CAACK,SAAS;AACrD;AACA,CAAC;AAAC0C,IAAA,GAJID,eAAe;AAMrB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAS,CAAC,GAAGzE,gBAAgB,CAAC,CAAC;EACvC,MAAM;IAAE0E;EAAK,CAAC,GAAGzE,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC0E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,IAAIiF,IAAI,CAAC,CAAC,CAAC;EAE1D,MAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;;EAExB;EACA5E,SAAS,CAAC,MAAM;IACd,MAAMkF,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BJ,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMI,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAU;EACnC,CAAC;EAED,oBACE1F,OAAA,CAACC,aAAa;IAAA0F,QAAA,gBACZ3F,OAAA,CAACI,WAAW;MACVwF,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAN,QAAA,gBAE9B3F,OAAA,CAACW,cAAc;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBrG,OAAA,CAACa,WAAW;QAAA8E,QAAA,gBACV3F,OAAA,CAACe,SAAS;UAAA4E,QAAA,GAAC,gCAAK,EAAC,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,QAAQ,KAAI,OAAO,EAAC,QAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACxDrG,OAAA,CAACsB,YAAY;UAAAqE,QAAA,EAAC;QAEd;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfrG,OAAA,CAAC2B,aAAa;UAAAgE,QAAA,gBACZ3F,OAAA,CAAC6B,YAAY;YACXpB,OAAO;YACP8F,OAAO,EAAEnB,cAAe;YACxBoB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAd,QAAA,EAC3B;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfrG,OAAA,CAAC6B,YAAY;YACX0E,OAAO,EAAEhB,iBAAkB;YAC3BiB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAd,QAAA,EAC3B;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEdrG,OAAA,CAACoC,SAAS;MAAAuD,QAAA,gBACR3F,OAAA,CAACsC,QAAQ;QACPkE,UAAU,EAAE;UAAEV,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAN,QAAA,gBAE9B3F,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC,SAAS;UAAAiD,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC1DrG,OAAA,CAAC4C,SAAS;UAAA+C,QAAA,EAAEhB,KAAK,CAACgC;QAAS;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACxCrG,OAAA,CAAC8C,SAAS;UAAA6C,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEXrG,OAAA,CAACsC,QAAQ;QACPkE,UAAU,EAAE;UAAEV,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAN,QAAA,gBAE9B3F,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC,SAAS;UAAAiD,QAAA,EAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACzDrG,OAAA,CAAC4C,SAAS;UAAA+C,QAAA,EAAEhB,KAAK,CAACiC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACtCrG,OAAA,CAAC8C,SAAS;UAAA6C,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEXrG,OAAA,CAACsC,QAAQ;QACPkE,UAAU,EAAE;UAAEV,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAN,QAAA,gBAE9B3F,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC,SAAS;UAAAiD,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC1DrG,OAAA,CAAC4C,SAAS;UAAA+C,QAAA,EAAEhB,KAAK,CAACkC;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzCrG,OAAA,CAAC8C,SAAS;UAAA6C,QAAA,EAAC;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEXrG,OAAA,CAACsC,QAAQ;QACPkE,UAAU,EAAE;UAAEV,CAAC,EAAE,CAAC;QAAE,CAAE;QACtBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAN,QAAA,gBAE9B3F,OAAA,CAACwC,QAAQ;UAACC,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC,SAAS;UAAAiD,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC1DrG,OAAA,CAAC4C,SAAS;UAAA+C,QAAA,EAAEnB,WAAW,CAACsC,kBAAkB,CAAC;QAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzDrG,OAAA,CAAC8C,SAAS;UAAA6C,QAAA,EAAC;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEZrG,OAAA,CAACiD,WAAW;MAAA0C,QAAA,gBACV3F,OAAA,CAACmD,YAAY;QAAAwC,QAAA,EAAC;MAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,EAChCtB,SAAS,CAACgC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBjH,OAAA,CAACwD,QAAQ;QAEPoC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEqB,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCnB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEqB,CAAC,EAAE;QAAE,CAAE;QAC9BlB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEkB,KAAK,EAAEF,KAAK,GAAG;QAAI,CAAE;QAClDT,UAAU,EAAE;UAAEU,CAAC,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAErB3F,OAAA,CAAC0D,SAAS;UAAAiC,QAAA,EAAEqB,IAAI,CAAC/B;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnCrG,OAAA;UAAA2F,QAAA,gBACE3F,OAAA,CAAC6D,QAAQ;YAAA8B,QAAA,EAAEqB,IAAI,CAAC7B;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChCrG,OAAA,CAACkE,eAAe;YAAAyB,QAAA,EAAEqB,IAAI,CAAC9B;UAAW;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA,GAVDW,IAAI,CAAChC,EAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWJ,CACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAAChC,EAAA,CAzIID,QAAQ;EAAA,QACSvE,gBAAgB,EACpBC,OAAO;AAAA;AAAAsH,IAAA,GAFpBhD,QAAQ;AA2Id,eAAeA,QAAQ;AAAC,IAAAjE,EAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAiD,IAAA;AAAAC,YAAA,CAAAlH,EAAA;AAAAkH,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}