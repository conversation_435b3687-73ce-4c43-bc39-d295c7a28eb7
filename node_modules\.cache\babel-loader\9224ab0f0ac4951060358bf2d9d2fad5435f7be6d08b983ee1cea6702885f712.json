{"ast": null, "code": "import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟游戏资源数据\nlet mockResources = [{\n  id: 1,\n  name: 'F/A-18C Hornet',\n  category: 'aircraft-jet',\n  description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\n  installed: true,\n  enabled: true,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '15.2 GB'\n}, {\n  id: 2,\n  name: 'A-10C II Tank Killer',\n  category: 'aircraft-jet',\n  description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\n  installed: true,\n  enabled: false,\n  hasUpdate: true,\n  updating: false,\n  version: '2.7.5',\n  size: '12.8 GB'\n}, {\n  id: 3,\n  name: 'F-16C Viper',\n  category: 'aircraft-jet',\n  description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\n  installed: false,\n  enabled: false,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.0',\n  size: '14.5 GB'\n}, {\n  id: 4,\n  name: 'Persian Gulf Map',\n  category: 'terrain',\n  description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\n  installed: true,\n  enabled: true,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '8.9 GB'\n}, {\n  id: 5,\n  name: 'Syria Map',\n  category: 'terrain',\n  description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\n  installed: false,\n  enabled: false,\n  hasUpdate: false,\n  updating: false,\n  version: '2.8.1',\n  size: '7.2 GB'\n}];\n\n// 模拟API响应\nconst mockAPI = {\n  getResources: async (params = {}) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    let filtered = [...mockResources];\n    if (params.category && params.category !== 'all') {\n      filtered = filtered.filter(r => r.category === params.category);\n    }\n    if (params.search) {\n      filtered = filtered.filter(r => r.name.toLowerCase().includes(params.search.toLowerCase()));\n    }\n    return filtered;\n  },\n  toggleInstall: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.installed = !resource.installed;\n      if (!resource.installed) {\n        resource.enabled = false;\n      }\n    }\n    return resource;\n  },\n  toggleEnable: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource && resource.installed) {\n      resource.enabled = !resource.enabled;\n    }\n    return resource;\n  },\n  checkUpdates: async () => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    return mockResources.filter(r => r.hasUpdate);\n  },\n  updateResource: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.hasUpdate = false;\n      resource.updating = false;\n    }\n    return resource;\n  },\n  repairResource: async resourceId => {\n    await new Promise(resolve => setTimeout(resolve, 600));\n    const resource = mockResources.find(r => r.id === resourceId);\n    return resource;\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getResources(params);\n      } else {\n        const response = await api.get('/game-resources', {\n          params\n        });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源分类\n  async getCategories() {\n    try {\n      if (MOCK_MODE) {\n        return [{\n          id: 'aircraft-jet',\n          name: '喷气发动机飞机'\n        }, {\n          id: 'aircraft-prop',\n          name: '活塞发动机飞机'\n        }, {\n          id: 'terrain',\n          name: '地形'\n        }, {\n          id: 'campaign',\n          name: '战役'\n        }];\n      } else {\n        const response = await api.get('/game-resources/categories');\n        return response.categories || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return mockResources.find(r => r.id === resourceId);\n      } else {\n        const response = await api.get(`/game-resources/${resourceId}`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleInstall(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleEnable(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 检查更新\n  async checkUpdates() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.checkUpdates();\n      } else {\n        const response = await api.post('/game-resources/check-updates');\n        return response.updates || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/update`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.repairResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/repair`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: {\n          q: query,\n          ...filters\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: {\n          limit\n        }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  }\n};\nexport default gameResourceService;", "map": {"version": 3, "names": ["axios", "MOCK_MODE", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "mockResources", "id", "name", "category", "description", "installed", "enabled", "hasUpdate", "updating", "version", "size", "mockAPI", "getResources", "params", "Promise", "resolve", "setTimeout", "filtered", "filter", "r", "search", "toLowerCase", "includes", "toggleInstall", "resourceId", "resource", "find", "toggleEnable", "checkUpdates", "updateResource", "repairResource", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "gameResourceService", "get", "resources", "getCategories", "categories", "getResource", "post", "updates", "cleanCache", "batchOperation", "operation", "resourceIds", "getInstallProgress", "progress", "cancelInstall", "searchResources", "query", "filters", "q", "getRecommended", "getLatest", "limit", "getPopular", "rateResource", "rating", "comment", "getResourceRatings", "ratings"], "sources": ["D:/Test/Battle Launcher/src/services/gameResourceService.js"], "sourcesContent": ["import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000, // 游戏资源操作可能需要更长时间\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟游戏资源数据\nlet mockResources = [\n  {\n    id: 1,\n    name: 'F/A-18C Hornet',\n    category: 'aircraft-jet',\n    description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',\n    installed: true,\n    enabled: true,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '15.2 GB'\n  },\n  {\n    id: 2,\n    name: 'A-10C II Tank Killer',\n    category: 'aircraft-jet',\n    description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',\n    installed: true,\n    enabled: false,\n    hasUpdate: true,\n    updating: false,\n    version: '2.7.5',\n    size: '12.8 GB'\n  },\n  {\n    id: 3,\n    name: 'F-16C Viper',\n    category: 'aircraft-jet',\n    description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',\n    installed: false,\n    enabled: false,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.0',\n    size: '14.5 GB'\n  },\n  {\n    id: 4,\n    name: 'Persian Gulf Map',\n    category: 'terrain',\n    description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',\n    installed: true,\n    enabled: true,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '8.9 GB'\n  },\n  {\n    id: 5,\n    name: 'Syria Map',\n    category: 'terrain',\n    description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',\n    installed: false,\n    enabled: false,\n    hasUpdate: false,\n    updating: false,\n    version: '2.8.1',\n    size: '7.2 GB'\n  }\n];\n\n// 模拟API响应\nconst mockAPI = {\n  getResources: async (params = {}) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    let filtered = [...mockResources];\n    \n    if (params.category && params.category !== 'all') {\n      filtered = filtered.filter(r => r.category === params.category);\n    }\n    \n    if (params.search) {\n      filtered = filtered.filter(r => \n        r.name.toLowerCase().includes(params.search.toLowerCase())\n      );\n    }\n    \n    return filtered;\n  },\n  \n  toggleInstall: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.installed = !resource.installed;\n      if (!resource.installed) {\n        resource.enabled = false;\n      }\n    }\n    return resource;\n  },\n  \n  toggleEnable: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource && resource.installed) {\n      resource.enabled = !resource.enabled;\n    }\n    return resource;\n  },\n  \n  checkUpdates: async () => {\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    return mockResources.filter(r => r.hasUpdate);\n  },\n  \n  updateResource: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 800));\n    const resource = mockResources.find(r => r.id === resourceId);\n    if (resource) {\n      resource.hasUpdate = false;\n      resource.updating = false;\n    }\n    return resource;\n  },\n  \n  repairResource: async (resourceId) => {\n    await new Promise(resolve => setTimeout(resolve, 600));\n    const resource = mockResources.find(r => r.id === resourceId);\n    return resource;\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst gameResourceService = {\n  // 获取游戏资源列表\n  async getResources(params = {}) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getResources(params);\n      } else {\n        const response = await api.get('/game-resources', { params });\n        return response.resources || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源分类\n  async getCategories() {\n    try {\n      if (MOCK_MODE) {\n        return [\n          { id: 'aircraft-jet', name: '喷气发动机飞机' },\n          { id: 'aircraft-prop', name: '活塞发动机飞机' },\n          { id: 'terrain', name: '地形' },\n          { id: 'campaign', name: '战役' }\n        ];\n      } else {\n        const response = await api.get('/game-resources/categories');\n        return response.categories || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个资源详情\n  async getResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return mockResources.find(r => r.id === resourceId);\n      } else {\n        const response = await api.get(`/game-resources/${resourceId}`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 安装/卸载资源\n  async toggleInstall(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleInstall(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-install`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 启用/禁用资源\n  async toggleEnable(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.toggleEnable(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 检查更新\n  async checkUpdates() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.checkUpdates();\n      } else {\n        const response = await api.post('/game-resources/check-updates');\n        return response.updates || [];\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新资源\n  async updateResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/update`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修复资源\n  async repairResource(resourceId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.repairResource(resourceId);\n      } else {\n        const response = await api.post(`/game-resources/${resourceId}/repair`);\n        return response.resource;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 清理资源缓存\n  async cleanCache() {\n    try {\n      const response = await api.post('/game-resources/clean-cache');\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 批量操作\n  async batchOperation(operation, resourceIds) {\n    try {\n      const response = await api.post('/game-resources/batch', {\n        operation,\n        resourceIds\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取安装进度\n  async getInstallProgress(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/progress`);\n      return response.progress;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 取消安装\n  async cancelInstall(resourceId) {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/cancel`);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 搜索资源\n  async searchResources(query, filters = {}) {\n    try {\n      const response = await api.get('/game-resources/search', {\n        params: { q: query, ...filters }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取推荐资源\n  async getRecommended() {\n    try {\n      const response = await api.get('/game-resources/recommended');\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取最新资源\n  async getLatest(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/latest', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取热门资源\n  async getPopular(limit = 10) {\n    try {\n      const response = await api.get('/game-resources/popular', {\n        params: { limit }\n      });\n      return response.resources || [];\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 评价资源\n  async rateResource(resourceId, rating, comment = '') {\n    try {\n      const response = await api.post(`/game-resources/${resourceId}/rate`, {\n        rating,\n        comment\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取资源评价\n  async getResourceRatings(resourceId) {\n    try {\n      const response = await api.get(`/game-resources/${resourceId}/ratings`);\n      return response.ratings || [];\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default gameResourceService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,SAAS,GAAG,IAAI;AACtB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EAAE;EAChBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,IAAIC,aAAa,GAAG,CAClB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;AACR,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,sBAAsB;EAC5BC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;AACR,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;AACR,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;AACR,CAAC,EACD;EACET,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE,KAAK;EAChBC,QAAQ,EAAE,KAAK;EACfC,OAAO,EAAE,OAAO;EAChBC,IAAI,EAAE;AACR,CAAC,CACF;;AAED;AACA,MAAMC,OAAO,GAAG;EACdC,YAAY,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IACnC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,IAAIE,QAAQ,GAAG,CAAC,GAAGjB,aAAa,CAAC;IAEjC,IAAIa,MAAM,CAACV,QAAQ,IAAIU,MAAM,CAACV,QAAQ,KAAK,KAAK,EAAE;MAChDc,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,QAAQ,KAAKU,MAAM,CAACV,QAAQ,CAAC;IACjE;IAEA,IAAIU,MAAM,CAACO,MAAM,EAAE;MACjBH,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,CAAC,IAC1BA,CAAC,CAACjB,IAAI,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,MAAM,CAACO,MAAM,CAACC,WAAW,CAAC,CAAC,CAC3D,CAAC;IACH;IAEA,OAAOJ,QAAQ;EACjB,CAAC;EAEDM,aAAa,EAAE,MAAOC,UAAU,IAAK;IACnC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAGzB,aAAa,CAAC0B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,UAAU,CAAC;IAC7D,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACpB,SAAS,GAAG,CAACoB,QAAQ,CAACpB,SAAS;MACxC,IAAI,CAACoB,QAAQ,CAACpB,SAAS,EAAE;QACvBoB,QAAQ,CAACnB,OAAO,GAAG,KAAK;MAC1B;IACF;IACA,OAAOmB,QAAQ;EACjB,CAAC;EAEDE,YAAY,EAAE,MAAOH,UAAU,IAAK;IAClC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAGzB,aAAa,CAAC0B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,UAAU,CAAC;IAC7D,IAAIC,QAAQ,IAAIA,QAAQ,CAACpB,SAAS,EAAE;MAClCoB,QAAQ,CAACnB,OAAO,GAAG,CAACmB,QAAQ,CAACnB,OAAO;IACtC;IACA,OAAOmB,QAAQ;EACjB,CAAC;EAEDG,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAM,IAAId,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACvD,OAAOf,aAAa,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;EAC/C,CAAC;EAEDsB,cAAc,EAAE,MAAOL,UAAU,IAAK;IACpC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAGzB,aAAa,CAAC0B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,UAAU,CAAC;IAC7D,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAClB,SAAS,GAAG,KAAK;MAC1BkB,QAAQ,CAACjB,QAAQ,GAAG,KAAK;IAC3B;IACA,OAAOiB,QAAQ;EACjB,CAAC;EAEDK,cAAc,EAAE,MAAON,UAAU,IAAK;IACpC,MAAM,IAAIV,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMU,QAAQ,GAAGzB,aAAa,CAAC0B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,UAAU,CAAC;IAC7D,OAAOC,QAAQ;EACjB;AACF,CAAC;;AAED;AACA9B,GAAG,CAACoC,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACnC,OAAO,CAACuC,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOzB,OAAO,CAAC0B,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA5C,GAAG,CAACoC,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC1BQ,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACE,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClCR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOlC,OAAO,CAAC0B,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMU,mBAAmB,GAAG;EAC1B;EACA,MAAMrC,YAAYA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI;MACF,IAAIpB,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACC,YAAY,CAACC,MAAM,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM4B,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,iBAAiB,EAAE;UAAErC;QAAO,CAAC,CAAC;QAC7D,OAAO4B,QAAQ,CAACU,SAAS,IAAI,EAAE;MACjC;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMa,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,IAAI3D,SAAS,EAAE;QACb,OAAO,CACL;UAAEQ,EAAE,EAAE,cAAc;UAAEC,IAAI,EAAE;QAAU,CAAC,EACvC;UAAED,EAAE,EAAE,eAAe;UAAEC,IAAI,EAAE;QAAU,CAAC,EACxC;UAAED,EAAE,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAK,CAAC,EAC7B;UAAED,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE;QAAK,CAAC,CAC/B;MACH,CAAC,MAAM;QACL,MAAMuC,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,4BAA4B,CAAC;QAC5D,OAAOT,QAAQ,CAACY,UAAU,IAAI,EAAE;MAClC;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMe,WAAWA,CAAC9B,UAAU,EAAE;IAC5B,IAAI;MACF,IAAI/B,SAAS,EAAE;QACb,OAAOO,aAAa,CAAC0B,IAAI,CAACP,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKuB,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,mBAAmB1B,UAAU,EAAE,CAAC;QAC/D,OAAOiB,QAAQ,CAAChB,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMhB,aAAaA,CAACC,UAAU,EAAE;IAC9B,IAAI;MACF,IAAI/B,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACY,aAAa,CAACC,UAAU,CAAC;MAChD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,iBAAiB,CAAC;QAC/E,OAAOiB,QAAQ,CAAChB,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMZ,YAAYA,CAACH,UAAU,EAAE;IAC7B,IAAI;MACF,IAAI/B,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACgB,YAAY,CAACH,UAAU,CAAC;MAC/C,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,gBAAgB,CAAC;QAC9E,OAAOiB,QAAQ,CAAChB,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMX,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACiB,YAAY,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,MAAMa,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,+BAA+B,CAAC;QAChE,OAAOd,QAAQ,CAACe,OAAO,IAAI,EAAE;MAC/B;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMV,cAAcA,CAACL,UAAU,EAAE;IAC/B,IAAI;MACF,IAAI/B,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACkB,cAAc,CAACL,UAAU,CAAC;MACjD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,SAAS,CAAC;QACvE,OAAOiB,QAAQ,CAAChB,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMT,cAAcA,CAACN,UAAU,EAAE;IAC/B,IAAI;MACF,IAAI/B,SAAS,EAAE;QACb,OAAO,MAAMkB,OAAO,CAACmB,cAAc,CAACN,UAAU,CAAC;MACjD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,SAAS,CAAC;QACvE,OAAOiB,QAAQ,CAAChB,QAAQ;MAC1B;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkB,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,6BAA6B,CAAC;MAC9D,OAAOd,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMmB,cAAcA,CAACC,SAAS,EAAEC,WAAW,EAAE;IAC3C,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,uBAAuB,EAAE;QACvDI,SAAS;QACTC;MACF,CAAC,CAAC;MACF,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMsB,kBAAkBA,CAACrC,UAAU,EAAE;IACnC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,mBAAmB1B,UAAU,WAAW,CAAC;MACxE,OAAOiB,QAAQ,CAACqB,QAAQ;IAC1B,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,aAAaA,CAACvC,UAAU,EAAE;IAC9B,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,SAAS,CAAC;MACvE,OAAOiB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMyB,eAAeA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMzB,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,wBAAwB,EAAE;QACvDrC,MAAM,EAAE;UAAEsD,CAAC,EAAEF,KAAK;UAAE,GAAGC;QAAQ;MACjC,CAAC,CAAC;MACF,OAAOzB,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM6B,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,6BAA6B,CAAC;MAC7D,OAAOT,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM8B,SAASA,CAACC,KAAK,GAAG,EAAE,EAAE;IAC1B,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,wBAAwB,EAAE;QACvDrC,MAAM,EAAE;UAAEyD;QAAM;MAClB,CAAC,CAAC;MACF,OAAO7B,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMgC,UAAUA,CAACD,KAAK,GAAG,EAAE,EAAE;IAC3B,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,yBAAyB,EAAE;QACxDrC,MAAM,EAAE;UAAEyD;QAAM;MAClB,CAAC,CAAC;MACF,OAAO7B,QAAQ,CAACU,SAAS,IAAI,EAAE;IACjC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMiC,YAAYA,CAAChD,UAAU,EAAEiD,MAAM,EAAEC,OAAO,GAAG,EAAE,EAAE;IACnD,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAM9C,GAAG,CAAC4D,IAAI,CAAC,mBAAmB/B,UAAU,OAAO,EAAE;QACpEiD,MAAM;QACNC;MACF,CAAC,CAAC;MACF,OAAOjC,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMoC,kBAAkBA,CAACnD,UAAU,EAAE;IACnC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAM9C,GAAG,CAACuD,GAAG,CAAC,mBAAmB1B,UAAU,UAAU,CAAC;MACvE,OAAOiB,QAAQ,CAACmC,OAAO,IAAI,EAAE;IAC/B,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeU,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}