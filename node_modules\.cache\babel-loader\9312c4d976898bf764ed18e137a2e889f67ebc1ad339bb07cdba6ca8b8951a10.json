{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n_c = SettingsContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 16px;\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0;\n`;\n_c3 = Title;\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n  align-items: center;\n`;\n_c4 = HeaderActions;\nconst ActionButton = styled(motion.button)`\n  padding: 8px 16px;\n  background: ${props => props.primary ? 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : 'rgba(255, 255, 255, 0.1)'};\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.primary ? 'linear-gradient(135deg, #ff8555 0%, #ff6b35 100%)' : 'rgba(255, 255, 255, 0.15)'};\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n`;\n_c5 = ActionButton;\nconst SettingsContent = styled.div`\n  display: flex;\n  gap: 24px;\n  flex: 1;\n  overflow: hidden;\n`;\n_c6 = SettingsContent;\nconst SettingsSidebar = styled.div`\n  width: 250px;\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n_c7 = SettingsSidebar;\nconst SidebarItem = styled(motion.button)`\n  padding: 12px 16px;\n  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  border: none;\n  border-radius: 8px;\n  color: ${props => props.active ? 'white' : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.active ? props.theme.colors.primary : 'rgba(255, 255, 255, 0.05)'};\n    color: ${props => props.active ? 'white' : props.theme.colors.text.primary};\n  }\n`;\n_c8 = SidebarItem;\nconst SettingsPanel = styled.div`\n  flex: 1;\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 24px;\n  overflow-y: auto;\n`;\n_c9 = SettingsPanel;\nconst PanelTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0 0 24px 0;\n`;\n_c0 = PanelTitle;\nconst SettingSection = styled.div`\n  margin-bottom: 32px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c1 = SettingSection;\nconst SectionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n_c10 = SectionTitle;\nconst SettingItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c11 = SettingItem;\nconst SettingLabel = styled.div`\n  flex: 1;\n`;\n_c12 = SettingLabel;\nconst SettingName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 4px;\n`;\n_c13 = SettingName;\nconst SettingDescription = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  line-height: 1.4;\n`;\n_c14 = SettingDescription;\nconst SettingControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n_c15 = SettingControl;\nconst Toggle = styled.label`\n  position: relative;\n  display: inline-block;\n  width: 44px;\n  height: 24px;\n  \n  input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n  \n  span {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: ${props => props.theme.colors.background.tertiary};\n    transition: 0.3s;\n    border-radius: 24px;\n    \n    &:before {\n      position: absolute;\n      content: \"\";\n      height: 18px;\n      width: 18px;\n      left: 3px;\n      bottom: 3px;\n      background-color: white;\n      transition: 0.3s;\n      border-radius: 50%;\n    }\n  }\n  \n  input:checked + span {\n    background-color: ${props => props.theme.colors.primary};\n  }\n  \n  input:checked + span:before {\n    transform: translateX(20px);\n  }\n`;\n_c16 = Toggle;\nconst Select = styled.select`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  min-width: 120px;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  option {\n    background: ${props => props.theme.colors.background.secondary};\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n_c17 = Select;\nconst Input = styled.input`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  min-width: 120px;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  &::placeholder {\n    color: ${props => props.theme.colors.text.tertiary};\n  }\n`;\nconst PathInput = styled.div`\n  display: flex;\n  gap: 8px;\n  align-items: center;\n`;\n_c18 = PathInput;\nconst PathField = styled(Input)`\n  flex: 1;\n  min-width: 200px;\n`;\n_c19 = PathField;\nconst BrowseButton = styled(motion.button)`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.xs};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.15);\n  }\n`;\n_c20 = BrowseButton;\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid ${props => props.theme.colors.border.secondary};\n  border-radius: 12px;\n  margin-bottom: 24px;\n`;\n_c21 = UserProfile;\nconst Avatar = styled.div`\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.bold};\n`;\n_c22 = Avatar;\nconst UserInfo = styled.div`\n  flex: 1;\n`;\n_c23 = UserInfo;\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 4px;\n`;\n_c24 = UserName;\nconst UserEmail = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n_c25 = UserEmail;\nconst SettingsPage = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [activeSection, setActiveSection] = useState('game');\n  const [settings, setSettings] = useState({\n    // 游戏设置\n    dcsPath: 'C:\\\\Program Files\\\\Eagle Dynamics\\\\DCS World',\n    savedGamesPath: '%USERPROFILE%\\\\Saved Games\\\\DCS',\n    autoStart: true,\n    checkUpdates: true,\n    enableVR: false,\n    resolution: '1920x1080',\n    graphics: 'high',\n    // 启动器设置\n    autoLaunch: false,\n    minimizeToTray: true,\n    showNotifications: true,\n    language: 'zh-CN',\n    theme: 'dark',\n    // 网络设置\n    autoDownload: true,\n    downloadPath: 'D:\\\\DCS Downloads',\n    maxDownloads: 3,\n    bandwidth: 'unlimited'\n  });\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleBrowsePath = pathType => {\n    // 模拟文件夹选择\n    alert(`选择${pathType}路径的功能需要在Electron环境中实现`);\n  };\n  const handleSaveSettings = () => {\n    // 保存设置到本地存储或发送到服务器\n    localStorage.setItem('launcherSettings', JSON.stringify(settings));\n    alert('设置已保存');\n  };\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      // 重置为默认设置\n      setSettings({\n        dcsPath: 'C:\\\\Program Files\\\\Eagle Dynamics\\\\DCS World',\n        savedGamesPath: '%USERPROFILE%\\\\Saved Games\\\\DCS',\n        autoStart: true,\n        checkUpdates: true,\n        enableVR: false,\n        resolution: '1920x1080',\n        graphics: 'high',\n        autoLaunch: false,\n        minimizeToTray: true,\n        showNotifications: true,\n        language: 'zh-CN',\n        theme: 'dark',\n        autoDownload: true,\n        downloadPath: 'D:\\\\DCS Downloads',\n        maxDownloads: 3,\n        bandwidth: 'unlimited'\n      });\n    }\n  };\n  const renderGameSettings = () => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"DCS \\u5B89\\u88C5\\u8DEF\\u5F84\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"DCS World \\u5B89\\u88C5\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"DCS World \\u7684\\u4E3B\\u5B89\\u88C5\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(PathInput, {\n            children: [/*#__PURE__*/_jsxDEV(PathField, {\n              value: settings.dcsPath,\n              onChange: e => handleSettingChange('dcsPath', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BrowseButton, {\n              onClick: () => handleBrowsePath('DCS安装'),\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\u6D4F\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u5B58\\u6863\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"DCS \\u5B58\\u6863\\u548C\\u914D\\u7F6E\\u6587\\u4EF6\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(PathInput, {\n            children: [/*#__PURE__*/_jsxDEV(PathField, {\n              value: settings.savedGamesPath,\n              onChange: e => handleSettingChange('savedGamesPath', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BrowseButton, {\n              onClick: () => handleBrowsePath('存档'),\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\u6D4F\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u542F\\u52A8\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u81EA\\u52A8\\u542F\\u52A8 DCS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u542F\\u52A8\\u5668\\u6253\\u5F00\\u65F6\\u81EA\\u52A8\\u542F\\u52A8 DCS World\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.autoStart,\n              onChange: e => handleSettingChange('autoStart', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u542F\\u7528 VR \\u6A21\\u5F0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u4F7F\\u7528 VR \\u5934\\u663E\\u542F\\u52A8 DCS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableVR,\n              onChange: e => handleSettingChange('enableVR', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u56FE\\u5F62\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u5206\\u8FA8\\u7387\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u6E38\\u620F\\u663E\\u793A\\u5206\\u8FA8\\u7387\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.resolution,\n            onChange: e => handleSettingChange('resolution', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1920x1080\",\n              children: \"1920x1080\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"2560x1440\",\n              children: \"2560x1440\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"3840x2160\",\n              children: \"3840x2160\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1366x768\",\n              children: \"1366x768\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u56FE\\u5F62\\u8D28\\u91CF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u9884\\u8BBE\\u56FE\\u5F62\\u8D28\\u91CF\\u7B49\\u7EA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.graphics,\n            onChange: e => handleSettingChange('graphics', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"low\",\n              children: \"\\u4F4E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"medium\",\n              children: \"\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"high\",\n              children: \"\\u9AD8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ultra\",\n              children: \"\\u6781\\u9AD8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n  const renderLauncherSettings = () => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u542F\\u52A8\\u5668\\u884C\\u4E3A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u5F00\\u673A\\u81EA\\u542F\\u52A8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u7CFB\\u7EDF\\u542F\\u52A8\\u65F6\\u81EA\\u52A8\\u8FD0\\u884C\\u542F\\u52A8\\u5668\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.autoLaunch,\n              onChange: e => handleSettingChange('autoLaunch', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u6700\\u5C0F\\u5316\\u5230\\u7CFB\\u7EDF\\u6258\\u76D8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u5173\\u95ED\\u7A97\\u53E3\\u65F6\\u6700\\u5C0F\\u5316\\u5230\\u7CFB\\u7EDF\\u6258\\u76D8\\u800C\\u4E0D\\u662F\\u9000\\u51FA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.minimizeToTray,\n              onChange: e => handleSettingChange('minimizeToTray', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u663E\\u793A\\u901A\\u77E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u663E\\u793A\\u66F4\\u65B0\\u3001\\u4E0B\\u8F7D\\u5B8C\\u6210\\u7B49\\u901A\\u77E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.showNotifications,\n              onChange: e => handleSettingChange('showNotifications', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u754C\\u9762\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u8BED\\u8A00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u542F\\u52A8\\u5668\\u754C\\u9762\\u8BED\\u8A00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.language,\n            onChange: e => handleSettingChange('language', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"zh-CN\",\n              children: \"\\u7B80\\u4F53\\u4E2D\\u6587\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"en-US\",\n              children: \"English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ru-RU\",\n              children: \"\\u0420\\u0443\\u0441\\u0441\\u043A\\u0438\\u0439\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u4E3B\\u9898\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u542F\\u52A8\\u5668\\u5916\\u89C2\\u4E3B\\u9898\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.theme,\n            onChange: e => handleSettingChange('theme', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"dark\",\n              children: \"\\u6DF1\\u8272\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"light\",\n              children: \"\\u6D45\\u8272\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"auto\",\n              children: \"\\u8DDF\\u968F\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n  const renderNetworkSettings = () => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u4E0B\\u8F7D\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u81EA\\u52A8\\u4E0B\\u8F7D\\u66F4\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u68C0\\u6D4B\\u5230\\u66F4\\u65B0\\u65F6\\u81EA\\u52A8\\u5F00\\u59CB\\u4E0B\\u8F7D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Toggle, {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.autoDownload,\n              onChange: e => handleSettingChange('autoDownload', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u4E0B\\u8F7D\\u76EE\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u6A21\\u7EC4\\u548C\\u66F4\\u65B0\\u6587\\u4EF6\\u7684\\u4E0B\\u8F7D\\u4F4D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(PathInput, {\n            children: [/*#__PURE__*/_jsxDEV(PathField, {\n              value: settings.downloadPath,\n              onChange: e => handleSettingChange('downloadPath', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BrowseButton, {\n              onClick: () => handleBrowsePath('下载'),\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"\\u6D4F\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u6700\\u5927\\u540C\\u65F6\\u4E0B\\u8F7D\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u540C\\u65F6\\u8FDB\\u884C\\u7684\\u6700\\u5927\\u4E0B\\u8F7D\\u4EFB\\u52A1\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.maxDownloads,\n            onChange: e => handleSettingChange('maxDownloads', parseInt(e.target.value)),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 1,\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 2,\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 3,\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 5,\n              children: \"5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u5E26\\u5BBD\\u9650\\u5236\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u4E0B\\u8F7D\\u901F\\u5EA6\\u9650\\u5236\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: settings.bandwidth,\n            onChange: e => handleSettingChange('bandwidth', e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"unlimited\",\n              children: \"\\u65E0\\u9650\\u5236\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"1mb\",\n              children: \"1 MB/s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"5mb\",\n              children: \"5 MB/s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"10mb\",\n              children: \"10 MB/s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n  const renderAccountSettings = () => /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [user && /*#__PURE__*/_jsxDEV(UserProfile, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        children: user.username ? user.username.charAt(0).toUpperCase() : 'U'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n        children: [/*#__PURE__*/_jsxDEV(UserName, {\n          children: user.username || '未知用户'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(UserEmail, {\n          children: user.email || '<EMAIL>'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n        onClick: logout,\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        children: \"\\u767B\\u51FA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(SettingSection, {\n      children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n        children: \"\\u8D26\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u66F4\\u6539\\u8D26\\u6237\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: () => alert('修改密码功能暂未实现'),\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingLabel, {\n          children: [/*#__PURE__*/_jsxDEV(SettingName, {\n            children: \"\\u66F4\\u65B0\\u4E2A\\u4EBA\\u8D44\\u6599\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SettingDescription, {\n            children: \"\\u4FEE\\u6539\\u7528\\u6237\\u540D\\u3001\\u90AE\\u7BB1\\u7B49\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SettingControl, {\n          children: /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: () => alert('更新资料功能暂未实现'),\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: \"\\u7F16\\u8F91\\u8D44\\u6599\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n  const renderCurrentPanel = () => {\n    switch (activeSection) {\n      case 'game':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(PanelTitle, {\n            children: \"\\u6E38\\u620F\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this), renderGameSettings()]\n        }, void 0, true);\n      case 'launcher':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(PanelTitle, {\n            children: \"\\u542F\\u52A8\\u5668\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), renderLauncherSettings()]\n        }, void 0, true);\n      case 'network':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(PanelTitle, {\n            children: \"\\u7F51\\u7EDC\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this), renderNetworkSettings()]\n        }, void 0, true);\n      case 'account':\n        return /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(PanelTitle, {\n            children: \"\\u8D26\\u6237\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this), renderAccountSettings()]\n        }, void 0, true);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SettingsContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u7CFB\\u7EDF\\u9009\\u9879\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeaderActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleResetSettings,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          primary: true,\n          onClick: handleSaveSettings,\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: \"\\uD83D\\uDCBE \\u4FDD\\u5B58\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsContent, {\n      children: [/*#__PURE__*/_jsxDEV(SettingsSidebar, {\n        children: [/*#__PURE__*/_jsxDEV(SidebarItem, {\n          active: activeSection === 'game',\n          onClick: () => setActiveSection('game'),\n          whileHover: {\n            x: 4\n          },\n          children: \"\\uD83C\\uDFAE \\u6E38\\u620F\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SidebarItem, {\n          active: activeSection === 'launcher',\n          onClick: () => setActiveSection('launcher'),\n          whileHover: {\n            x: 4\n          },\n          children: \"\\uD83D\\uDE80 \\u542F\\u52A8\\u5668\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SidebarItem, {\n          active: activeSection === 'network',\n          onClick: () => setActiveSection('network'),\n          whileHover: {\n            x: 4\n          },\n          children: \"\\uD83C\\uDF10 \\u7F51\\u7EDC\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 807,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SidebarItem, {\n          active: activeSection === 'account',\n          onClick: () => setActiveSection('account'),\n          whileHover: {\n            x: 4\n          },\n          children: \"\\uD83D\\uDC64 \\u8D26\\u6237\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SettingsPanel, {\n        children: renderCurrentPanel()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 791,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 769,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"52k8hGxwFdynCuo9F30wmu6woww=\", false, function () {\n  return [useAuth];\n});\n_c26 = SettingsPage;\nexport default SettingsPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26;\n$RefreshReg$(_c, \"SettingsContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"HeaderActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"SettingsContent\");\n$RefreshReg$(_c7, \"SettingsSidebar\");\n$RefreshReg$(_c8, \"SidebarItem\");\n$RefreshReg$(_c9, \"SettingsPanel\");\n$RefreshReg$(_c0, \"PanelTitle\");\n$RefreshReg$(_c1, \"SettingSection\");\n$RefreshReg$(_c10, \"SectionTitle\");\n$RefreshReg$(_c11, \"SettingItem\");\n$RefreshReg$(_c12, \"SettingLabel\");\n$RefreshReg$(_c13, \"SettingName\");\n$RefreshReg$(_c14, \"SettingDescription\");\n$RefreshReg$(_c15, \"SettingControl\");\n$RefreshReg$(_c16, \"Toggle\");\n$RefreshReg$(_c17, \"Select\");\n$RefreshReg$(_c18, \"PathInput\");\n$RefreshReg$(_c19, \"PathField\");\n$RefreshReg$(_c20, \"BrowseButton\");\n$RefreshReg$(_c21, \"UserProfile\");\n$RefreshReg$(_c22, \"Avatar\");\n$RefreshReg$(_c23, \"UserInfo\");\n$RefreshReg$(_c24, \"UserName\");\n$RefreshReg$(_c25, \"UserEmail\");\n$RefreshReg$(_c26, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "motion", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsContainer", "div", "_c", "Header", "_c2", "Title", "h1", "props", "theme", "fontSizes", "fontWeights", "bold", "colors", "text", "primary", "_c3", "HeaderActions", "_c4", "ActionButton", "button", "border", "sm", "_c5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c6", "SettingsSidebar", "_c7", "SidebarItem", "active", "secondary", "_c8", "SettingsPanel", "_c9", "PanelTitle", "h2", "xl", "semibold", "_c0", "SettingSection", "_c1", "SectionTitle", "h3", "lg", "medium", "_c10", "SettingItem", "_c11", "SettingLabel", "_c12", "SettingName", "_c13", "SettingDescription", "xs", "tertiary", "_c14", "SettingControl", "_c15", "Toggle", "label", "background", "_c16", "Select", "select", "_c17", "Input", "input", "PathInput", "_c18", "PathField", "_c19", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c20", "UserProfile", "_c21", "Avatar", "_c22", "UserInfo", "_c23", "UserName", "_c24", "UserEmail", "_c25", "SettingsPage", "_s", "user", "logout", "activeSection", "setActiveSection", "settings", "setSettings", "dcs<PERSON>ath", "savedGamesPath", "autoStart", "checkUpdates", "enableVR", "resolution", "graphics", "autoLaunch", "minimizeToTray", "showNotifications", "language", "autoDownload", "downloadPath", "maxDownloads", "bandwidth", "handleSettingChange", "key", "value", "prev", "handleBrowsePath", "pathType", "alert", "handleSaveSettings", "localStorage", "setItem", "JSON", "stringify", "handleResetSettings", "window", "confirm", "renderGameSettings", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "onClick", "whileHover", "scale", "whileTap", "type", "checked", "renderLauncherSettings", "renderNetworkSettings", "parseInt", "renderAccountSettings", "username", "char<PERSON>t", "toUpperCase", "email", "renderCurrentPanel", "x", "_c26", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/pages/SettingsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\n\nconst SettingsContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 16px;\n`;\n\nconst Title = styled.h1`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0;\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n  align-items: center;\n`;\n\nconst ActionButton = styled(motion.button)`\n  padding: 8px 16px;\n  background: ${props => props.primary ? \n    'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)' : \n    'rgba(255, 255, 255, 0.1)'\n  };\n  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.primary ? 'white' : props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.primary ? \n      'linear-gradient(135deg, #ff8555 0%, #ff6b35 100%)' : \n      'rgba(255, 255, 255, 0.15)'\n    };\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n`;\n\nconst SettingsContent = styled.div`\n  display: flex;\n  gap: 24px;\n  flex: 1;\n  overflow: hidden;\n`;\n\nconst SettingsSidebar = styled.div`\n  width: 250px;\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst SidebarItem = styled(motion.button)`\n  padding: 12px 16px;\n  background: ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  border: none;\n  border-radius: 8px;\n  color: ${props => props.active ? 'white' : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.active ? props.theme.colors.primary : 'rgba(255, 255, 255, 0.05)'};\n    color: ${props => props.active ? 'white' : props.theme.colors.text.primary};\n  }\n`;\n\nconst SettingsPanel = styled.div`\n  flex: 1;\n  background: rgba(45, 45, 45, 0.8);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 12px;\n  padding: 24px;\n  overflow-y: auto;\n`;\n\nconst PanelTitle = styled.h2`\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0 0 24px 0;\n`;\n\nconst SettingSection = styled.div`\n  margin-bottom: 32px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin: 0 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n\nconst SettingItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst SettingLabel = styled.div`\n  flex: 1;\n`;\n\nconst SettingName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 4px;\n`;\n\nconst SettingDescription = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  line-height: 1.4;\n`;\n\nconst SettingControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst Toggle = styled.label`\n  position: relative;\n  display: inline-block;\n  width: 44px;\n  height: 24px;\n  \n  input {\n    opacity: 0;\n    width: 0;\n    height: 0;\n  }\n  \n  span {\n    position: absolute;\n    cursor: pointer;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: ${props => props.theme.colors.background.tertiary};\n    transition: 0.3s;\n    border-radius: 24px;\n    \n    &:before {\n      position: absolute;\n      content: \"\";\n      height: 18px;\n      width: 18px;\n      left: 3px;\n      bottom: 3px;\n      background-color: white;\n      transition: 0.3s;\n      border-radius: 50%;\n    }\n  }\n  \n  input:checked + span {\n    background-color: ${props => props.theme.colors.primary};\n  }\n  \n  input:checked + span:before {\n    transform: translateX(20px);\n  }\n`;\n\nconst Select = styled.select`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  cursor: pointer;\n  min-width: 120px;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  option {\n    background: ${props => props.theme.colors.background.secondary};\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n\nconst Input = styled.input`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  min-width: 120px;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n  }\n  \n  &::placeholder {\n    color: ${props => props.theme.colors.text.tertiary};\n  }\n`;\n\nconst PathInput = styled.div`\n  display: flex;\n  gap: 8px;\n  align-items: center;\n`;\n\nconst PathField = styled(Input)`\n  flex: 1;\n  min-width: 200px;\n`;\n\nconst BrowseButton = styled(motion.button)`\n  padding: 6px 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 6px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.xs};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.15);\n  }\n`;\n\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid ${props => props.theme.colors.border.secondary};\n  border-radius: 12px;\n  margin-bottom: 24px;\n`;\n\nconst Avatar = styled.div`\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: ${props => props.theme.fontSizes.xl};\n  font-weight: ${props => props.theme.fontWeights.bold};\n`;\n\nconst UserInfo = styled.div`\n  flex: 1;\n`;\n\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.semibold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 4px;\n`;\n\nconst UserEmail = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n\nconst SettingsPage = () => {\n  const { user, logout } = useAuth();\n  const [activeSection, setActiveSection] = useState('game');\n  const [settings, setSettings] = useState({\n    // 游戏设置\n    dcsPath: 'C:\\\\Program Files\\\\Eagle Dynamics\\\\DCS World',\n    savedGamesPath: '%USERPROFILE%\\\\Saved Games\\\\DCS',\n    autoStart: true,\n    checkUpdates: true,\n    enableVR: false,\n    resolution: '1920x1080',\n    graphics: 'high',\n    \n    // 启动器设置\n    autoLaunch: false,\n    minimizeToTray: true,\n    showNotifications: true,\n    language: 'zh-CN',\n    theme: 'dark',\n    \n    // 网络设置\n    autoDownload: true,\n    downloadPath: 'D:\\\\DCS Downloads',\n    maxDownloads: 3,\n    bandwidth: 'unlimited'\n  });\n\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({ ...prev, [key]: value }));\n  };\n\n  const handleBrowsePath = (pathType) => {\n    // 模拟文件夹选择\n    alert(`选择${pathType}路径的功能需要在Electron环境中实现`);\n  };\n\n  const handleSaveSettings = () => {\n    // 保存设置到本地存储或发送到服务器\n    localStorage.setItem('launcherSettings', JSON.stringify(settings));\n    alert('设置已保存');\n  };\n\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      // 重置为默认设置\n      setSettings({\n        dcsPath: 'C:\\\\Program Files\\\\Eagle Dynamics\\\\DCS World',\n        savedGamesPath: '%USERPROFILE%\\\\Saved Games\\\\DCS',\n        autoStart: true,\n        checkUpdates: true,\n        enableVR: false,\n        resolution: '1920x1080',\n        graphics: 'high',\n        autoLaunch: false,\n        minimizeToTray: true,\n        showNotifications: true,\n        language: 'zh-CN',\n        theme: 'dark',\n        autoDownload: true,\n        downloadPath: 'D:\\\\DCS Downloads',\n        maxDownloads: 3,\n        bandwidth: 'unlimited'\n      });\n    }\n  };\n\n  const renderGameSettings = () => (\n    <>\n      <SettingSection>\n        <SectionTitle>DCS 安装路径</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>DCS World 安装目录</SettingName>\n            <SettingDescription>DCS World 的主安装目录</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <PathInput>\n              <PathField\n                value={settings.dcsPath}\n                onChange={(e) => handleSettingChange('dcsPath', e.target.value)}\n              />\n              <BrowseButton\n                onClick={() => handleBrowsePath('DCS安装')}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                浏览\n              </BrowseButton>\n            </PathInput>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>存档目录</SettingName>\n            <SettingDescription>DCS 存档和配置文件目录</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <PathInput>\n              <PathField\n                value={settings.savedGamesPath}\n                onChange={(e) => handleSettingChange('savedGamesPath', e.target.value)}\n              />\n              <BrowseButton\n                onClick={() => handleBrowsePath('存档')}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                浏览\n              </BrowseButton>\n            </PathInput>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n\n      <SettingSection>\n        <SectionTitle>启动选项</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>自动启动 DCS</SettingName>\n            <SettingDescription>启动器打开时自动启动 DCS World</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoStart}\n                onChange={(e) => handleSettingChange('autoStart', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>启用 VR 模式</SettingName>\n            <SettingDescription>使用 VR 头显启动 DCS</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableVR}\n                onChange={(e) => handleSettingChange('enableVR', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n\n      <SettingSection>\n        <SectionTitle>图形设置</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>分辨率</SettingName>\n            <SettingDescription>游戏显示分辨率</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.resolution}\n              onChange={(e) => handleSettingChange('resolution', e.target.value)}\n            >\n              <option value=\"1920x1080\">1920x1080</option>\n              <option value=\"2560x1440\">2560x1440</option>\n              <option value=\"3840x2160\">3840x2160</option>\n              <option value=\"1366x768\">1366x768</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>图形质量</SettingName>\n            <SettingDescription>预设图形质量等级</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.graphics}\n              onChange={(e) => handleSettingChange('graphics', e.target.value)}\n            >\n              <option value=\"low\">低</option>\n              <option value=\"medium\">中</option>\n              <option value=\"high\">高</option>\n              <option value=\"ultra\">极高</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n    </>\n  );\n\n  const renderLauncherSettings = () => (\n    <>\n      <SettingSection>\n        <SectionTitle>启动器行为</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>开机自启动</SettingName>\n            <SettingDescription>系统启动时自动运行启动器</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoLaunch}\n                onChange={(e) => handleSettingChange('autoLaunch', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>最小化到系统托盘</SettingName>\n            <SettingDescription>关闭窗口时最小化到系统托盘而不是退出</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.minimizeToTray}\n                onChange={(e) => handleSettingChange('minimizeToTray', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>显示通知</SettingName>\n            <SettingDescription>显示更新、下载完成等通知</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.showNotifications}\n                onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n\n      <SettingSection>\n        <SectionTitle>界面设置</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>语言</SettingName>\n            <SettingDescription>启动器界面语言</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.language}\n              onChange={(e) => handleSettingChange('language', e.target.value)}\n            >\n              <option value=\"zh-CN\">简体中文</option>\n              <option value=\"en-US\">English</option>\n              <option value=\"ru-RU\">Русский</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>主题</SettingName>\n            <SettingDescription>启动器外观主题</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.theme}\n              onChange={(e) => handleSettingChange('theme', e.target.value)}\n            >\n              <option value=\"dark\">深色</option>\n              <option value=\"light\">浅色</option>\n              <option value=\"auto\">跟随系统</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n    </>\n  );\n\n  const renderNetworkSettings = () => (\n    <>\n      <SettingSection>\n        <SectionTitle>下载设置</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>自动下载更新</SettingName>\n            <SettingDescription>检测到更新时自动开始下载</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Toggle>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoDownload}\n                onChange={(e) => handleSettingChange('autoDownload', e.target.checked)}\n              />\n              <span></span>\n            </Toggle>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>下载目录</SettingName>\n            <SettingDescription>模组和更新文件的下载位置</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <PathInput>\n              <PathField\n                value={settings.downloadPath}\n                onChange={(e) => handleSettingChange('downloadPath', e.target.value)}\n              />\n              <BrowseButton\n                onClick={() => handleBrowsePath('下载')}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                浏览\n              </BrowseButton>\n            </PathInput>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>最大同时下载数</SettingName>\n            <SettingDescription>同时进行的最大下载任务数</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.maxDownloads}\n              onChange={(e) => handleSettingChange('maxDownloads', parseInt(e.target.value))}\n            >\n              <option value={1}>1</option>\n              <option value={2}>2</option>\n              <option value={3}>3</option>\n              <option value={5}>5</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>带宽限制</SettingName>\n            <SettingDescription>下载速度限制</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <Select\n              value={settings.bandwidth}\n              onChange={(e) => handleSettingChange('bandwidth', e.target.value)}\n            >\n              <option value=\"unlimited\">无限制</option>\n              <option value=\"1mb\">1 MB/s</option>\n              <option value=\"5mb\">5 MB/s</option>\n              <option value=\"10mb\">10 MB/s</option>\n            </Select>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n    </>\n  );\n\n  const renderAccountSettings = () => (\n    <>\n      {user && (\n        <UserProfile>\n          <Avatar>\n            {user.username ? user.username.charAt(0).toUpperCase() : 'U'}\n          </Avatar>\n          <UserInfo>\n            <UserName>{user.username || '未知用户'}</UserName>\n            <UserEmail>{user.email || '<EMAIL>'}</UserEmail>\n          </UserInfo>\n          <ActionButton\n            onClick={logout}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            登出\n          </ActionButton>\n        </UserProfile>\n      )}\n\n      <SettingSection>\n        <SectionTitle>账户管理</SectionTitle>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>修改密码</SettingName>\n            <SettingDescription>更改账户密码</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <ActionButton\n              onClick={() => alert('修改密码功能暂未实现')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              修改密码\n            </ActionButton>\n          </SettingControl>\n        </SettingItem>\n        <SettingItem>\n          <SettingLabel>\n            <SettingName>更新个人资料</SettingName>\n            <SettingDescription>修改用户名、邮箱等信息</SettingDescription>\n          </SettingLabel>\n          <SettingControl>\n            <ActionButton\n              onClick={() => alert('更新资料功能暂未实现')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              编辑资料\n            </ActionButton>\n          </SettingControl>\n        </SettingItem>\n      </SettingSection>\n    </>\n  );\n\n  const renderCurrentPanel = () => {\n    switch (activeSection) {\n      case 'game':\n        return (\n          <>\n            <PanelTitle>游戏设置</PanelTitle>\n            {renderGameSettings()}\n          </>\n        );\n      case 'launcher':\n        return (\n          <>\n            <PanelTitle>启动器设置</PanelTitle>\n            {renderLauncherSettings()}\n          </>\n        );\n      case 'network':\n        return (\n          <>\n            <PanelTitle>网络设置</PanelTitle>\n            {renderNetworkSettings()}\n          </>\n        );\n      case 'account':\n        return (\n          <>\n            <PanelTitle>账户设置</PanelTitle>\n            {renderAccountSettings()}\n          </>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <SettingsContainer>\n      <Header>\n        <Title>系统选项</Title>\n        <HeaderActions>\n          <ActionButton\n            onClick={handleResetSettings}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            🔄 重置设置\n          </ActionButton>\n          <ActionButton\n            primary\n            onClick={handleSaveSettings}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            💾 保存设置\n          </ActionButton>\n        </HeaderActions>\n      </Header>\n\n      <SettingsContent>\n        <SettingsSidebar>\n          <SidebarItem\n            active={activeSection === 'game'}\n            onClick={() => setActiveSection('game')}\n            whileHover={{ x: 4 }}\n          >\n            🎮 游戏设置\n          </SidebarItem>\n          <SidebarItem\n            active={activeSection === 'launcher'}\n            onClick={() => setActiveSection('launcher')}\n            whileHover={{ x: 4 }}\n          >\n            🚀 启动器设置\n          </SidebarItem>\n          <SidebarItem\n            active={activeSection === 'network'}\n            onClick={() => setActiveSection('network')}\n            whileHover={{ x: 4 }}\n          >\n            🌐 网络设置\n          </SidebarItem>\n          <SidebarItem\n            active={activeSection === 'account'}\n            onClick={() => setActiveSection('account')}\n            whileHover={{ x: 4 }}\n          >\n            👤 账户设置\n          </SidebarItem>\n        </SettingsSidebar>\n\n        <SettingsPanel>\n          {renderCurrentPanel()}\n        </SettingsPanel>\n      </SettingsContent>\n    </SettingsContainer>\n  );\n};\n\nexport default SettingsPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,iBAAiB,GAAGP,MAAM,CAACQ,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,iBAAiB;AAQvB,MAAMG,MAAM,GAAGV,MAAM,CAACQ,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,MAAM;AAQZ,MAAME,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB,eAAeC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBF,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACC,IAAI;AACtD,WAAWJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA,CAAC;AAACC,GAAA,GALIV,KAAK;AAOX,MAAMW,aAAa,GAAGvB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGzB,MAAM,CAACC,MAAM,CAACyB,MAAM,CAAC;AAC1C;AACA,gBAAgBZ,KAAK,IAAIA,KAAK,CAACO,OAAO,GAClC,mDAAmD,GACnD,0BAA0B;AAC9B,sBACsBP,KAAK,IAAIA,KAAK,CAACO,OAAO,GAAG,aAAa,GAAGP,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChG;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACO,OAAO,GAAG,OAAO,GAAGP,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AAC7E,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA,kBAAkBd,KAAK,IAAIA,KAAK,CAACO,OAAO,GAClC,mDAAmD,GACnD,2BAA2B;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACQ,GAAA,GAxBIJ,YAAY;AA0BlB,MAAMK,eAAe,GAAG9B,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GALID,eAAe;AAOrB,MAAME,eAAe,GAAGhC,MAAM,CAACQ,GAAG;AAClC;AACA;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GATID,eAAe;AAWrB,MAAME,WAAW,GAAGlC,MAAM,CAACC,MAAM,CAACyB,MAAM,CAAC;AACzC;AACA,gBAAgBZ,KAAK,IAAIA,KAAK,CAACqB,MAAM,GAAGrB,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO,GAAG,aAAa;AAClF;AACA;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACqB,MAAM,GAAG,OAAO,GAAGrB,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AAC9E,eAAetB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA;AACA,kBAAkBd,KAAK,IAAIA,KAAK,CAACqB,MAAM,GAAGrB,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO,GAAG,2BAA2B;AAClG,aAAaP,KAAK,IAAIA,KAAK,CAACqB,MAAM,GAAG,OAAO,GAAGrB,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AAC9E;AACA,CAAC;AAACgB,GAAA,GAfIH,WAAW;AAiBjB,MAAMI,aAAa,GAAGtC,MAAM,CAACQ,GAAG;AAChC;AACA;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAPID,aAAa;AASnB,MAAME,UAAU,GAAGxC,MAAM,CAACyC,EAAE;AAC5B,eAAe3B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC0B,EAAE;AAChD,iBAAiB5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAAC0B,QAAQ;AAC1D,WAAW7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA,CAAC;AAACuB,GAAA,GALIJ,UAAU;AAOhB,MAAMK,cAAc,GAAG7C,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,GAAA,GANID,cAAc;AAQpB,MAAME,YAAY,GAAG/C,MAAM,CAACgD,EAAE;AAC9B,eAAelC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACiC,EAAE;AAChD,iBAAiBnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACiC,MAAM;AACxD,WAAWpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA;AACA,6BAA6BP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACS,SAAS;AACzE,CAAC;AAACe,IAAA,GAPIJ,YAAY;AASlB,MAAMK,WAAW,GAAGpD,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,6BAA6BM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACS,SAAS;AACzE;AACA;AACA;AACA;AACA,CAAC;AAACiB,IAAA,GAVID,WAAW;AAYjB,MAAME,YAAY,GAAGtD,MAAM,CAACQ,GAAG;AAC/B;AACA,CAAC;AAAC+C,IAAA,GAFID,YAAY;AAIlB,MAAME,WAAW,GAAGxD,MAAM,CAACQ,GAAG;AAC9B,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,iBAAiBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACiC,MAAM;AACxD,WAAWpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA,CAAC;AAACoC,IAAA,GALID,WAAW;AAOjB,MAAME,kBAAkB,GAAG1D,MAAM,CAACQ,GAAG;AACrC,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC2C,EAAE;AAChD,WAAW7C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwC,QAAQ;AACpD;AACA,CAAC;AAACC,IAAA,GAJIH,kBAAkB;AAMxB,MAAMI,cAAc,GAAG9D,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GAJID,cAAc;AAMpB,MAAME,MAAM,GAAGhE,MAAM,CAACiE,KAAK;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBnD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAAC+C,UAAU,CAACN,QAAQ;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB9C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AAC3D;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GA3CIH,MAAM;AA6CZ,MAAMI,MAAM,GAAGpE,MAAM,CAACqE,MAAM;AAC5B;AACA;AACA,sBAAsBvD,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AACvD;AACA;AACA;AACA,kBAAkBP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAAC+C,UAAU,CAAC9B,SAAS;AAClE,aAAatB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACrD;AACA,CAAC;AAACiD,IAAA,GAnBIF,MAAM;AAqBZ,MAAMG,KAAK,GAAGvE,MAAM,CAACwE,KAAK;AAC1B;AACA;AACA,sBAAsB1D,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD;AACA;AACA;AACA;AACA,oBAAoBd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACE,OAAO;AACvD;AACA;AACA;AACA,aAAaP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACwC,QAAQ;AACtD;AACA,CAAC;AAED,MAAMa,SAAS,GAAGzE,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACkE,IAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAG3E,MAAM,CAACuE,KAAK,CAAC;AAC/B;AACA;AACA,CAAC;AAACK,IAAA,GAHID,SAAS;AAKf,MAAME,YAAY,GAAG7E,MAAM,CAACC,MAAM,CAACyB,MAAM,CAAC;AAC1C;AACA;AACA,sBAAsBZ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACN,OAAO;AAChE;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD,eAAeP,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC2C,EAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,IAAA,GAbID,YAAY;AAelB,MAAME,WAAW,GAAG/E,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,sBAAsBM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACQ,MAAM,CAACS,SAAS;AAClE;AACA;AACA,CAAC;AAAC4C,IAAA,GATID,WAAW;AAWjB,MAAME,MAAM,GAAGjF,MAAM,CAACQ,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAAC0B,EAAE;AAChD,iBAAiB5B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAACC,IAAI;AACtD,CAAC;AAACgE,IAAA,GAXID,MAAM;AAaZ,MAAME,QAAQ,GAAGnF,MAAM,CAACQ,GAAG;AAC3B;AACA,CAAC;AAAC4E,IAAA,GAFID,QAAQ;AAId,MAAME,QAAQ,GAAGrF,MAAM,CAACQ,GAAG;AAC3B,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACiC,EAAE;AAChD,iBAAiBnC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACE,WAAW,CAAC0B,QAAQ;AAC1D,WAAW7B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACC,OAAO;AACnD;AACA,CAAC;AAACiE,IAAA,GALID,QAAQ;AAOd,MAAME,SAAS,GAAGvF,MAAM,CAACQ,GAAG;AAC5B,eAAeM,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,SAAS,CAACY,EAAE;AAChD,WAAWd,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,MAAM,CAACC,IAAI,CAACgB,SAAS;AACrD,CAAC;AAACoD,IAAA,GAHID,SAAS;AAKf,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAG1F,OAAO,CAAC,CAAC;EAClC,MAAM,CAAC2F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,MAAM,CAAC;EAC1D,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,QAAQ,CAAC;IACvC;IACAmG,OAAO,EAAE,8CAA8C;IACvDC,cAAc,EAAE,iCAAiC;IACjDC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE,MAAM;IAEhB;IACAC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE,IAAI;IACvBC,QAAQ,EAAE,OAAO;IACjB5F,KAAK,EAAE,MAAM;IAEb;IACA6F,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC1ClB,WAAW,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,GAAG,GAAGC;IAAM,CAAC,CAAC,CAAC;EAClD,CAAC;EAED,MAAME,gBAAgB,GAAIC,QAAQ,IAAK;IACrC;IACAC,KAAK,CAAC,KAAKD,QAAQ,uBAAuB,CAAC;EAC7C,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAC5B,QAAQ,CAAC,CAAC;IAClEuB,KAAK,CAAC,OAAO,CAAC;EAChB,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACjC;MACA9B,WAAW,CAAC;QACVC,OAAO,EAAE,8CAA8C;QACvDC,cAAc,EAAE,iCAAiC;QACjDC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE,WAAW;QACvBC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,IAAI;QACpBC,iBAAiB,EAAE,IAAI;QACvBC,QAAQ,EAAE,OAAO;QACjB5F,KAAK,EAAE,MAAM;QACb6F,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,mBAAmB;QACjCC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAGA,CAAA,kBACzB3H,OAAA,CAAAE,SAAA;IAAA0H,QAAA,gBACE5H,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACrChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACqE,SAAS;YAAAuD,QAAA,gBACR5H,OAAA,CAACuE,SAAS;cACRuC,KAAK,EAAEnB,QAAQ,CAACE,OAAQ;cACxBoC,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,SAAS,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACFhI,OAAA,CAACyE,YAAY;cACX2D,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAC,OAAO,CAAE;cACzCqB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAV,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACqE,SAAS;YAAAuD,QAAA,gBACR5H,OAAA,CAACuE,SAAS;cACRuC,KAAK,EAAEnB,QAAQ,CAACG,cAAe;cAC/BmC,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,gBAAgB,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACFhI,OAAA,CAACyE,YAAY;cACX2D,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAC,IAAI,CAAE;cACtCqB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAV,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEjBhI,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACnChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACI,SAAU;cAC5BkC,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,WAAW,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACnChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACM,QAAS;cAC3BgC,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEjBhI,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAACO,UAAW;YAC3B+B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,YAAY,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAc,QAAA,gBAEnE5H,OAAA;cAAQ8G,KAAK,EAAC,WAAW;cAAAc,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ChI,OAAA;cAAQ8G,KAAK,EAAC,WAAW;cAAAc,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ChI,OAAA;cAAQ8G,KAAK,EAAC,WAAW;cAAAc,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5ChI,OAAA;cAAQ8G,KAAK,EAAC,UAAU;cAAAc,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAACQ,QAAS;YACzB8B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAc,QAAA,gBAEjE5H,OAAA;cAAQ8G,KAAK,EAAC,KAAK;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9BhI,OAAA;cAAQ8G,KAAK,EAAC,QAAQ;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjChI,OAAA;cAAQ8G,KAAK,EAAC,MAAM;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/BhI,OAAA;cAAQ8G,KAAK,EAAC,OAAO;cAAAc,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACjB,CACH;EAED,MAAMU,sBAAsB,GAAGA,CAAA,kBAC7B1I,OAAA,CAAAE,SAAA;IAAA0H,QAAA,gBACE5H,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAClChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACS,UAAW;cAC7B6B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,YAAY,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACnChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACU,cAAe;cACjC4B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,gBAAgB,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACW,iBAAkB;cACpC2B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,mBAAmB,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEjBhI,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAACY,QAAS;YACzB0B,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAc,QAAA,gBAEjE5H,OAAA;cAAQ8G,KAAK,EAAC,OAAO;cAAAc,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnChI,OAAA;cAAQ8G,KAAK,EAAC,OAAO;cAAAc,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtChI,OAAA;cAAQ8G,KAAK,EAAC,OAAO;cAAAc,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAAChF,KAAM;YACtBsH,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,OAAO,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAc,QAAA,gBAE9D5H,OAAA;cAAQ8G,KAAK,EAAC,MAAM;cAAAc,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChChI,OAAA;cAAQ8G,KAAK,EAAC,OAAO;cAAAc,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjChI,OAAA;cAAQ8G,KAAK,EAAC,MAAM;cAAAc,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACjB,CACH;EAED,MAAMW,qBAAqB,GAAGA,CAAA,kBAC5B3I,OAAA,CAAAE,SAAA;IAAA0H,QAAA,eACE5H,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACjChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAAC4D,MAAM;YAAAgE,QAAA,gBACL5H,OAAA;cACEwI,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE9C,QAAQ,CAACa,YAAa;cAC/ByB,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,cAAc,EAAEsB,CAAC,CAACC,MAAM,CAACM,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACFhI,OAAA;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACqE,SAAS;YAAAuD,QAAA,gBACR5H,OAAA,CAACuE,SAAS;cACRuC,KAAK,EAAEnB,QAAQ,CAACc,YAAa;cAC7BwB,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,cAAc,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACFhI,OAAA,CAACyE,YAAY;cACX2D,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAAC,IAAI,CAAE;cACtCqB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAAV,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAACe,YAAa;YAC7BuB,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,cAAc,EAAEgC,QAAQ,CAACV,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,CAAE;YAAAc,QAAA,gBAE/E5H,OAAA;cAAQ8G,KAAK,EAAE,CAAE;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BhI,OAAA;cAAQ8G,KAAK,EAAE,CAAE;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BhI,OAAA;cAAQ8G,KAAK,EAAE,CAAE;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BhI,OAAA;cAAQ8G,KAAK,EAAE,CAAE;cAAAc,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACgE,MAAM;YACL8C,KAAK,EAAEnB,QAAQ,CAACgB,SAAU;YAC1BsB,QAAQ,EAAGC,CAAC,IAAKtB,mBAAmB,CAAC,WAAW,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;YAAAc,QAAA,gBAElE5H,OAAA;cAAQ8G,KAAK,EAAC,WAAW;cAAAc,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtChI,OAAA;cAAQ8G,KAAK,EAAC,KAAK;cAAAc,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnChI,OAAA;cAAQ8G,KAAK,EAAC,KAAK;cAAAc,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnChI,OAAA;cAAQ8G,KAAK,EAAC,MAAM;cAAAc,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC,gBACjB,CACH;EAED,MAAMa,qBAAqB,GAAGA,CAAA,kBAC5B7I,OAAA,CAAAE,SAAA;IAAA0H,QAAA,GACGrC,IAAI,iBACHvF,OAAA,CAAC2E,WAAW;MAAAiD,QAAA,gBACV5H,OAAA,CAAC6E,MAAM;QAAA+C,QAAA,EACJrC,IAAI,CAACuD,QAAQ,GAAGvD,IAAI,CAACuD,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;MAAG;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACThI,OAAA,CAAC+E,QAAQ;QAAA6C,QAAA,gBACP5H,OAAA,CAACiF,QAAQ;UAAA2C,QAAA,EAAErC,IAAI,CAACuD,QAAQ,IAAI;QAAM;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAC9ChI,OAAA,CAACmF,SAAS;UAAAyC,QAAA,EAAErC,IAAI,CAAC0D,KAAK,IAAI;QAAkB;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACXhI,OAAA,CAACqB,YAAY;QACX+G,OAAO,EAAE5C,MAAO;QAChB6C,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAV,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACd,eAEDhI,OAAA,CAACyC,cAAc;MAAAmF,QAAA,gBACb5H,OAAA,CAAC2C,YAAY;QAAAiF,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACjChI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/BhI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACqB,YAAY;YACX+G,OAAO,EAAEA,CAAA,KAAMlB,KAAK,CAAC,YAAY,CAAE;YACnCmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAV,QAAA,EAC3B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACdhI,OAAA,CAACgD,WAAW;QAAA4E,QAAA,gBACV5H,OAAA,CAACkD,YAAY;UAAA0E,QAAA,gBACX5H,OAAA,CAACoD,WAAW;YAAAwE,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACjChI,OAAA,CAACsD,kBAAkB;YAAAsE,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACfhI,OAAA,CAAC0D,cAAc;UAAAkE,QAAA,eACb5H,OAAA,CAACqB,YAAY;YACX+G,OAAO,EAAEA,CAAA,KAAMlB,KAAK,CAAC,YAAY,CAAE;YACnCmB,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAV,QAAA,EAC3B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACjB,CACH;EAED,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQzD,aAAa;MACnB,KAAK,MAAM;QACT,oBACEzF,OAAA,CAAAE,SAAA;UAAA0H,QAAA,gBACE5H,OAAA,CAACoC,UAAU;YAAAwF,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC5BL,kBAAkB,CAAC,CAAC;QAAA,eACrB,CAAC;MAEP,KAAK,UAAU;QACb,oBACE3H,OAAA,CAAAE,SAAA;UAAA0H,QAAA,gBACE5H,OAAA,CAACoC,UAAU;YAAAwF,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC7BU,sBAAsB,CAAC,CAAC;QAAA,eACzB,CAAC;MAEP,KAAK,SAAS;QACZ,oBACE1I,OAAA,CAAAE,SAAA;UAAA0H,QAAA,gBACE5H,OAAA,CAACoC,UAAU;YAAAwF,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC5BW,qBAAqB,CAAC,CAAC;QAAA,eACxB,CAAC;MAEP,KAAK,SAAS;QACZ,oBACE3I,OAAA,CAAAE,SAAA;UAAA0H,QAAA,gBACE5H,OAAA,CAACoC,UAAU;YAAAwF,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC5Ba,qBAAqB,CAAC,CAAC;QAAA,eACxB,CAAC;MAEP;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE7I,OAAA,CAACG,iBAAiB;IAAAyH,QAAA,gBAChB5H,OAAA,CAACM,MAAM;MAAAsH,QAAA,gBACL5H,OAAA,CAACQ,KAAK;QAAAoH,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBhI,OAAA,CAACmB,aAAa;QAAAyG,QAAA,gBACZ5H,OAAA,CAACqB,YAAY;UACX+G,OAAO,EAAEZ,mBAAoB;UAC7Ba,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAV,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfhI,OAAA,CAACqB,YAAY;UACXJ,OAAO;UACPmH,OAAO,EAAEjB,kBAAmB;UAC5BkB,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAV,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEThI,OAAA,CAAC0B,eAAe;MAAAkG,QAAA,gBACd5H,OAAA,CAAC4B,eAAe;QAAAgG,QAAA,gBACd5H,OAAA,CAAC8B,WAAW;UACVC,MAAM,EAAE0D,aAAa,KAAK,MAAO;UACjC2C,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAAC,MAAM,CAAE;UACxC2C,UAAU,EAAE;YAAEc,CAAC,EAAE;UAAE,CAAE;UAAAvB,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdhI,OAAA,CAAC8B,WAAW;UACVC,MAAM,EAAE0D,aAAa,KAAK,UAAW;UACrC2C,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAAC,UAAU,CAAE;UAC5C2C,UAAU,EAAE;YAAEc,CAAC,EAAE;UAAE,CAAE;UAAAvB,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdhI,OAAA,CAAC8B,WAAW;UACVC,MAAM,EAAE0D,aAAa,KAAK,SAAU;UACpC2C,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAAC,SAAS,CAAE;UAC3C2C,UAAU,EAAE;YAAEc,CAAC,EAAE;UAAE,CAAE;UAAAvB,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACdhI,OAAA,CAAC8B,WAAW;UACVC,MAAM,EAAE0D,aAAa,KAAK,SAAU;UACpC2C,OAAO,EAAEA,CAAA,KAAM1C,gBAAgB,CAAC,SAAS,CAAE;UAC3C2C,UAAU,EAAE;YAAEc,CAAC,EAAE;UAAE,CAAE;UAAAvB,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAElBhI,OAAA,CAACkC,aAAa;QAAA0F,QAAA,EACXsB,kBAAkB,CAAC;MAAC;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAExB,CAAC;AAAC1C,EAAA,CAlgBID,YAAY;EAAA,QACSvF,OAAO;AAAA;AAAAsJ,IAAA,GAD5B/D,YAAY;AAogBlB,eAAeA,YAAY;AAAC,IAAAhF,EAAA,EAAAE,GAAA,EAAAW,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAgE,IAAA;AAAAC,YAAA,CAAAhJ,EAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAtG,IAAA;AAAAsG,YAAA,CAAApG,IAAA;AAAAoG,YAAA,CAAAlG,IAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAtF,IAAA;AAAAsF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}