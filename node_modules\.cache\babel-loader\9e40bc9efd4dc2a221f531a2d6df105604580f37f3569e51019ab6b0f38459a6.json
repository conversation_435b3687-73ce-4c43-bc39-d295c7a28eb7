{"ast": null, "code": "const theme = {\n  // 颜色系统\n  colors: {\n    // 主色调 - 橙色系\n    primary: '#ff6b35',\n    primaryHover: '#ff8555',\n    primaryActive: '#e55a2b',\n    primaryLight: '#ff9d75',\n    primaryDark: '#cc5429',\n    // 次要色调 - 蓝色系\n    secondary: '#4a9eff',\n    secondaryHover: '#6bb0ff',\n    secondaryActive: '#3a8eef',\n    // 强调色 - 紫色系\n    accent: '#8b5cf6',\n    accentHover: '#a78bfa',\n    accentActive: '#7c3aed',\n    // 背景色\n    background: {\n      primary: '#1a1a1a',\n      secondary: '#2d2d2d',\n      tertiary: '#3a3a3a',\n      quaternary: '#4a4a4a',\n      card: 'rgba(45, 45, 45, 0.8)',\n      cardHover: 'rgba(55, 55, 55, 0.9)',\n      overlay: 'rgba(0, 0, 0, 0.8)',\n      glass: 'rgba(26, 26, 26, 0.8)'\n    },\n    // 文本色\n    text: {\n      primary: '#ffffff',\n      secondary: '#cccccc',\n      tertiary: '#999999',\n      disabled: '#666666',\n      inverse: '#1a1a1a'\n    },\n    // 状态色\n    status: {\n      success: '#4caf50',\n      warning: '#ff9800',\n      error: '#f44336',\n      info: '#2196f3'\n    },\n    // 边框色\n    border: {\n      primary: 'rgba(255, 255, 255, 0.1)',\n      secondary: 'rgba(255, 255, 255, 0.05)',\n      tertiary: 'rgba(255, 255, 255, 0.02)',\n      active: '#ff6b35',\n      focus: '#ff8555',\n      error: '#f44336'\n    },\n    // 游戏相关色彩\n    game: {\n      installed: '#4caf50',\n      available: '#2196f3',\n      updating: '#ff9800',\n      error: '#f44336'\n    }\n  },\n  // 字体系统\n  fonts: {\n    primary: \"'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif\",\n    mono: \"'Consolas', 'Monaco', 'Courier New', monospace\"\n  },\n  // 字体大小\n  fontSizes: {\n    xs: '12px',\n    sm: '14px',\n    md: '16px',\n    lg: '18px',\n    xl: '20px',\n    '2xl': '24px',\n    '3xl': '30px',\n    '4xl': '36px'\n  },\n  // 字重\n  fontWeights: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700\n  },\n  // 间距系统\n  spacing: {\n    xs: '4px',\n    sm: '8px',\n    md: '16px',\n    lg: '24px',\n    xl: '32px',\n    '2xl': '48px',\n    '3xl': '64px'\n  },\n  // 圆角\n  borderRadius: {\n    sm: '4px',\n    md: '8px',\n    lg: '12px',\n    xl: '16px',\n    full: '50%'\n  },\n  // 阴影\n  shadows: {\n    sm: '0 2px 4px rgba(0, 0, 0, 0.1)',\n    md: '0 4px 8px rgba(0, 0, 0, 0.2)',\n    lg: '0 8px 16px rgba(0, 0, 0, 0.3)',\n    xl: '0 16px 32px rgba(0, 0, 0, 0.4)',\n    glow: '0 0 20px rgba(255, 107, 53, 0.3)',\n    glowHover: '0 0 30px rgba(255, 107, 53, 0.4)',\n    card: '0 4px 12px rgba(0, 0, 0, 0.15)',\n    cardHover: '0 8px 24px rgba(0, 0, 0, 0.25)'\n  },\n  // 过渡动画\n  transitions: {\n    fast: '0.15s ease',\n    normal: '0.3s ease',\n    slow: '0.5s ease'\n  },\n  // 层级\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060\n  },\n  // 断点\n  breakpoints: {\n    sm: '576px',\n    md: '768px',\n    lg: '992px',\n    xl: '1200px',\n    '2xl': '1400px'\n  },\n  // 组件特定样式\n  components: {\n    button: {\n      height: {\n        sm: '32px',\n        md: '40px',\n        lg: '48px'\n      },\n      padding: {\n        sm: '0 12px',\n        md: '0 16px',\n        lg: '0 24px'\n      }\n    },\n    input: {\n      height: {\n        sm: '32px',\n        md: '40px',\n        lg: '48px'\n      }\n    },\n    sidebar: {\n      width: '240px',\n      collapsedWidth: '60px'\n    },\n    titleBar: {\n      height: '32px'\n    }\n  }\n};\nexport default theme;", "map": {"version": 3, "names": ["theme", "colors", "primary", "primaryHover", "primaryActive", "primaryLight", "primaryDark", "secondary", "secondaryHover", "secondaryActive", "accent", "accentHover", "accentActive", "background", "tertiary", "quaternary", "card", "cardHover", "overlay", "glass", "text", "disabled", "inverse", "status", "success", "warning", "error", "info", "border", "active", "focus", "game", "installed", "available", "updating", "fonts", "mono", "fontSizes", "xs", "sm", "md", "lg", "xl", "fontWeights", "light", "normal", "medium", "semibold", "bold", "spacing", "borderRadius", "full", "shadows", "glow", "glowHover", "transitions", "fast", "slow", "zIndex", "dropdown", "sticky", "fixed", "modal", "popover", "tooltip", "breakpoints", "components", "button", "height", "padding", "input", "sidebar", "width", "collapsedWidth", "titleBar"], "sources": ["D:/Test/Battle Launcher/src/styles/theme.js"], "sourcesContent": ["const theme = {\n  // 颜色系统\n  colors: {\n    // 主色调 - 橙色系\n    primary: '#ff6b35',\n    primaryHover: '#ff8555',\n    primaryActive: '#e55a2b',\n    primaryLight: '#ff9d75',\n    primaryDark: '#cc5429',\n    \n    // 次要色调 - 蓝色系\n    secondary: '#4a9eff',\n    secondaryHover: '#6bb0ff',\n    secondaryActive: '#3a8eef',\n    \n    // 强调色 - 紫色系\n    accent: '#8b5cf6',\n    accentHover: '#a78bfa',\n    accentActive: '#7c3aed',\n    \n    // 背景色\n    background: {\n      primary: '#1a1a1a',\n      secondary: '#2d2d2d',\n      tertiary: '#3a3a3a',\n      quaternary: '#4a4a4a',\n      card: 'rgba(45, 45, 45, 0.8)',\n      cardHover: 'rgba(55, 55, 55, 0.9)',\n      overlay: 'rgba(0, 0, 0, 0.8)',\n      glass: 'rgba(26, 26, 26, 0.8)'\n    },\n    \n    // 文本色\n    text: {\n      primary: '#ffffff',\n      secondary: '#cccccc',\n      tertiary: '#999999',\n      disabled: '#666666',\n      inverse: '#1a1a1a'\n    },\n    \n    // 状态色\n    status: {\n      success: '#4caf50',\n      warning: '#ff9800',\n      error: '#f44336',\n      info: '#2196f3'\n    },\n    \n    // 边框色\n    border: {\n      primary: 'rgba(255, 255, 255, 0.1)',\n      secondary: 'rgba(255, 255, 255, 0.05)',\n      tertiary: 'rgba(255, 255, 255, 0.02)',\n      active: '#ff6b35',\n      focus: '#ff8555',\n      error: '#f44336'\n    },\n    \n    // 游戏相关色彩\n    game: {\n      installed: '#4caf50',\n      available: '#2196f3',\n      updating: '#ff9800',\n      error: '#f44336'\n    }\n  },\n  \n  // 字体系统\n  fonts: {\n    primary: \"'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif\",\n    mono: \"'Consolas', 'Monaco', 'Courier New', monospace\"\n  },\n  \n  // 字体大小\n  fontSizes: {\n    xs: '12px',\n    sm: '14px',\n    md: '16px',\n    lg: '18px',\n    xl: '20px',\n    '2xl': '24px',\n    '3xl': '30px',\n    '4xl': '36px'\n  },\n  \n  // 字重\n  fontWeights: {\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    bold: 700\n  },\n  \n  // 间距系统\n  spacing: {\n    xs: '4px',\n    sm: '8px',\n    md: '16px',\n    lg: '24px',\n    xl: '32px',\n    '2xl': '48px',\n    '3xl': '64px'\n  },\n  \n  // 圆角\n  borderRadius: {\n    sm: '4px',\n    md: '8px',\n    lg: '12px',\n    xl: '16px',\n    full: '50%'\n  },\n  \n  // 阴影\n  shadows: {\n    sm: '0 2px 4px rgba(0, 0, 0, 0.1)',\n    md: '0 4px 8px rgba(0, 0, 0, 0.2)',\n    lg: '0 8px 16px rgba(0, 0, 0, 0.3)',\n    xl: '0 16px 32px rgba(0, 0, 0, 0.4)',\n    glow: '0 0 20px rgba(255, 107, 53, 0.3)',\n    glowHover: '0 0 30px rgba(255, 107, 53, 0.4)',\n    card: '0 4px 12px rgba(0, 0, 0, 0.15)',\n    cardHover: '0 8px 24px rgba(0, 0, 0, 0.25)'\n  },\n  \n  // 过渡动画\n  transitions: {\n    fast: '0.15s ease',\n    normal: '0.3s ease',\n    slow: '0.5s ease'\n  },\n  \n  // 层级\n  zIndex: {\n    dropdown: 1000,\n    sticky: 1020,\n    fixed: 1030,\n    modal: 1040,\n    popover: 1050,\n    tooltip: 1060\n  },\n  \n  // 断点\n  breakpoints: {\n    sm: '576px',\n    md: '768px',\n    lg: '992px',\n    xl: '1200px',\n    '2xl': '1400px'\n  },\n  \n  // 组件特定样式\n  components: {\n    button: {\n      height: {\n        sm: '32px',\n        md: '40px',\n        lg: '48px'\n      },\n      padding: {\n        sm: '0 12px',\n        md: '0 16px',\n        lg: '0 24px'\n      }\n    },\n    \n    input: {\n      height: {\n        sm: '32px',\n        md: '40px',\n        lg: '48px'\n      }\n    },\n    \n    sidebar: {\n      width: '240px',\n      collapsedWidth: '60px'\n    },\n    \n    titleBar: {\n      height: '32px'\n    }\n  }\n};\n\nexport default theme;"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACZ;EACAC,MAAM,EAAE;IACN;IACAC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,SAAS;IACxBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,SAAS;IAEtB;IACAC,SAAS,EAAE,SAAS;IACpBC,cAAc,EAAE,SAAS;IACzBC,eAAe,EAAE,SAAS;IAE1B;IACAC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,SAAS;IAEvB;IACAC,UAAU,EAAE;MACVX,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE,SAAS;MACpBO,QAAQ,EAAE,SAAS;MACnBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE,uBAAuB;MAC7BC,SAAS,EAAE,uBAAuB;MAClCC,OAAO,EAAE,oBAAoB;MAC7BC,KAAK,EAAE;IACT,CAAC;IAED;IACAC,IAAI,EAAE;MACJlB,OAAO,EAAE,SAAS;MAClBK,SAAS,EAAE,SAAS;MACpBO,QAAQ,EAAE,SAAS;MACnBO,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE;IACX,CAAC;IAED;IACAC,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IAED;IACAC,MAAM,EAAE;MACN1B,OAAO,EAAE,0BAA0B;MACnCK,SAAS,EAAE,2BAA2B;MACtCO,QAAQ,EAAE,2BAA2B;MACrCe,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE,SAAS;MAChBJ,KAAK,EAAE;IACT,CAAC;IAED;IACAK,IAAI,EAAE;MACJC,SAAS,EAAE,SAAS;MACpBC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,SAAS;MACnBR,KAAK,EAAE;IACT;EACF,CAAC;EAED;EACAS,KAAK,EAAE;IACLjC,OAAO,EAAE,oEAAoE;IAC7EkC,IAAI,EAAE;EACR,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACV,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,KAAK,EAAE;EACT,CAAC;EAED;EACAC,WAAW,EAAE;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE;EACR,CAAC;EAED;EACAC,OAAO,EAAE;IACPX,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACV,KAAK,EAAE,MAAM;IACb,KAAK,EAAE;EACT,CAAC;EAED;EACAQ,YAAY,EAAE;IACZX,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVS,IAAI,EAAE;EACR,CAAC;EAED;EACAC,OAAO,EAAE;IACPb,EAAE,EAAE,8BAA8B;IAClCC,EAAE,EAAE,8BAA8B;IAClCC,EAAE,EAAE,+BAA+B;IACnCC,EAAE,EAAE,gCAAgC;IACpCW,IAAI,EAAE,kCAAkC;IACxCC,SAAS,EAAE,kCAAkC;IAC7CtC,IAAI,EAAE,gCAAgC;IACtCC,SAAS,EAAE;EACb,CAAC;EAED;EACAsC,WAAW,EAAE;IACXC,IAAI,EAAE,YAAY;IAClBX,MAAM,EAAE,WAAW;IACnBY,IAAI,EAAE;EACR,CAAC;EAED;EACAC,MAAM,EAAE;IACNC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,IAAI;IACXC,OAAO,EAAE,IAAI;IACbC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,WAAW,EAAE;IACX1B,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZ,KAAK,EAAE;EACT,CAAC;EAED;EACAwB,UAAU,EAAE;IACVC,MAAM,EAAE;MACNC,MAAM,EAAE;QACN7B,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN,CAAC;MACD4B,OAAO,EAAE;QACP9B,EAAE,EAAE,QAAQ;QACZC,EAAE,EAAE,QAAQ;QACZC,EAAE,EAAE;MACN;IACF,CAAC;IAED6B,KAAK,EAAE;MACLF,MAAM,EAAE;QACN7B,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN;IACF,CAAC;IAED8B,OAAO,EAAE;MACPC,KAAK,EAAE,OAAO;MACdC,cAAc,EAAE;IAClB,CAAC;IAEDC,QAAQ,EAAE;MACRN,MAAM,EAAE;IACV;EACF;AACF,CAAC;AAED,eAAepE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}