{"ast": null, "code": "import { cssVariableRegex } from '../../../render/dom/utils/is-css-variable.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { colorRegex, floatRegex, isString, sanitize } from '../utils.mjs';\nfunction test(v) {\n  var _a, _b;\n  return isNaN(v) && isString(v) && (((_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) + (((_b = v.match(colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) > 0;\n}\nconst cssVarTokeniser = {\n  regex: cssVariableRegex,\n  countKey: \"Vars\",\n  token: \"${v}\",\n  parse: noop\n};\nconst colorTokeniser = {\n  regex: colorRegex,\n  countKey: \"Colors\",\n  token: \"${c}\",\n  parse: color.parse\n};\nconst numberTokeniser = {\n  regex: floatRegex,\n  countKey: \"Numbers\",\n  token: \"${n}\",\n  parse: number.parse\n};\nfunction tokenise(info, {\n  regex,\n  countKey,\n  token,\n  parse\n}) {\n  const matches = info.tokenised.match(regex);\n  if (!matches) return;\n  info[\"num\" + countKey] = matches.length;\n  info.tokenised = info.tokenised.replace(regex, token);\n  info.values.push(...matches.map(parse));\n}\nfunction analyseComplexValue(value) {\n  const originalValue = value.toString();\n  const info = {\n    value: originalValue,\n    tokenised: originalValue,\n    values: [],\n    numVars: 0,\n    numColors: 0,\n    numNumbers: 0\n  };\n  if (info.value.includes(\"var(--\")) tokenise(info, cssVarTokeniser);\n  tokenise(info, colorTokeniser);\n  tokenise(info, numberTokeniser);\n  return info;\n}\nfunction parseComplexValue(v) {\n  return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n  const {\n    values,\n    numColors,\n    numVars,\n    tokenised\n  } = analyseComplexValue(source);\n  const numValues = values.length;\n  return v => {\n    let output = tokenised;\n    for (let i = 0; i < numValues; i++) {\n      if (i < numVars) {\n        output = output.replace(cssVarTokeniser.token, v[i]);\n      } else if (i < numVars + numColors) {\n        output = output.replace(colorTokeniser.token, color.transform(v[i]));\n      } else {\n        output = output.replace(numberTokeniser.token, sanitize(v[i]));\n      }\n    }\n    return output;\n  };\n}\nconst convertNumbersToZero = v => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n  const parsed = parseComplexValue(v);\n  const transformer = createTransformer(v);\n  return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n  test,\n  parse: parseComplexValue,\n  createTransformer,\n  getAnimatableNone\n};\nexport { analyseComplexValue, complex };", "map": {"version": 3, "names": ["cssVariableRegex", "noop", "color", "number", "colorRegex", "floatRegex", "isString", "sanitize", "test", "v", "_a", "_b", "isNaN", "match", "length", "cssVarTokeniser", "regex", "<PERSON><PERSON><PERSON>", "token", "parse", "colorTokeniser", "numberToken<PERSON>", "tokenise", "info", "matches", "tokenised", "replace", "values", "push", "map", "analyseComplexValue", "value", "originalValue", "toString", "numVars", "numColors", "numNumbers", "includes", "parseComplexValue", "createTransformer", "source", "numValues", "output", "i", "transform", "convertNumbersToZero", "getAnimatableNone", "parsed", "transformer", "complex"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/value/types/complex/index.mjs"], "sourcesContent": ["import { cssVariableRegex } from '../../../render/dom/utils/is-css-variable.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { color } from '../color/index.mjs';\nimport { number } from '../numbers/index.mjs';\nimport { colorRegex, floatRegex, isString, sanitize } from '../utils.mjs';\n\nfunction test(v) {\n    var _a, _b;\n    return (isNaN(v) &&\n        isString(v) &&\n        (((_a = v.match(floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) +\n            (((_b = v.match(colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) >\n            0);\n}\nconst cssVarTokeniser = {\n    regex: cssVariableRegex,\n    countKey: \"Vars\",\n    token: \"${v}\",\n    parse: noop,\n};\nconst colorTokeniser = {\n    regex: colorRegex,\n    countKey: \"Colors\",\n    token: \"${c}\",\n    parse: color.parse,\n};\nconst numberTokeniser = {\n    regex: floatRegex,\n    countKey: \"Numbers\",\n    token: \"${n}\",\n    parse: number.parse,\n};\nfunction tokenise(info, { regex, countKey, token, parse }) {\n    const matches = info.tokenised.match(regex);\n    if (!matches)\n        return;\n    info[\"num\" + countKey] = matches.length;\n    info.tokenised = info.tokenised.replace(regex, token);\n    info.values.push(...matches.map(parse));\n}\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const info = {\n        value: originalValue,\n        tokenised: originalValue,\n        values: [],\n        numVars: 0,\n        numColors: 0,\n        numNumbers: 0,\n    };\n    if (info.value.includes(\"var(--\"))\n        tokenise(info, cssVarTokeniser);\n    tokenise(info, colorTokeniser);\n    tokenise(info, numberTokeniser);\n    return info;\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { values, numColors, numVars, tokenised } = analyseComplexValue(source);\n    const numValues = values.length;\n    return (v) => {\n        let output = tokenised;\n        for (let i = 0; i < numValues; i++) {\n            if (i < numVars) {\n                output = output.replace(cssVarTokeniser.token, v[i]);\n            }\n            else if (i < numVars + numColors) {\n                output = output.replace(colorTokeniser.token, color.transform(v[i]));\n            }\n            else {\n                output = output.replace(numberTokeniser.token, sanitize(v[i]));\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\nexport { analyseComplexValue, complex };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,IAAI,QAAQ,yBAAyB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAEzE,SAASC,IAAIA,CAACC,CAAC,EAAE;EACb,IAAIC,EAAE,EAAEC,EAAE;EACV,OAAQC,KAAK,CAACH,CAAC,CAAC,IACZH,QAAQ,CAACG,CAAC,CAAC,IACX,CAAC,CAAC,CAACC,EAAE,GAAGD,CAAC,CAACI,KAAK,CAACR,UAAU,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,MAAM,KAAK,CAAC,KAC5E,CAAC,CAACH,EAAE,GAAGF,CAAC,CAACI,KAAK,CAACT,UAAU,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM,KAAK,CAAC,CAAC,GAClF,CAAC;AACb;AACA,MAAMC,eAAe,GAAG;EACpBC,KAAK,EAAEhB,gBAAgB;EACvBiB,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAElB;AACX,CAAC;AACD,MAAMmB,cAAc,GAAG;EACnBJ,KAAK,EAAEZ,UAAU;EACjBa,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAEjB,KAAK,CAACiB;AACjB,CAAC;AACD,MAAME,eAAe,GAAG;EACpBL,KAAK,EAAEX,UAAU;EACjBY,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,MAAM;EACbC,KAAK,EAAEhB,MAAM,CAACgB;AAClB,CAAC;AACD,SAASG,QAAQA,CAACC,IAAI,EAAE;EAAEP,KAAK;EAAEC,QAAQ;EAAEC,KAAK;EAAEC;AAAM,CAAC,EAAE;EACvD,MAAMK,OAAO,GAAGD,IAAI,CAACE,SAAS,CAACZ,KAAK,CAACG,KAAK,CAAC;EAC3C,IAAI,CAACQ,OAAO,EACR;EACJD,IAAI,CAAC,KAAK,GAAGN,QAAQ,CAAC,GAAGO,OAAO,CAACV,MAAM;EACvCS,IAAI,CAACE,SAAS,GAAGF,IAAI,CAACE,SAAS,CAACC,OAAO,CAACV,KAAK,EAAEE,KAAK,CAAC;EACrDK,IAAI,CAACI,MAAM,CAACC,IAAI,CAAC,GAAGJ,OAAO,CAACK,GAAG,CAACV,KAAK,CAAC,CAAC;AAC3C;AACA,SAASW,mBAAmBA,CAACC,KAAK,EAAE;EAChC,MAAMC,aAAa,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EACtC,MAAMV,IAAI,GAAG;IACTQ,KAAK,EAAEC,aAAa;IACpBP,SAAS,EAAEO,aAAa;IACxBL,MAAM,EAAE,EAAE;IACVO,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EAChB,CAAC;EACD,IAAIb,IAAI,CAACQ,KAAK,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAC7Bf,QAAQ,CAACC,IAAI,EAAER,eAAe,CAAC;EACnCO,QAAQ,CAACC,IAAI,EAAEH,cAAc,CAAC;EAC9BE,QAAQ,CAACC,IAAI,EAAEF,eAAe,CAAC;EAC/B,OAAOE,IAAI;AACf;AACA,SAASe,iBAAiBA,CAAC7B,CAAC,EAAE;EAC1B,OAAOqB,mBAAmB,CAACrB,CAAC,CAAC,CAACkB,MAAM;AACxC;AACA,SAASY,iBAAiBA,CAACC,MAAM,EAAE;EAC/B,MAAM;IAAEb,MAAM;IAAEQ,SAAS;IAAED,OAAO;IAAET;EAAU,CAAC,GAAGK,mBAAmB,CAACU,MAAM,CAAC;EAC7E,MAAMC,SAAS,GAAGd,MAAM,CAACb,MAAM;EAC/B,OAAQL,CAAC,IAAK;IACV,IAAIiC,MAAM,GAAGjB,SAAS;IACtB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;MAChC,IAAIA,CAAC,GAAGT,OAAO,EAAE;QACbQ,MAAM,GAAGA,MAAM,CAAChB,OAAO,CAACX,eAAe,CAACG,KAAK,EAAET,CAAC,CAACkC,CAAC,CAAC,CAAC;MACxD,CAAC,MACI,IAAIA,CAAC,GAAGT,OAAO,GAAGC,SAAS,EAAE;QAC9BO,MAAM,GAAGA,MAAM,CAAChB,OAAO,CAACN,cAAc,CAACF,KAAK,EAAEhB,KAAK,CAAC0C,SAAS,CAACnC,CAAC,CAACkC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,MACI;QACDD,MAAM,GAAGA,MAAM,CAAChB,OAAO,CAACL,eAAe,CAACH,KAAK,EAAEX,QAAQ,CAACE,CAAC,CAACkC,CAAC,CAAC,CAAC,CAAC;MAClE;IACJ;IACA,OAAOD,MAAM;EACjB,CAAC;AACL;AACA,MAAMG,oBAAoB,GAAIpC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAGA,CAAC;AACjE,SAASqC,iBAAiBA,CAACrC,CAAC,EAAE;EAC1B,MAAMsC,MAAM,GAAGT,iBAAiB,CAAC7B,CAAC,CAAC;EACnC,MAAMuC,WAAW,GAAGT,iBAAiB,CAAC9B,CAAC,CAAC;EACxC,OAAOuC,WAAW,CAACD,MAAM,CAAClB,GAAG,CAACgB,oBAAoB,CAAC,CAAC;AACxD;AACA,MAAMI,OAAO,GAAG;EACZzC,IAAI;EACJW,KAAK,EAAEmB,iBAAiB;EACxBC,iBAAiB;EACjBO;AACJ,CAAC;AAED,SAAShB,mBAAmB,EAAEmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}