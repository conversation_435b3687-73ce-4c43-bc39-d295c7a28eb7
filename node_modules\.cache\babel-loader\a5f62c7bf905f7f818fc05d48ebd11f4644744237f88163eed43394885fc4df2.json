{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nfunction calcLength(axis) {\n  return axis.max - axis.min;\n}\nfunction isNear(value, target = 0, maxDistance = 0.01) {\n  return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n  delta.origin = origin;\n  delta.originPoint = mix(source.min, source.max, delta.origin);\n  delta.scale = calcLength(target) / calcLength(source);\n  if (isNear(delta.scale, 1, 0.0001) || isNaN(delta.scale)) delta.scale = 1;\n  delta.translate = mix(target.min, target.max, delta.origin) - delta.originPoint;\n  if (isNear(delta.translate) || isNaN(delta.translate)) delta.translate = 0;\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n  calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n  calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n  target.min = parent.min + relative.min;\n  target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n  calcRelativeAxis(target.x, relative.x, parent.x);\n  calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n  target.min = layout.min - parent.min;\n  target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n  calcRelativeAxisPosition(target.x, layout.x, parent.x);\n  calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };", "map": {"version": 3, "names": ["mix", "calcLength", "axis", "max", "min", "isNear", "value", "target", "maxDistance", "Math", "abs", "calcAxisDelta", "delta", "source", "origin", "originPoint", "scale", "isNaN", "translate", "calcBoxDelta", "x", "originX", "undefined", "y", "originY", "calcRelativeAxis", "relative", "parent", "calcRelativeBox", "calcRelativeAxisPosition", "layout", "calcRelativePosition"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\n\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target = 0, maxDistance = 0.01) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = mix(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    if (isNear(delta.scale, 1, 0.0001) || isNaN(delta.scale))\n        delta.scale = 1;\n    delta.translate =\n        mix(target.min, target.max, delta.origin) - delta.originPoint;\n    if (isNear(delta.translate) || isNaN(delta.translate))\n        delta.translate = 0;\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AAEzC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,GAAG;AAC9B;AACA,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,GAAG,CAAC,EAAEC,WAAW,GAAG,IAAI,EAAE;EACnD,OAAOC,IAAI,CAACC,GAAG,CAACJ,KAAK,GAAGC,MAAM,CAAC,IAAIC,WAAW;AAClD;AACA,SAASG,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAEN,MAAM,EAAEO,MAAM,GAAG,GAAG,EAAE;EACxDF,KAAK,CAACE,MAAM,GAAGA,MAAM;EACrBF,KAAK,CAACG,WAAW,GAAGf,GAAG,CAACa,MAAM,CAACT,GAAG,EAAES,MAAM,CAACV,GAAG,EAAES,KAAK,CAACE,MAAM,CAAC;EAC7DF,KAAK,CAACI,KAAK,GAAGf,UAAU,CAACM,MAAM,CAAC,GAAGN,UAAU,CAACY,MAAM,CAAC;EACrD,IAAIR,MAAM,CAACO,KAAK,CAACI,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,IAAIC,KAAK,CAACL,KAAK,CAACI,KAAK,CAAC,EACpDJ,KAAK,CAACI,KAAK,GAAG,CAAC;EACnBJ,KAAK,CAACM,SAAS,GACXlB,GAAG,CAACO,MAAM,CAACH,GAAG,EAAEG,MAAM,CAACJ,GAAG,EAAES,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACG,WAAW;EACjE,IAAIV,MAAM,CAACO,KAAK,CAACM,SAAS,CAAC,IAAID,KAAK,CAACL,KAAK,CAACM,SAAS,CAAC,EACjDN,KAAK,CAACM,SAAS,GAAG,CAAC;AAC3B;AACA,SAASC,YAAYA,CAACP,KAAK,EAAEC,MAAM,EAAEN,MAAM,EAAEO,MAAM,EAAE;EACjDH,aAAa,CAACC,KAAK,CAACQ,CAAC,EAAEP,MAAM,CAACO,CAAC,EAAEb,MAAM,CAACa,CAAC,EAAEN,MAAM,GAAGA,MAAM,CAACO,OAAO,GAAGC,SAAS,CAAC;EAC/EX,aAAa,CAACC,KAAK,CAACW,CAAC,EAAEV,MAAM,CAACU,CAAC,EAAEhB,MAAM,CAACgB,CAAC,EAAET,MAAM,GAAGA,MAAM,CAACU,OAAO,GAAGF,SAAS,CAAC;AACnF;AACA,SAASG,gBAAgBA,CAAClB,MAAM,EAAEmB,QAAQ,EAAEC,MAAM,EAAE;EAChDpB,MAAM,CAACH,GAAG,GAAGuB,MAAM,CAACvB,GAAG,GAAGsB,QAAQ,CAACtB,GAAG;EACtCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAACyB,QAAQ,CAAC;AAClD;AACA,SAASE,eAAeA,CAACrB,MAAM,EAAEmB,QAAQ,EAAEC,MAAM,EAAE;EAC/CF,gBAAgB,CAAClB,MAAM,CAACa,CAAC,EAAEM,QAAQ,CAACN,CAAC,EAAEO,MAAM,CAACP,CAAC,CAAC;EAChDK,gBAAgB,CAAClB,MAAM,CAACgB,CAAC,EAAEG,QAAQ,CAACH,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AACpD;AACA,SAASM,wBAAwBA,CAACtB,MAAM,EAAEuB,MAAM,EAAEH,MAAM,EAAE;EACtDpB,MAAM,CAACH,GAAG,GAAG0B,MAAM,CAAC1B,GAAG,GAAGuB,MAAM,CAACvB,GAAG;EACpCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAAC6B,MAAM,CAAC;AAChD;AACA,SAASC,oBAAoBA,CAACxB,MAAM,EAAEuB,MAAM,EAAEH,MAAM,EAAE;EAClDE,wBAAwB,CAACtB,MAAM,CAACa,CAAC,EAAEU,MAAM,CAACV,CAAC,EAAEO,MAAM,CAACP,CAAC,CAAC;EACtDS,wBAAwB,CAACtB,MAAM,CAACgB,CAAC,EAAEO,MAAM,CAACP,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AAC1D;AAEA,SAASZ,aAAa,EAAEQ,YAAY,EAAElB,UAAU,EAAEwB,gBAAgB,EAAEI,wBAAwB,EAAED,eAAe,EAAEG,oBAAoB,EAAE1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}