{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\pages\\\\LoginPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  position: relative;\n  overflow: hidden;\n`;\n_c = LoginContainer;\nconst BackgroundImage = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1920 1080\"><defs><linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\"><stop offset=\"0%\" style=\"stop-color:%23ff6b35;stop-opacity:0.1\"/><stop offset=\"100%\" style=\"stop-color:%234a9eff;stop-opacity:0.1\"/></linearGradient></defs><rect width=\"100%\" height=\"100%\" fill=\"url(%23bg)\"/></svg>') center/cover;\n  opacity: 0.3;\n`;\n_c2 = BackgroundImage;\nconst LoginCard = styled(motion.div)`\n  background: rgba(45, 45, 45, 0.95);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 48px;\n  width: 100%;\n  max-width: 400px;\n  backdrop-filter: blur(20px);\n  box-shadow: ${props => props.theme.shadows.xl};\n  position: relative;\n  z-index: 1;\n`;\n_c3 = LoginCard;\nconst Logo = styled.div`\n  text-align: center;\n  margin-bottom: 32px;\n`;\n_c4 = Logo;\nconst LogoIcon = styled.div`\n  width: 64px;\n  height: 64px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  font-weight: bold;\n  color: white;\n  margin: 0 auto 16px;\n  box-shadow: ${props => props.theme.shadows.glow};\n`;\n_c5 = LogoIcon;\nconst LogoText = styled.h1`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n_c6 = LogoText;\nconst LogoSubtext = styled.p`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.tertiary};\n`;\n_c7 = LogoSubtext;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n_c8 = Form;\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n_c9 = FormGroup;\nconst Label = styled.label`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n_c0 = Label;\nconst Input = styled.input`\n  height: 48px;\n  padding: 0 16px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 8px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.md};\n  transition: all 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    background: rgba(255, 255, 255, 0.1);\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);\n  }\n  \n  &::placeholder {\n    color: ${props => props.theme.colors.text.tertiary};\n  }\n`;\n_c1 = Input;\nconst Button = styled(motion.button)`\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border: none;\n  border-radius: 8px;\n  color: white;\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: ${props => props.theme.shadows.glow};\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n`;\n_c10 = Button;\nconst ErrorMessage = styled(motion.div)`\n  background: rgba(244, 67, 54, 0.1);\n  border: 1px solid rgba(244, 67, 54, 0.3);\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: ${props => props.theme.colors.status.error};\n  font-size: ${props => props.theme.fontSizes.sm};\n  text-align: center;\n`;\n_c11 = ErrorMessage;\nconst ToggleMode = styled.div`\n  text-align: center;\n  margin-top: 24px;\n  \n  span {\n    color: ${props => props.theme.colors.text.tertiary};\n    font-size: ${props => props.theme.fontSizes.sm};\n  }\n  \n  button {\n    background: none;\n    border: none;\n    color: ${props => props.theme.colors.primary};\n    font-size: ${props => props.theme.fontSizes.sm};\n    cursor: pointer;\n    margin-left: 8px;\n    \n    &:hover {\n      text-decoration: underline;\n    }\n  }\n`;\n_c12 = ToggleMode;\nconst LoginPage = () => {\n  _s();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const {\n    login,\n    register,\n    loading,\n    error,\n    clearError\n  } = useAuth();\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // 清除错误信息\n    if (error) {\n      clearError();\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (isLogin) {\n      await login({\n        username: formData.username,\n        password: formData.password\n      });\n    } else {\n      if (formData.password !== formData.confirmPassword) {\n        return;\n      }\n      await register({\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n    clearError();\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: [/*#__PURE__*/_jsxDEV(BackgroundImage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(LoginCard, {\n      initial: {\n        opacity: 0,\n        y: 50\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      transition: {\n        duration: 0.6\n      },\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n          children: \"D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n          children: \"DCS World\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LogoSubtext, {\n          children: \"\\u6570\\u5B57\\u6218\\u6597\\u6A21\\u62DF\\u5668\\u542F\\u52A8\\u5668\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            name: \"username\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n            value: formData.username,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"\\u90AE\\u7BB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n            value: formData.email,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"password\",\n            name: \"password\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n            value: formData.password,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), !isLogin && /*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(Label, {\n            children: \"\\u786E\\u8BA4\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"password\",\n            name: \"confirmPassword\",\n            placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u5BC6\\u7801\",\n            value: formData.confirmPassword,\n            onChange: handleInputChange,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          initial: {\n            opacity: 0,\n            y: -10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          disabled: loading,\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: loading ? '处理中...' : isLogin ? '登录' : '注册'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ToggleMode, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: isLogin ? '还没有账号？' : '已有账号？'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: toggleMode,\n          children: isLogin ? '立即注册' : '立即登录'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"/dgnmGCRg4aAcc1kvHkIGUTyqjM=\", false, function () {\n  return [useAuth];\n});\n_c13 = LoginPage;\nexport default LoginPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"BackgroundImage\");\n$RefreshReg$(_c3, \"LoginCard\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"LogoIcon\");\n$RefreshReg$(_c6, \"LogoText\");\n$RefreshReg$(_c7, \"LogoSubtext\");\n$RefreshReg$(_c8, \"Form\");\n$RefreshReg$(_c9, \"FormGroup\");\n$RefreshReg$(_c0, \"Label\");\n$RefreshReg$(_c1, \"Input\");\n$RefreshReg$(_c10, \"Button\");\n$RefreshReg$(_c11, \"ErrorMessage\");\n$RefreshReg$(_c12, \"ToggleMode\");\n$RefreshReg$(_c13, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "motion", "useAuth", "jsxDEV", "_jsxDEV", "LoginContainer", "div", "_c", "BackgroundImage", "_c2", "LoginCard", "props", "theme", "colors", "border", "primary", "shadows", "xl", "_c3", "Logo", "_c4", "LogoIcon", "glow", "_c5", "LogoText", "h1", "fontSizes", "fontWeights", "bold", "text", "_c6", "LogoSubtext", "p", "sm", "tertiary", "_c7", "Form", "form", "_c8", "FormGroup", "_c9", "Label", "label", "medium", "secondary", "_c0", "Input", "input", "md", "_c1", "<PERSON><PERSON>", "button", "_c10", "ErrorMessage", "status", "error", "_c11", "ToggleMode", "_c12", "LoginPage", "_s", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "username", "email", "password", "confirmPassword", "login", "register", "loading", "clearError", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "toggleMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "y", "animate", "transition", "duration", "onSubmit", "type", "placeholder", "onChange", "required", "disabled", "whileHover", "scale", "whileTap", "onClick", "_c13", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../context/AuthContext';\n\nconst LoginContainer = styled.div`\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  position: relative;\n  overflow: hidden;\n`;\n\nconst BackgroundImage = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1920 1080\"><defs><linearGradient id=\"bg\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\"><stop offset=\"0%\" style=\"stop-color:%23ff6b35;stop-opacity:0.1\"/><stop offset=\"100%\" style=\"stop-color:%234a9eff;stop-opacity:0.1\"/></linearGradient></defs><rect width=\"100%\" height=\"100%\" fill=\"url(%23bg)\"/></svg>') center/cover;\n  opacity: 0.3;\n`;\n\nconst LoginCard = styled(motion.div)`\n  background: rgba(45, 45, 45, 0.95);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 16px;\n  padding: 48px;\n  width: 100%;\n  max-width: 400px;\n  backdrop-filter: blur(20px);\n  box-shadow: ${props => props.theme.shadows.xl};\n  position: relative;\n  z-index: 1;\n`;\n\nconst Logo = styled.div`\n  text-align: center;\n  margin-bottom: 32px;\n`;\n\nconst LogoIcon = styled.div`\n  width: 64px;\n  height: 64px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 32px;\n  font-weight: bold;\n  color: white;\n  margin: 0 auto 16px;\n  box-shadow: ${props => props.theme.shadows.glow};\n`;\n\nconst LogoText = styled.h1`\n  font-size: ${props => props.theme.fontSizes['2xl']};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  margin-bottom: 8px;\n`;\n\nconst LogoSubtext = styled.p`\n  font-size: ${props => props.theme.fontSizes.sm};\n  color: ${props => props.theme.colors.text.tertiary};\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst Label = styled.label`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.secondary};\n`;\n\nconst Input = styled.input`\n  height: 48px;\n  padding: 0 16px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 8px;\n  color: ${props => props.theme.colors.text.primary};\n  font-size: ${props => props.theme.fontSizes.md};\n  transition: all 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${props => props.theme.colors.primary};\n    background: rgba(255, 255, 255, 0.1);\n    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);\n  }\n  \n  &::placeholder {\n    color: ${props => props.theme.colors.text.tertiary};\n  }\n`;\n\nconst Button = styled(motion.button)`\n  height: 48px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border: none;\n  border-radius: 8px;\n  color: white;\n  font-size: ${props => props.theme.fontSizes.md};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: ${props => props.theme.shadows.glow};\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n`;\n\nconst ErrorMessage = styled(motion.div)`\n  background: rgba(244, 67, 54, 0.1);\n  border: 1px solid rgba(244, 67, 54, 0.3);\n  border-radius: 8px;\n  padding: 12px 16px;\n  color: ${props => props.theme.colors.status.error};\n  font-size: ${props => props.theme.fontSizes.sm};\n  text-align: center;\n`;\n\nconst ToggleMode = styled.div`\n  text-align: center;\n  margin-top: 24px;\n  \n  span {\n    color: ${props => props.theme.colors.text.tertiary};\n    font-size: ${props => props.theme.fontSizes.sm};\n  }\n  \n  button {\n    background: none;\n    border: none;\n    color: ${props => props.theme.colors.primary};\n    font-size: ${props => props.theme.fontSizes.sm};\n    cursor: pointer;\n    margin-left: 8px;\n    \n    &:hover {\n      text-decoration: underline;\n    }\n  }\n`;\n\nconst LoginPage = () => {\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  \n  const { login, register, loading, error, clearError } = useAuth();\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // 清除错误信息\n    if (error) {\n      clearError();\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (isLogin) {\n      await login({\n        username: formData.username,\n        password: formData.password\n      });\n    } else {\n      if (formData.password !== formData.confirmPassword) {\n        return;\n      }\n      \n      await register({\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      });\n    }\n  };\n\n  const toggleMode = () => {\n    setIsLogin(!isLogin);\n    setFormData({\n      username: '',\n      email: '',\n      password: '',\n      confirmPassword: ''\n    });\n    clearError();\n  };\n\n  return (\n    <LoginContainer>\n      <BackgroundImage />\n      \n      <LoginCard\n        initial={{ opacity: 0, y: 50 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.6 }}\n      >\n        <Logo>\n          <LogoIcon>D</LogoIcon>\n          <LogoText>DCS World</LogoText>\n          <LogoSubtext>数字战斗模拟器启动器</LogoSubtext>\n        </Logo>\n\n        <Form onSubmit={handleSubmit}>\n          <FormGroup>\n            <Label>用户名</Label>\n            <Input\n              type=\"text\"\n              name=\"username\"\n              placeholder=\"请输入用户名\"\n              value={formData.username}\n              onChange={handleInputChange}\n              required\n            />\n          </FormGroup>\n\n          {!isLogin && (\n            <FormGroup>\n              <Label>邮箱</Label>\n              <Input\n                type=\"email\"\n                name=\"email\"\n                placeholder=\"请输入邮箱地址\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n          )}\n\n          <FormGroup>\n            <Label>密码</Label>\n            <Input\n              type=\"password\"\n              name=\"password\"\n              placeholder=\"请输入密码\"\n              value={formData.password}\n              onChange={handleInputChange}\n              required\n            />\n          </FormGroup>\n\n          {!isLogin && (\n            <FormGroup>\n              <Label>确认密码</Label>\n              <Input\n                type=\"password\"\n                name=\"confirmPassword\"\n                placeholder=\"请再次输入密码\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required\n              />\n            </FormGroup>\n          )}\n\n          {error && (\n            <ErrorMessage\n              initial={{ opacity: 0, y: -10 }}\n              animate={{ opacity: 1, y: 0 }}\n            >\n              {error}\n            </ErrorMessage>\n          )}\n\n          <Button\n            type=\"submit\"\n            disabled={loading}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            {loading ? '处理中...' : (isLogin ? '登录' : '注册')}\n          </Button>\n        </Form>\n\n        <ToggleMode>\n          <span>\n            {isLogin ? '还没有账号？' : '已有账号？'}\n          </span>\n          <button type=\"button\" onClick={toggleMode}>\n            {isLogin ? '立即注册' : '立即登录'}\n          </button>\n        </ToggleMode>\n      </LoginCard>\n    </LoginContainer>\n  );\n};\n\nexport default LoginPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIF,cAAc;AAWpB,MAAMG,eAAe,GAAGR,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,eAAe;AAUrB,MAAME,SAAS,GAAGV,MAAM,CAACC,MAAM,CAACK,GAAG,CAAC;AACpC;AACA,sBAAsBK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA;AACA;AACA;AACA;AACA,gBAAgBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACC,EAAE;AAC/C;AACA;AACA,CAAC;AAACC,GAAA,GAXIR,SAAS;AAaf,MAAMS,IAAI,GAAGnB,MAAM,CAACM,GAAG;AACvB;AACA;AACA,CAAC;AAACc,GAAA,GAHID,IAAI;AAKV,MAAME,QAAQ,GAAGrB,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACM,IAAI;AACjD,CAAC;AAACC,GAAA,GAbIF,QAAQ;AAed,MAAMG,QAAQ,GAAGxB,MAAM,CAACyB,EAAE;AAC1B,eAAed,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiBf,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACC,IAAI;AACtD,WAAWjB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACd,OAAO;AACnD;AACA,CAAC;AAACe,GAAA,GALIN,QAAQ;AAOd,MAAMO,WAAW,GAAG/B,MAAM,CAACgC,CAAC;AAC5B,eAAerB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACO,EAAE;AAChD,WAAWtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACK,QAAQ;AACpD,CAAC;AAACC,GAAA,GAHIJ,WAAW;AAKjB,MAAMK,IAAI,GAAGpC,MAAM,CAACqC,IAAI;AACxB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,SAAS,GAAGvC,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACkC,GAAA,GAJID,SAAS;AAMf,MAAME,KAAK,GAAGzC,MAAM,CAAC0C,KAAK;AAC1B,eAAe/B,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACO,EAAE;AAChD,iBAAiBtB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACgB,MAAM;AACxD,WAAWhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACe,SAAS;AACrD,CAAC;AAACC,GAAA,GAJIJ,KAAK;AAMX,MAAMK,KAAK,GAAG9C,MAAM,CAAC+C,KAAK;AAC1B;AACA;AACA;AACA,sBAAsBpC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA,WAAWJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACd,OAAO;AACnD,eAAeJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACsB,EAAE;AAChD;AACA;AACA;AACA;AACA,oBAAoBrC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,OAAO;AACvD;AACA;AACA;AACA;AACA;AACA,aAAaJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACK,QAAQ;AACtD;AACA,CAAC;AAACe,GAAA,GApBIH,KAAK;AAsBX,MAAMI,MAAM,GAAGlD,MAAM,CAACC,MAAM,CAACkD,MAAM,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA,eAAexC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACsB,EAAE;AAChD,iBAAiBrC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACe,WAAW,CAACgB,MAAM;AACxD;AACA;AACA;AACA;AACA,kBAAkBhC,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACI,OAAO,CAACM,IAAI;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GAnBIF,MAAM;AAqBZ,MAAMG,YAAY,GAAGrD,MAAM,CAACC,MAAM,CAACK,GAAG,CAAC;AACvC;AACA;AACA;AACA;AACA,WAAWK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACyC,MAAM,CAACC,KAAK;AACnD,eAAe5C,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACO,EAAE;AAChD;AACA,CAAC;AAACuB,IAAA,GARIH,YAAY;AAUlB,MAAMI,UAAU,GAAGzD,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACgB,IAAI,CAACK,QAAQ;AACtD,iBAAiBvB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACO,EAAE;AAClD;AACA;AACA;AACA;AACA;AACA,aAAatB,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACC,MAAM,CAACE,OAAO;AAChD,iBAAiBJ,KAAK,IAAIA,KAAK,CAACC,KAAK,CAACc,SAAS,CAACO,EAAE;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,IAAA,GArBID,UAAU;AAuBhB,MAAME,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC;IACvCkE,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,OAAO;IAAEhB,KAAK;IAAEiB;EAAW,CAAC,GAAGtE,OAAO,CAAC,CAAC;EAEjE,MAAMuE,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCb,WAAW,CAACc,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrB,KAAK,EAAE;MACTiB,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAInB,OAAO,EAAE;MACX,MAAMQ,KAAK,CAAC;QACVJ,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BE,QAAQ,EAAEJ,QAAQ,CAACI;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIJ,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;QAClD;MACF;MAEA,MAAME,QAAQ,CAAC;QACbL,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMc,UAAU,GAAGA,CAAA,KAAM;IACvBnB,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBG,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE;IACnB,CAAC,CAAC;IACFI,UAAU,CAAC,CAAC;EACd,CAAC;EAED,oBACEpE,OAAA,CAACC,cAAc;IAAA6E,QAAA,gBACb9E,OAAA,CAACI,eAAe;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnBlF,OAAA,CAACM,SAAS;MACR6E,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAV,QAAA,gBAE9B9E,OAAA,CAACe,IAAI;QAAA+D,QAAA,gBACH9E,OAAA,CAACiB,QAAQ;UAAA6D,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eACtBlF,OAAA,CAACoB,QAAQ;UAAA0D,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAC9BlF,OAAA,CAAC2B,WAAW;UAAAmD,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAEPlF,OAAA,CAACgC,IAAI;QAACyD,QAAQ,EAAEd,YAAa;QAAAG,QAAA,gBAC3B9E,OAAA,CAACmC,SAAS;UAAA2C,QAAA,gBACR9E,OAAA,CAACqC,KAAK;YAAAyC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClBlF,OAAA,CAAC0C,KAAK;YACJgD,IAAI,EAAC,MAAM;YACXnB,IAAI,EAAC,UAAU;YACfoB,WAAW,EAAC,sCAAQ;YACpBnB,KAAK,EAAEb,QAAQ,CAACE,QAAS;YACzB+B,QAAQ,EAAEvB,iBAAkB;YAC5BwB,QAAQ;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEX,CAACzB,OAAO,iBACPzD,OAAA,CAACmC,SAAS;UAAA2C,QAAA,gBACR9E,OAAA,CAACqC,KAAK;YAAAyC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjBlF,OAAA,CAAC0C,KAAK;YACJgD,IAAI,EAAC,OAAO;YACZnB,IAAI,EAAC,OAAO;YACZoB,WAAW,EAAC,4CAAS;YACrBnB,KAAK,EAAEb,QAAQ,CAACG,KAAM;YACtB8B,QAAQ,EAAEvB,iBAAkB;YAC5BwB,QAAQ;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ,eAEDlF,OAAA,CAACmC,SAAS;UAAA2C,QAAA,gBACR9E,OAAA,CAACqC,KAAK;YAAAyC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjBlF,OAAA,CAAC0C,KAAK;YACJgD,IAAI,EAAC,UAAU;YACfnB,IAAI,EAAC,UAAU;YACfoB,WAAW,EAAC,gCAAO;YACnBnB,KAAK,EAAEb,QAAQ,CAACI,QAAS;YACzB6B,QAAQ,EAAEvB,iBAAkB;YAC5BwB,QAAQ;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEX,CAACzB,OAAO,iBACPzD,OAAA,CAACmC,SAAS;UAAA2C,QAAA,gBACR9E,OAAA,CAACqC,KAAK;YAAAyC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnBlF,OAAA,CAAC0C,KAAK;YACJgD,IAAI,EAAC,UAAU;YACfnB,IAAI,EAAC,iBAAiB;YACtBoB,WAAW,EAAC,4CAAS;YACrBnB,KAAK,EAAEb,QAAQ,CAACK,eAAgB;YAChC4B,QAAQ,EAAEvB,iBAAkB;YAC5BwB,QAAQ;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ,EAEA/B,KAAK,iBACJnD,OAAA,CAACiD,YAAY;UACXkC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAP,QAAA,EAE7B3B;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACf,eAEDlF,OAAA,CAAC8C,MAAM;UACL4C,IAAI,EAAC,QAAQ;UACbI,QAAQ,EAAE3B,OAAQ;UAClB4B,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAlB,QAAA,EAEzBX,OAAO,GAAG,QAAQ,GAAIV,OAAO,GAAG,IAAI,GAAG;QAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPlF,OAAA,CAACqD,UAAU;QAAAyB,QAAA,gBACT9E,OAAA;UAAA8E,QAAA,EACGrB,OAAO,GAAG,QAAQ,GAAG;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACPlF,OAAA;UAAQ0F,IAAI,EAAC,QAAQ;UAACQ,OAAO,EAAErB,UAAW;UAAAC,QAAA,EACvCrB,OAAO,GAAG,MAAM,GAAG;QAAM;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAC1B,EAAA,CA1JID,SAAS;EAAA,QAS2CzD,OAAO;AAAA;AAAAqG,IAAA,GAT3D5C,SAAS;AA4Jf,eAAeA,SAAS;AAAC,IAAApD,EAAA,EAAAE,GAAA,EAAAS,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAA6C,IAAA;AAAAC,YAAA,CAAAjG,EAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}