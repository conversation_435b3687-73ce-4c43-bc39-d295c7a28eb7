{"ast": null, "code": "import { millisecondsToSeconds } from '../../../utils/time-conversion.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n  return keys.some(key => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n  let springOptions = {\n    velocity: 0.0,\n    stiffness: 100,\n    damping: 10,\n    mass: 1.0,\n    isResolvedFromDuration: false,\n    ...options\n  };\n  // stiffness/damping/mass overrides duration/bounce\n  if (!isSpringType(options, physicsKeys) && isSpringType(options, durationKeys)) {\n    const derived = findSpring(options);\n    springOptions = {\n      ...springOptions,\n      ...derived,\n      mass: 1.0\n    };\n    springOptions.isResolvedFromDuration = true;\n  }\n  return springOptions;\n}\nfunction spring({\n  keyframes,\n  restDelta,\n  restSpeed,\n  ...options\n}) {\n  const origin = keyframes[0];\n  const target = keyframes[keyframes.length - 1];\n  /**\n   * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n   * to reduce GC during animation.\n   */\n  const state = {\n    done: false,\n    value: origin\n  };\n  const {\n    stiffness,\n    damping,\n    mass,\n    duration,\n    velocity,\n    isResolvedFromDuration\n  } = getSpringOptions({\n    ...options,\n    velocity: -millisecondsToSeconds(options.velocity || 0)\n  });\n  const initialVelocity = velocity || 0.0;\n  const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n  const initialDelta = target - origin;\n  const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n  /**\n   * If we're working on a granular scale, use smaller defaults for determining\n   * when the spring is finished.\n   *\n   * These defaults have been selected emprically based on what strikes a good\n   * ratio between feeling good and finishing as soon as changes are imperceptible.\n   */\n  const isGranularScale = Math.abs(initialDelta) < 5;\n  restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n  restDelta || (restDelta = isGranularScale ? 0.005 : 0.5);\n  let resolveSpring;\n  if (dampingRatio < 1) {\n    const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n    // Underdamped spring\n    resolveSpring = t => {\n      const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n      return target - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) / angularFreq * Math.sin(angularFreq * t) + initialDelta * Math.cos(angularFreq * t));\n    };\n  } else if (dampingRatio === 1) {\n    // Critically damped spring\n    resolveSpring = t => target - Math.exp(-undampedAngularFreq * t) * (initialDelta + (initialVelocity + undampedAngularFreq * initialDelta) * t);\n  } else {\n    // Overdamped spring\n    const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n    resolveSpring = t => {\n      const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n      // When performing sinh or cosh values can hit Infinity so we cap them here\n      const freqForT = Math.min(dampedAngularFreq * t, 300);\n      return target - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) * Math.sinh(freqForT) + dampedAngularFreq * initialDelta * Math.cosh(freqForT)) / dampedAngularFreq;\n    };\n  }\n  return {\n    calculatedDuration: isResolvedFromDuration ? duration || null : null,\n    next: t => {\n      const current = resolveSpring(t);\n      if (!isResolvedFromDuration) {\n        let currentVelocity = initialVelocity;\n        if (t !== 0) {\n          /**\n           * We only need to calculate velocity for under-damped springs\n           * as over- and critically-damped springs can't overshoot, so\n           * checking only for displacement is enough.\n           */\n          if (dampingRatio < 1) {\n            currentVelocity = calcGeneratorVelocity(resolveSpring, t, current);\n          } else {\n            currentVelocity = 0;\n          }\n        }\n        const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n        const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n        state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n      } else {\n        state.done = t >= duration;\n      }\n      state.value = state.done ? target : current;\n      return state;\n    }\n  };\n}\nexport { spring };", "map": {"version": 3, "names": ["millisecondsToSeconds", "calcGeneratorVelocity", "findSpring", "calcAngularFreq", "durationKeys", "physicsKeys", "isSpringType", "options", "keys", "some", "key", "undefined", "getSpringOptions", "springOptions", "velocity", "stiffness", "damping", "mass", "isResolvedFromDuration", "derived", "spring", "keyframes", "restDelta", "restSpeed", "origin", "target", "length", "state", "done", "value", "duration", "initialVelocity", "dampingRatio", "Math", "sqrt", "initialDelta", "undampedAngularFreq", "isGranularScale", "abs", "resolveSpring", "angularFreq", "t", "envelope", "exp", "sin", "cos", "dampedAngularFreq", "freqForT", "min", "sinh", "cosh", "calculatedDuration", "next", "current", "currentVelocity", "isBelowVelocityThreshold", "isBelowDisplacementThreshold"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from '../../../utils/time-conversion.mjs';\nimport { calcGeneratorVelocity } from '../utils/velocity.mjs';\nimport { findSpring, calcAngularFreq } from './find.mjs';\n\nconst durationKeys = [\"duration\", \"bounce\"];\nconst physicsKeys = [\"stiffness\", \"damping\", \"mass\"];\nfunction isSpringType(options, keys) {\n    return keys.some((key) => options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = {\n        velocity: 0.0,\n        stiffness: 100,\n        damping: 10,\n        mass: 1.0,\n        isResolvedFromDuration: false,\n        ...options,\n    };\n    // stiffness/damping/mass overrides duration/bounce\n    if (!isSpringType(options, physicsKeys) &&\n        isSpringType(options, durationKeys)) {\n        const derived = findSpring(options);\n        springOptions = {\n            ...springOptions,\n            ...derived,\n            mass: 1.0,\n        };\n        springOptions.isResolvedFromDuration = true;\n    }\n    return springOptions;\n}\nfunction spring({ keyframes, restDelta, restSpeed, ...options }) {\n    const origin = keyframes[0];\n    const target = keyframes[keyframes.length - 1];\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = { done: false, value: origin };\n    const { stiffness, damping, mass, duration, velocity, isResolvedFromDuration, } = getSpringOptions({\n        ...options,\n        velocity: -millisecondsToSeconds(options.velocity || 0),\n    });\n    const initialVelocity = velocity || 0.0;\n    const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n    const initialDelta = target - origin;\n    const undampedAngularFreq = millisecondsToSeconds(Math.sqrt(stiffness / mass));\n    /**\n     * If we're working on a granular scale, use smaller defaults for determining\n     * when the spring is finished.\n     *\n     * These defaults have been selected emprically based on what strikes a good\n     * ratio between feeling good and finishing as soon as changes are imperceptible.\n     */\n    const isGranularScale = Math.abs(initialDelta) < 5;\n    restSpeed || (restSpeed = isGranularScale ? 0.01 : 2);\n    restDelta || (restDelta = isGranularScale ? 0.005 : 0.5);\n    let resolveSpring;\n    if (dampingRatio < 1) {\n        const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n        // Underdamped spring\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            return (target -\n                envelope *\n                    (((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) /\n                        angularFreq) *\n                        Math.sin(angularFreq * t) +\n                        initialDelta * Math.cos(angularFreq * t)));\n        };\n    }\n    else if (dampingRatio === 1) {\n        // Critically damped spring\n        resolveSpring = (t) => target -\n            Math.exp(-undampedAngularFreq * t) *\n                (initialDelta +\n                    (initialVelocity + undampedAngularFreq * initialDelta) * t);\n    }\n    else {\n        // Overdamped spring\n        const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n        resolveSpring = (t) => {\n            const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n            // When performing sinh or cosh values can hit Infinity so we cap them here\n            const freqForT = Math.min(dampedAngularFreq * t, 300);\n            return (target -\n                (envelope *\n                    ((initialVelocity +\n                        dampingRatio * undampedAngularFreq * initialDelta) *\n                        Math.sinh(freqForT) +\n                        dampedAngularFreq *\n                            initialDelta *\n                            Math.cosh(freqForT))) /\n                    dampedAngularFreq);\n        };\n    }\n    return {\n        calculatedDuration: isResolvedFromDuration ? duration || null : null,\n        next: (t) => {\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                let currentVelocity = initialVelocity;\n                if (t !== 0) {\n                    /**\n                     * We only need to calculate velocity for under-damped springs\n                     * as over- and critically-damped springs can't overshoot, so\n                     * checking only for displacement is enough.\n                     */\n                    if (dampingRatio < 1) {\n                        currentVelocity = calcGeneratorVelocity(resolveSpring, t, current);\n                    }\n                    else {\n                        currentVelocity = 0;\n                    }\n                }\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(target - current) <= restDelta;\n                state.done =\n                    isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            }\n            else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? target : current;\n            return state;\n        },\n    };\n}\n\nexport { spring };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,UAAU,EAAEC,eAAe,QAAQ,YAAY;AAExD,MAAMC,YAAY,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC3C,MAAMC,WAAW,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC;AACpD,SAASC,YAAYA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACC,IAAI,CAAEC,GAAG,IAAKH,OAAO,CAACG,GAAG,CAAC,KAAKC,SAAS,CAAC;AACzD;AACA,SAASC,gBAAgBA,CAACL,OAAO,EAAE;EAC/B,IAAIM,aAAa,GAAG;IAChBC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,GAAG;IACTC,sBAAsB,EAAE,KAAK;IAC7B,GAAGX;EACP,CAAC;EACD;EACA,IAAI,CAACD,YAAY,CAACC,OAAO,EAAEF,WAAW,CAAC,IACnCC,YAAY,CAACC,OAAO,EAAEH,YAAY,CAAC,EAAE;IACrC,MAAMe,OAAO,GAAGjB,UAAU,CAACK,OAAO,CAAC;IACnCM,aAAa,GAAG;MACZ,GAAGA,aAAa;MAChB,GAAGM,OAAO;MACVF,IAAI,EAAE;IACV,CAAC;IACDJ,aAAa,CAACK,sBAAsB,GAAG,IAAI;EAC/C;EACA,OAAOL,aAAa;AACxB;AACA,SAASO,MAAMA,CAAC;EAAEC,SAAS;EAAEC,SAAS;EAAEC,SAAS;EAAE,GAAGhB;AAAQ,CAAC,EAAE;EAC7D,MAAMiB,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;EAC3B,MAAMI,MAAM,GAAGJ,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;EAC9C;AACJ;AACA;AACA;EACI,MAAMC,KAAK,GAAG;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAEL;EAAO,CAAC;EAC5C,MAAM;IAAET,SAAS;IAAEC,OAAO;IAAEC,IAAI;IAAEa,QAAQ;IAAEhB,QAAQ;IAAEI;EAAwB,CAAC,GAAGN,gBAAgB,CAAC;IAC/F,GAAGL,OAAO;IACVO,QAAQ,EAAE,CAACd,qBAAqB,CAACO,OAAO,CAACO,QAAQ,IAAI,CAAC;EAC1D,CAAC,CAAC;EACF,MAAMiB,eAAe,GAAGjB,QAAQ,IAAI,GAAG;EACvC,MAAMkB,YAAY,GAAGhB,OAAO,IAAI,CAAC,GAAGiB,IAAI,CAACC,IAAI,CAACnB,SAAS,GAAGE,IAAI,CAAC,CAAC;EAChE,MAAMkB,YAAY,GAAGV,MAAM,GAAGD,MAAM;EACpC,MAAMY,mBAAmB,GAAGpC,qBAAqB,CAACiC,IAAI,CAACC,IAAI,CAACnB,SAAS,GAAGE,IAAI,CAAC,CAAC;EAC9E;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMoB,eAAe,GAAGJ,IAAI,CAACK,GAAG,CAACH,YAAY,CAAC,GAAG,CAAC;EAClDZ,SAAS,KAAKA,SAAS,GAAGc,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC;EACrDf,SAAS,KAAKA,SAAS,GAAGe,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC;EACxD,IAAIE,aAAa;EACjB,IAAIP,YAAY,GAAG,CAAC,EAAE;IAClB,MAAMQ,WAAW,GAAGrC,eAAe,CAACiC,mBAAmB,EAAEJ,YAAY,CAAC;IACtE;IACAO,aAAa,GAAIE,CAAC,IAAK;MACnB,MAAMC,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAAC,CAACX,YAAY,GAAGI,mBAAmB,GAAGK,CAAC,CAAC;MAClE,OAAQhB,MAAM,GACViB,QAAQ,IACF,CAACX,eAAe,GACdC,YAAY,GAAGI,mBAAmB,GAAGD,YAAY,IACjDK,WAAW,GACXP,IAAI,CAACW,GAAG,CAACJ,WAAW,GAAGC,CAAC,CAAC,GACzBN,YAAY,GAAGF,IAAI,CAACY,GAAG,CAACL,WAAW,GAAGC,CAAC,CAAC,CAAC;IACzD,CAAC;EACL,CAAC,MACI,IAAIT,YAAY,KAAK,CAAC,EAAE;IACzB;IACAO,aAAa,GAAIE,CAAC,IAAKhB,MAAM,GACzBQ,IAAI,CAACU,GAAG,CAAC,CAACP,mBAAmB,GAAGK,CAAC,CAAC,IAC7BN,YAAY,GACT,CAACJ,eAAe,GAAGK,mBAAmB,GAAGD,YAAY,IAAIM,CAAC,CAAC;EAC3E,CAAC,MACI;IACD;IACA,MAAMK,iBAAiB,GAAGV,mBAAmB,GAAGH,IAAI,CAACC,IAAI,CAACF,YAAY,GAAGA,YAAY,GAAG,CAAC,CAAC;IAC1FO,aAAa,GAAIE,CAAC,IAAK;MACnB,MAAMC,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAAC,CAACX,YAAY,GAAGI,mBAAmB,GAAGK,CAAC,CAAC;MAClE;MACA,MAAMM,QAAQ,GAAGd,IAAI,CAACe,GAAG,CAACF,iBAAiB,GAAGL,CAAC,EAAE,GAAG,CAAC;MACrD,OAAQhB,MAAM,GACTiB,QAAQ,IACJ,CAACX,eAAe,GACbC,YAAY,GAAGI,mBAAmB,GAAGD,YAAY,IACjDF,IAAI,CAACgB,IAAI,CAACF,QAAQ,CAAC,GACnBD,iBAAiB,GACbX,YAAY,GACZF,IAAI,CAACiB,IAAI,CAACH,QAAQ,CAAC,CAAC,GAC5BD,iBAAiB;IAC7B,CAAC;EACL;EACA,OAAO;IACHK,kBAAkB,EAAEjC,sBAAsB,GAAGY,QAAQ,IAAI,IAAI,GAAG,IAAI;IACpEsB,IAAI,EAAGX,CAAC,IAAK;MACT,MAAMY,OAAO,GAAGd,aAAa,CAACE,CAAC,CAAC;MAChC,IAAI,CAACvB,sBAAsB,EAAE;QACzB,IAAIoC,eAAe,GAAGvB,eAAe;QACrC,IAAIU,CAAC,KAAK,CAAC,EAAE;UACT;AACpB;AACA;AACA;AACA;UACoB,IAAIT,YAAY,GAAG,CAAC,EAAE;YAClBsB,eAAe,GAAGrD,qBAAqB,CAACsC,aAAa,EAAEE,CAAC,EAAEY,OAAO,CAAC;UACtE,CAAC,MACI;YACDC,eAAe,GAAG,CAAC;UACvB;QACJ;QACA,MAAMC,wBAAwB,GAAGtB,IAAI,CAACK,GAAG,CAACgB,eAAe,CAAC,IAAI/B,SAAS;QACvE,MAAMiC,4BAA4B,GAAGvB,IAAI,CAACK,GAAG,CAACb,MAAM,GAAG4B,OAAO,CAAC,IAAI/B,SAAS;QAC5EK,KAAK,CAACC,IAAI,GACN2B,wBAAwB,IAAIC,4BAA4B;MAChE,CAAC,MACI;QACD7B,KAAK,CAACC,IAAI,GAAGa,CAAC,IAAIX,QAAQ;MAC9B;MACAH,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACC,IAAI,GAAGH,MAAM,GAAG4B,OAAO;MAC3C,OAAO1B,KAAK;IAChB;EACJ,CAAC;AACL;AAEA,SAASP,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}