{"ast": null, "code": "import { useMotionValue } from './use-motion-value.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { cancelFrame, frame } from '../frameloop/frame.mjs';\nfunction useCombineMotionValues(values, combineValues) {\n  /**\n   * Initialise the returned motion value. This remains the same between renders.\n   */\n  const value = useMotionValue(combineValues());\n  /**\n   * Create a function that will update the template motion value with the latest values.\n   * This is pre-bound so whenever a motion value updates it can schedule its\n   * execution in Framesync. If it's already been scheduled it won't be fired twice\n   * in a single frame.\n   */\n  const updateValue = () => value.set(combineValues());\n  /**\n   * Synchronously update the motion value with the latest values during the render.\n   * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n   */\n  updateValue();\n  /**\n   * Subscribe to all motion values found within the template. Whenever any of them change,\n   * schedule an update.\n   */\n  useIsomorphicLayoutEffect(() => {\n    const scheduleUpdate = () => frame.update(updateValue, false, true);\n    const subscriptions = values.map(v => v.on(\"change\", scheduleUpdate));\n    return () => {\n      subscriptions.forEach(unsubscribe => unsubscribe());\n      cancelFrame(updateValue);\n    };\n  });\n  return value;\n}\nexport { useCombineMotionValues };", "map": {"version": 3, "names": ["useMotionValue", "useIsomorphicLayoutEffect", "cancelFrame", "frame", "useCombineMotionValues", "values", "combineValues", "value", "updateValue", "set", "scheduleUpdate", "update", "subscriptions", "map", "v", "on", "for<PERSON>ach", "unsubscribe"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/value/use-combine-values.mjs"], "sourcesContent": ["import { useMotionValue } from './use-motion-value.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { cancelFrame, frame } from '../frameloop/frame.mjs';\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = useMotionValue(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    useIsomorphicLayoutEffect(() => {\n        const scheduleUpdate = () => frame.update(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            cancelFrame(updateValue);\n        };\n    });\n    return value;\n}\n\nexport { useCombineMotionValues };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,WAAW,EAAEC,KAAK,QAAQ,wBAAwB;AAE3D,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACnD;AACJ;AACA;EACI,MAAMC,KAAK,GAAGP,cAAc,CAACM,aAAa,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,WAAW,GAAGA,CAAA,KAAMD,KAAK,CAACE,GAAG,CAACH,aAAa,CAAC,CAAC,CAAC;EACpD;AACJ;AACA;AACA;EACIE,WAAW,CAAC,CAAC;EACb;AACJ;AACA;AACA;EACIP,yBAAyB,CAAC,MAAM;IAC5B,MAAMS,cAAc,GAAGA,CAAA,KAAMP,KAAK,CAACQ,MAAM,CAACH,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;IACnE,MAAMI,aAAa,GAAGP,MAAM,CAACQ,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC,QAAQ,EAAEL,cAAc,CAAC,CAAC;IACvE,OAAO,MAAM;MACTE,aAAa,CAACI,OAAO,CAAEC,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC;MACrDf,WAAW,CAACM,WAAW,CAAC;IAC5B,CAAC;EACL,CAAC,CAAC;EACF,OAAOD,KAAK;AAChB;AAEA,SAASH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}