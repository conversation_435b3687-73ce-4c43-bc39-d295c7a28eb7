{"ast": null, "code": "import { useContext } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { useReducedMotion } from './use-reduced-motion.mjs';\nfunction useReducedMotionConfig() {\n  const reducedMotionPreference = useReducedMotion();\n  const {\n    reducedMotion\n  } = useContext(MotionConfigContext);\n  if (reducedMotion === \"never\") {\n    return false;\n  } else if (reducedMotion === \"always\") {\n    return true;\n  } else {\n    return reducedMotionPreference;\n  }\n}\nexport { useReducedMotionConfig };", "map": {"version": 3, "names": ["useContext", "MotionConfigContext", "useReducedMotion", "useReducedMotionConfig", "reducedMotionPreference", "reducedMotion"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/utils/reduced-motion/use-reduced-motion-config.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { useReducedMotion } from './use-reduced-motion.mjs';\n\nfunction useReducedMotionConfig() {\n    const reducedMotionPreference = useReducedMotion();\n    const { reducedMotion } = useContext(MotionConfigContext);\n    if (reducedMotion === \"never\") {\n        return false;\n    }\n    else if (reducedMotion === \"always\") {\n        return true;\n    }\n    else {\n        return reducedMotionPreference;\n    }\n}\n\nexport { useReducedMotionConfig };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,gBAAgB,QAAQ,0BAA0B;AAE3D,SAASC,sBAAsBA,CAAA,EAAG;EAC9B,MAAMC,uBAAuB,GAAGF,gBAAgB,CAAC,CAAC;EAClD,MAAM;IAAEG;EAAc,CAAC,GAAGL,UAAU,CAACC,mBAAmB,CAAC;EACzD,IAAII,aAAa,KAAK,OAAO,EAAE;IAC3B,OAAO,KAAK;EAChB,CAAC,MACI,IAAIA,aAAa,KAAK,QAAQ,EAAE;IACjC,OAAO,IAAI;EACf,CAAC,MACI;IACD,OAAOD,uBAAuB;EAClC;AACJ;AAEA,SAASD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}