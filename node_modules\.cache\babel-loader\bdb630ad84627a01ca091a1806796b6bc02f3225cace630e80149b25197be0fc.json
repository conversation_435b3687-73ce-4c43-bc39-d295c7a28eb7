{"ast": null, "code": "import { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n  const newValues = scrapeMotionValuesFromProps$1(props, prevProps);\n  for (const key in props) {\n    if (isMotionValue(props[key]) || isMotionValue(prevProps[key])) {\n      const targetKey = transformPropOrder.indexOf(key) !== -1 ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1) : key;\n      newValues[targetKey] = props[key];\n    }\n  }\n  return newValues;\n}\nexport { scrapeMotionValuesFromProps };", "map": {"version": 3, "names": ["isMotionValue", "scrapeMotionValuesFromProps", "scrapeMotionValuesFromProps$1", "transformPropOrder", "props", "prevProps", "newValues", "key", "<PERSON><PERSON><PERSON>", "indexOf", "char<PERSON>t", "toUpperCase", "substring"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue } from '../../../value/utils/is-motion-value.mjs';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\nimport { transformPropOrder } from '../../html/utils/transform.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps);\n    for (const key in props) {\n        if (isMotionValue(props[key]) || isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,0CAA0C;AACxE,SAASC,2BAA2B,IAAIC,6BAA6B,QAAQ,2CAA2C;AACxH,SAASC,kBAAkB,QAAQ,gCAAgC;AAEnE,SAASF,2BAA2BA,CAACG,KAAK,EAAEC,SAAS,EAAE;EACnD,MAAMC,SAAS,GAAGJ,6BAA6B,CAACE,KAAK,EAAEC,SAAS,CAAC;EACjE,KAAK,MAAME,GAAG,IAAIH,KAAK,EAAE;IACrB,IAAIJ,aAAa,CAACI,KAAK,CAACG,GAAG,CAAC,CAAC,IAAIP,aAAa,CAACK,SAAS,CAACE,GAAG,CAAC,CAAC,EAAE;MAC5D,MAAMC,SAAS,GAAGL,kBAAkB,CAACM,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,GAClD,MAAM,GAAGA,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,GAAG,CAACK,SAAS,CAAC,CAAC,CAAC,GACvDL,GAAG;MACTD,SAAS,CAACE,SAAS,CAAC,GAAGJ,KAAK,CAACG,GAAG,CAAC;IACrC;EACJ;EACA,OAAOD,SAAS;AACpB;AAEA,SAASL,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}