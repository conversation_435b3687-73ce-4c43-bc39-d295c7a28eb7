{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed === 'true' ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n_c = SidebarContainer;\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c2 = SidebarHeader;\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c3 = Logo;\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c4 = LogoText;\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n_c5 = Navigation;\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n_c6 = NavItem;\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n_c7 = NavLink;\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n_c8 = NavIcon;\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c9 = NavText;\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n_c0 = NavBadge;\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n_c1 = SidebarFooter;\nconst UserProfile = styled.div`\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 107, 53, 0.1);\n  border: 1px solid rgba(255, 107, 53, 0.2);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.15);\n    border-color: rgba(255, 107, 53, 0.3);\n  }\n`;\n_c10 = UserProfile;\nconst UserMenu = styled(motion.div)`\n  position: absolute;\n  bottom: 100%;\n  left: 0;\n  right: 0;\n  margin-bottom: 8px;\n  background: rgba(26, 26, 26, 0.95);\n  border: 1px solid rgba(255, 107, 53, 0.3);\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  z-index: 1000;\n`;\n_c11 = UserMenu;\nconst UserMenuItem = styled.div`\n  padding: 12px 16px;\n  color: ${props => props.theme.colors.text.primary};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.1);\n    color: #ff6b35;\n  }\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  }\n`;\n_c12 = UserMenuItem;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);\n`;\n_c13 = UserAvatar;\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n_c14 = UserInfo;\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n_c15 = UserName;\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n_c16 = UserStatus;\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n_c17 = ToggleButton;\nconst Sidebar = ({\n  collapsed,\n  onToggle\n}) => {\n  _s();\n  var _user$username;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    getStats\n  } = useGameResources();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const stats = getStats();\n  const navigationItems = [{\n    path: '/',\n    icon: '🏠',\n    label: '主页',\n    badge: null\n  }, {\n    path: '/modules',\n    icon: '📦',\n    label: '游戏文件',\n    badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n  }, {\n    path: '/store',\n    icon: '🛒',\n    label: '试玩和购买',\n    badge: null\n  }, {\n    path: '/settings',\n    icon: '⚙️',\n    label: '系统选项',\n    badge: null\n  }];\n\n  // 管理员专用菜单项\n  const adminNavigationItems = [{\n    path: '/admin/users',\n    icon: '👥',\n    label: '用户管理',\n    badge: null\n  }, {\n    path: '/admin/modules',\n    icon: '🔧',\n    label: '模组管理',\n    badge: null\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  const handleUserClick = () => {\n    setShowUserMenu(!showUserMenu);\n  };\n  const handleSwitchUser = () => {\n    setShowUserMenu(false);\n    logout();\n    navigate('/login');\n  };\n  const handleLogout = () => {\n    setShowUserMenu(false);\n    logout();\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(SidebarContainer, {\n    collapsed: collapsed.toString(),\n    initial: {\n      x: -240\n    },\n    animate: {\n      x: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        collapsed: collapsed.toString(),\n        children: \"DCS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      children: [navigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n        whileHover: {\n          scale: 1.02\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        children: /*#__PURE__*/_jsxDEV(NavLink, {\n          active: location.pathname === item.path,\n          onClick: () => handleNavigation(item.path),\n          children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavText, {\n            collapsed: collapsed.toString(),\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), item.badge && /*#__PURE__*/_jsxDEV(NavBadge, {\n            collapsed: collapsed.toString(),\n            children: item.badge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, item.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this)), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0 8px 0',\n            padding: '0 20px',\n            fontSize: '12px',\n            color: '#666',\n            opacity: collapsed ? 0 : 1,\n            transition: 'opacity 0.3s ease'\n          },\n          children: \"\\u7BA1\\u7406\\u5458\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), adminNavigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: /*#__PURE__*/_jsxDEV(NavLink, {\n            active: location.pathname === item.path,\n            onClick: () => handleNavigation(item.path),\n            children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(NavText, {\n              collapsed: collapsed.toString(),\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this), item.badge && /*#__PURE__*/_jsxDEV(NavBadge, {\n              collapsed: collapsed.toString(),\n              children: item.badge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this)\n        }, item.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidebarFooter, {\n      children: /*#__PURE__*/_jsxDEV(UserProfile, {\n        onClick: handleUserClick,\n        children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n          children: showUserMenu && !collapsed && /*#__PURE__*/_jsxDEV(UserMenu, {\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: 10\n            },\n            transition: {\n              duration: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(UserMenuItem, {\n              onClick: handleSwitchUser,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDD04\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), \"\\u5207\\u6362\\u7528\\u6237\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(UserMenuItem, {\n              onClick: handleLogout,\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDEAA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), \"\\u9000\\u51FA\\u767B\\u5F55\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserAvatar, {\n          children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          collapsed: collapsed.toString(),\n          children: [/*#__PURE__*/_jsxDEV(UserName, {\n            children: (user === null || user === void 0 ? void 0 : user.username) || 'Guest'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserStatus, {\n            children: (user === null || user === void 0 ? void 0 : user.role) === 'admin' ? '管理员' : '在线'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n      onClick: onToggle,\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: collapsed ? '→' : '←'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"2sdMcIN/OHnAB74Nz4LljkOna2c=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useGameResources];\n});\n_c18 = Sidebar;\nexport default Sidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"SidebarContainer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"Navigation\");\n$RefreshReg$(_c6, \"NavItem\");\n$RefreshReg$(_c7, \"NavLink\");\n$RefreshReg$(_c8, \"NavIcon\");\n$RefreshReg$(_c9, \"NavText\");\n$RefreshReg$(_c0, \"NavBadge\");\n$RefreshReg$(_c1, \"SidebarFooter\");\n$RefreshReg$(_c10, \"UserProfile\");\n$RefreshReg$(_c11, \"UserMenu\");\n$RefreshReg$(_c12, \"UserMenuItem\");\n$RefreshReg$(_c13, \"UserAvatar\");\n$RefreshReg$(_c14, \"UserInfo\");\n$RefreshReg$(_c15, \"UserName\");\n$RefreshReg$(_c16, \"UserStatus\");\n$RefreshReg$(_c17, \"ToggleButton\");\n$RefreshReg$(_c18, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useLocation", "useNavigate", "styled", "motion", "AnimatePresence", "useAuth", "useGameResources", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SidebarContainer", "aside", "props", "collapsed", "theme", "components", "sidebar", "collapsedWidth", "width", "colors", "border", "primary", "_c", "SidebarHeader", "div", "secondary", "_c2", "Logo", "_c3", "LogoText", "fontSizes", "lg", "fontWeights", "bold", "text", "_c4", "Navigation", "nav", "_c5", "NavItem", "_c6", "NavLink", "button", "active", "sm", "medium", "normal", "_c7", "NavIcon", "span", "_c8", "NavText", "_c9", "NavBadge", "_c0", "SidebarFooter", "_c1", "UserProfile", "_c10", "UserMenu", "_c11", "UserMenuItem", "_c12", "UserAvatar", "_c13", "UserInfo", "_c14", "UserName", "_c15", "UserStatus", "xs", "tertiary", "_c16", "ToggleButton", "background", "_c17", "Sidebar", "onToggle", "_s", "_user$username", "location", "navigate", "user", "logout", "getStats", "showUserMenu", "setShowUserMenu", "stats", "navigationItems", "path", "icon", "label", "badge", "hasUpdates", "adminNavigationItems", "handleNavigation", "handleUserClick", "handleSwitchUser", "handleLogout", "toString", "initial", "x", "animate", "transition", "duration", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "whileHover", "scale", "whileTap", "pathname", "onClick", "role", "style", "margin", "padding", "fontSize", "color", "opacity", "y", "exit", "username", "char<PERSON>t", "toUpperCase", "_c18", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/components/layout/Sidebar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\n\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed === 'true' ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n\nconst UserProfile = styled.div`\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 107, 53, 0.1);\n  border: 1px solid rgba(255, 107, 53, 0.2);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.15);\n    border-color: rgba(255, 107, 53, 0.3);\n  }\n`;\n\nconst UserMenu = styled(motion.div)`\n  position: absolute;\n  bottom: 100%;\n  left: 0;\n  right: 0;\n  margin-bottom: 8px;\n  background: rgba(26, 26, 26, 0.95);\n  border: 1px solid rgba(255, 107, 53, 0.3);\n  border-radius: 8px;\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n  z-index: 1000;\n`;\n\nconst UserMenuItem = styled.div`\n  padding: 12px 16px;\n  color: ${props => props.theme.colors.text.primary};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.1);\n    color: #ff6b35;\n  }\n  \n  &:not(:last-child) {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);\n`;\n\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed === 'true' ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n\nconst Sidebar = ({ collapsed, onToggle }) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { getStats } = useGameResources();\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  \n  const stats = getStats();\n\n  const navigationItems = [\n    {\n      path: '/',\n      icon: '🏠',\n      label: '主页',\n      badge: null\n    },\n    {\n      path: '/modules',\n      icon: '📦',\n      label: '游戏文件',\n      badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n    },\n    {\n      path: '/store',\n      icon: '🛒',\n      label: '试玩和购买',\n      badge: null\n    },\n    {\n      path: '/settings',\n      icon: '⚙️',\n      label: '系统选项',\n      badge: null\n    }\n  ];\n\n  // 管理员专用菜单项\n  const adminNavigationItems = [\n    {\n      path: '/admin/users',\n      icon: '👥',\n      label: '用户管理',\n      badge: null\n    },\n    {\n      path: '/admin/modules',\n      icon: '🔧',\n      label: '模组管理',\n      badge: null\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  const handleUserClick = () => {\n    setShowUserMenu(!showUserMenu);\n  };\n\n  const handleSwitchUser = () => {\n    setShowUserMenu(false);\n    logout();\n    navigate('/login');\n  };\n\n  const handleLogout = () => {\n    setShowUserMenu(false);\n    logout();\n    navigate('/login');\n  };\n\n  return (\n    <SidebarContainer\n      collapsed={collapsed.toString()}\n      initial={{ x: -240 }}\n      animate={{ x: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <SidebarHeader>\n        <Logo>D</Logo>\n        <LogoText collapsed={collapsed.toString()}>DCS</LogoText>\n      </SidebarHeader>\n\n      <Navigation>\n        {navigationItems.map((item) => (\n          <NavItem\n            key={item.path}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <NavLink\n              active={location.pathname === item.path}\n              onClick={() => handleNavigation(item.path)}\n            >\n              <NavIcon>{item.icon}</NavIcon>\n              <NavText collapsed={collapsed.toString()}>{item.label}</NavText>\n              {item.badge && (\n                <NavBadge collapsed={collapsed.toString()}>{item.badge}</NavBadge>\n              )}\n            </NavLink>\n          </NavItem>\n        ))}\n        \n        {/* 管理员专用菜单 */}\n        {user?.role === 'admin' && (\n          <>\n            <div style={{\n              margin: '16px 0 8px 0',\n              padding: '0 20px',\n              fontSize: '12px',\n              color: '#666',\n              opacity: collapsed ? 0 : 1,\n              transition: 'opacity 0.3s ease'\n            }}>\n              管理员功能\n            </div>\n            {adminNavigationItems.map((item) => (\n              <NavItem\n                key={item.path}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <NavLink\n                  active={location.pathname === item.path}\n                  onClick={() => handleNavigation(item.path)}\n                >\n                  <NavIcon>{item.icon}</NavIcon>\n                  <NavText collapsed={collapsed.toString()}>{item.label}</NavText>\n                  {item.badge && (\n                    <NavBadge collapsed={collapsed.toString()}>{item.badge}</NavBadge>\n                  )}\n                </NavLink>\n              </NavItem>\n            ))}\n          </>\n        )}\n      </Navigation>\n\n      <SidebarFooter>\n        <UserProfile onClick={handleUserClick}>\n          <AnimatePresence>\n            {showUserMenu && !collapsed && (\n              <UserMenu\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 10 }}\n                transition={{ duration: 0.2 }}\n              >\n                <UserMenuItem onClick={handleSwitchUser}>\n                  <span>🔄</span>\n                  切换用户\n                </UserMenuItem>\n                <UserMenuItem onClick={handleLogout}>\n                  <span>🚪</span>\n                  退出登录\n                </UserMenuItem>\n              </UserMenu>\n            )}\n          </AnimatePresence>\n          <UserAvatar>\n            {user?.username?.charAt(0).toUpperCase() || 'U'}\n          </UserAvatar>\n          <UserInfo collapsed={collapsed.toString()}>\n            <UserName>{user?.username || 'Guest'}</UserName>\n            <UserStatus>{user?.role === 'admin' ? '管理员' : '在线'}</UserStatus>\n          </UserInfo>\n        </UserProfile>\n      </SidebarFooter>\n\n      <ToggleButton\n        onClick={onToggle}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {collapsed ? '→' : '←'}\n      </ToggleButton>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,gBAAgB,GAAGT,MAAM,CAACC,MAAM,CAACS,KAAK,CAAC;AAC7C,WAAWC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,GAAGD,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACC,cAAc,GAAGL,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACE,KAAK;AACrI;AACA;AACA,4BAA4BN,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AACtE;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIZ,gBAAgB;AAWtB,MAAMa,aAAa,GAAGtB,MAAM,CAACuB,GAAG;AAChC;AACA,6BAA6BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACzE;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,aAAa;AAQnB,MAAMI,IAAI,GAAG1B,MAAM,CAACuB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAZID,IAAI;AAcV,MAAME,QAAQ,GAAG5B,MAAM,CAACuB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACC,EAAE;AAChD,iBAAiBnB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACC,IAAI;AACtD,WAAWrB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AACxD;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GARIN,QAAQ;AAUd,MAAMO,UAAU,GAAGnC,MAAM,CAACoC,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,OAAO,GAAGtC,MAAM,CAACC,MAAM,CAACsB,GAAG,CAAC;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,OAAO;AAMb,MAAME,OAAO,GAAGxC,MAAM,CAACyC,MAAM;AAC7B;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG,yBAAyB,GAAG,aAAa;AACjF;AACA,2BAA2B/B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAG,aAAa;AAC7F,WAAWT,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAGT,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACjG,eAAeb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM,GAAGjC,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACc,MAAM;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAalC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACrD;AACA,CAAC;AAAC0B,GAAA,GApBIN,OAAO;AAsBb,MAAMO,OAAO,GAAG/C,MAAM,CAACgD,IAAI;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,OAAO;AAOb,MAAMG,OAAO,GAAGlD,MAAM,CAACgD,IAAI;AAC3B,aAAarC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AACxD;AACA;AACA;AACA,CAAC;AAACuC,GAAA,GALID,OAAO;AAOb,MAAME,QAAQ,GAAGpD,MAAM,CAACgD,IAAI;AAC5B,gBAAgBrC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AACxD;AACA,CAAC;AAACyC,GAAA,GATID,QAAQ;AAWd,MAAME,aAAa,GAAGtD,MAAM,CAACuB,GAAG;AAChC;AACA,0BAA0BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACtE,CAAC;AAAC+B,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGxD,MAAM,CAACuB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAhBID,WAAW;AAkBjB,MAAME,QAAQ,GAAG1D,MAAM,CAACC,MAAM,CAACsB,GAAG,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAZID,QAAQ;AAcd,MAAME,YAAY,GAAG5D,MAAM,CAACuB,GAAG;AAC/B;AACA,WAAWZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GAlBID,YAAY;AAoBlB,MAAME,UAAU,GAAG9D,MAAM,CAACuB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GAbID,UAAU;AAehB,MAAME,QAAQ,GAAGhE,MAAM,CAACuB,GAAG;AAC3B,aAAaZ,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AACxD;AACA;AACA,CAAC;AAACqD,IAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGlE,MAAM,CAACuB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM;AACxD,WAAWjC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD;AACA,CAAC;AAAC+C,IAAA,GALID,QAAQ;AAOd,MAAME,UAAU,GAAGpE,MAAM,CAACuB,GAAG;AAC7B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACwC,EAAE;AAChD,WAAW1D,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACqC,QAAQ;AACpD;AACA,CAAC;AAACC,IAAA,GAJIH,UAAU;AAMhB,MAAMI,YAAY,GAAGxE,MAAM,CAACC,MAAM,CAACwC,MAAM,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACuD,UAAU,CAACjD,SAAS;AAChE,sBAAsBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA,WAAWT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACrD;AACA,oBAAoBT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACvD;AACA,CAAC;AAACsD,IAAA,GAvBIF,YAAY;AAyBlB,MAAMG,OAAO,GAAGA,CAAC;EAAE/D,SAAS;EAAEgE;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC3C,MAAMC,QAAQ,GAAGjF,WAAW,CAAC,CAAC;EAC9B,MAAMkF,QAAQ,GAAGjF,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkF,IAAI;IAAEC;EAAO,CAAC,GAAG/E,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEgF;EAAS,CAAC,GAAG/E,gBAAgB,CAAC,CAAC;EACvC,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMyF,KAAK,GAAGH,QAAQ,CAAC,CAAC;EAExB,MAAMI,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAEL,KAAK,CAACM,UAAU,GAAG,CAAC,GAAGN,KAAK,CAACM,UAAU,GAAG;EACnD,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,oBAAoB,GAAG,CAC3B;IACEL,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,gBAAgB,GAAIN,IAAI,IAAK;IACjCR,QAAQ,CAACQ,IAAI,CAAC;EAChB,CAAC;EAED,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5BV,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7BX,eAAe,CAAC,KAAK,CAAC;IACtBH,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzBZ,eAAe,CAAC,KAAK,CAAC;IACtBH,MAAM,CAAC,CAAC;IACRF,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACE1E,OAAA,CAACG,gBAAgB;IACfG,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;IAChCC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9BlG,OAAA,CAACgB,aAAa;MAAAkF,QAAA,gBACZlG,OAAA,CAACoB,IAAI;QAAA8E,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACdtG,OAAA,CAACsB,QAAQ;QAAChB,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;QAAAM,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAEhBtG,OAAA,CAAC6B,UAAU;MAAAqE,QAAA,GACRjB,eAAe,CAACsB,GAAG,CAAEC,IAAI,iBACxBxG,OAAA,CAACgC,OAAO;QAENyE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAR,QAAA,eAE1BlG,OAAA,CAACkC,OAAO;UACNE,MAAM,EAAEqC,QAAQ,CAACmC,QAAQ,KAAKJ,IAAI,CAACtB,IAAK;UACxC2B,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAACgB,IAAI,CAACtB,IAAI,CAAE;UAAAgB,QAAA,gBAE3ClG,OAAA,CAACyC,OAAO;YAAAyD,QAAA,EAAEM,IAAI,CAACrB;UAAI;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9BtG,OAAA,CAAC4C,OAAO;YAACtC,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;YAAAM,QAAA,EAAEM,IAAI,CAACpB;UAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EAC/DE,IAAI,CAACnB,KAAK,iBACTrF,OAAA,CAAC8C,QAAQ;YAACxC,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;YAAAM,QAAA,EAAEM,IAAI,CAACnB;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAClE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC,GAbLE,IAAI,CAACtB,IAAI;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcP,CACV,CAAC,EAGD,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,iBACrB9G,OAAA,CAAAE,SAAA;QAAAgG,QAAA,gBACElG,OAAA;UAAK+G,KAAK,EAAE;YACVC,MAAM,EAAE,cAAc;YACtBC,OAAO,EAAE,QAAQ;YACjBC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE9G,SAAS,GAAG,CAAC,GAAG,CAAC;YAC1B0F,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACLf,oBAAoB,CAACgB,GAAG,CAAEC,IAAI,iBAC7BxG,OAAA,CAACgC,OAAO;UAENyE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAR,QAAA,eAE1BlG,OAAA,CAACkC,OAAO;YACNE,MAAM,EAAEqC,QAAQ,CAACmC,QAAQ,KAAKJ,IAAI,CAACtB,IAAK;YACxC2B,OAAO,EAAEA,CAAA,KAAMrB,gBAAgB,CAACgB,IAAI,CAACtB,IAAI,CAAE;YAAAgB,QAAA,gBAE3ClG,OAAA,CAACyC,OAAO;cAAAyD,QAAA,EAAEM,IAAI,CAACrB;YAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9BtG,OAAA,CAAC4C,OAAO;cAACtC,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;cAAAM,QAAA,EAAEM,IAAI,CAACpB;YAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAC/DE,IAAI,CAACnB,KAAK,iBACTrF,OAAA,CAAC8C,QAAQ;cAACxC,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;cAAAM,QAAA,EAAEM,IAAI,CAACnB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC,GAbLE,IAAI,CAACtB,IAAI;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcP,CACV,CAAC;MAAA,eACF,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEbtG,OAAA,CAACgD,aAAa;MAAAkD,QAAA,eACZlG,OAAA,CAACkD,WAAW;QAAC2D,OAAO,EAAEpB,eAAgB;QAAAS,QAAA,gBACpClG,OAAA,CAACJ,eAAe;UAAAsG,QAAA,EACbpB,YAAY,IAAI,CAACxE,SAAS,iBACzBN,OAAA,CAACoD,QAAQ;YACPyC,OAAO,EAAE;cAAEuB,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BtB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC5BrB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAC,QAAA,gBAE9BlG,OAAA,CAACsD,YAAY;cAACuD,OAAO,EAAEnB,gBAAiB;cAAAQ,QAAA,gBACtClG,OAAA;gBAAAkG,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,4BAEjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACftG,OAAA,CAACsD,YAAY;cAACuD,OAAO,EAAElB,YAAa;cAAAO,QAAA,gBAClClG,OAAA;gBAAAkG,QAAA,EAAM;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,4BAEjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,eAClBtG,OAAA,CAACwD,UAAU;UAAA0C,QAAA,EACR,CAAAvB,IAAI,aAAJA,IAAI,wBAAAH,cAAA,GAAJG,IAAI,CAAE4C,QAAQ,cAAA/C,cAAA,uBAAdA,cAAA,CAAgBgD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAG;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACbtG,OAAA,CAAC0D,QAAQ;UAACpD,SAAS,EAAEA,SAAS,CAACsF,QAAQ,CAAC,CAAE;UAAAM,QAAA,gBACxClG,OAAA,CAAC4D,QAAQ;YAAAsC,QAAA,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4C,QAAQ,KAAI;UAAO;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChDtG,OAAA,CAAC8D,UAAU;YAAAoC,QAAA,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,GAAG,KAAK,GAAG;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEhBtG,OAAA,CAACkE,YAAY;MACX2C,OAAO,EAAEvC,QAAS;MAClBmC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MAAAR,QAAA,EAExB5F,SAAS,GAAG,GAAG,GAAG;IAAG;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEvB,CAAC;AAAC/B,EAAA,CAnLIF,OAAO;EAAA,QACM7E,WAAW,EACXC,WAAW,EACHI,OAAO,EACXC,gBAAgB;AAAA;AAAA4H,IAAA,GAJjCrD,OAAO;AAqLb,eAAeA,OAAO;AAAC,IAAAtD,EAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAsD,IAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}