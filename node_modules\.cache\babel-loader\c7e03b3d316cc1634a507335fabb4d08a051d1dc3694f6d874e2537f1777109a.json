{"ast": null, "code": "import { createGlobalStyle } from 'styled-components';\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html, body {\n    width: 100%;\n    height: 100%;\n    font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;\n    background: #1a1a1a;\n    color: #ffffff;\n    overflow: hidden;\n    user-select: none;\n    -webkit-user-select: none;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  #root {\n    width: 100%;\n    height: 100%;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: rgba(255, 107, 53, 0.6);\n    border-radius: 4px;\n    transition: background 0.3s ease;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: rgba(255, 107, 53, 0.8);\n  }\n\n  /* 按钮基础样式 */\n  button {\n    border: none;\n    outline: none;\n    cursor: pointer;\n    font-family: inherit;\n    transition: all 0.3s ease;\n  }\n\n  button:disabled {\n    cursor: not-allowed;\n    opacity: 0.6;\n  }\n\n  /* 输入框基础样式 */\n  input, textarea {\n    border: none;\n    outline: none;\n    font-family: inherit;\n    background: transparent;\n  }\n\n  /* 链接样式 */\n  a {\n    color: inherit;\n    text-decoration: none;\n  }\n\n  /* 选择文本样式 */\n  ::selection {\n    background: rgba(255, 107, 53, 0.3);\n    color: #ffffff;\n  }\n\n  /* 禁用拖拽 */\n  img, svg {\n    -webkit-user-drag: none;\n    user-drag: none;\n  }\n\n  /* 动画关键帧 */\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(20px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n\n  @keyframes slideInLeft {\n    from {\n      opacity: 0;\n      transform: translateX(-30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n\n  @keyframes slideInRight {\n    from {\n      opacity: 0;\n      transform: translateX(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n    }\n    50% {\n      transform: scale(1.05);\n    }\n    100% {\n      transform: scale(1);\n    }\n  }\n\n  @keyframes spin {\n    from {\n      transform: rotate(0deg);\n    }\n    to {\n      transform: rotate(360deg);\n    }\n  }\n\n  /* 工具类 */\n  .fade-in {\n    animation: fadeIn 0.6s ease-out;\n  }\n\n  .slide-in-left {\n    animation: slideInLeft 0.6s ease-out;\n  }\n\n  .slide-in-right {\n    animation: slideInRight 0.6s ease-out;\n  }\n\n  .pulse {\n    animation: pulse 2s infinite;\n  }\n\n  .spin {\n    animation: spin 1s linear infinite;\n  }\n\n  /* 响应式断点 */\n  @media (max-width: 1024px) {\n    html {\n      font-size: 14px;\n    }\n  }\n\n  @media (max-width: 768px) {\n    html {\n      font-size: 12px;\n    }\n  }\n`;\nexport default GlobalStyle;", "map": {"version": 3, "names": ["createGlobalStyle", "GlobalStyle"], "sources": ["D:/Test/Battle Launcher/src/styles/GlobalStyle.js"], "sourcesContent": ["import { createGlobalStyle } from 'styled-components';\n\nconst GlobalStyle = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html, body {\n    width: 100%;\n    height: 100%;\n    font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;\n    background: #1a1a1a;\n    color: #ffffff;\n    overflow: hidden;\n    user-select: none;\n    -webkit-user-select: none;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  #root {\n    width: 100%;\n    height: 100%;\n  }\n\n  /* 滚动条样式 */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: rgba(255, 107, 53, 0.6);\n    border-radius: 4px;\n    transition: background 0.3s ease;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: rgba(255, 107, 53, 0.8);\n  }\n\n  /* 按钮基础样式 */\n  button {\n    border: none;\n    outline: none;\n    cursor: pointer;\n    font-family: inherit;\n    transition: all 0.3s ease;\n  }\n\n  button:disabled {\n    cursor: not-allowed;\n    opacity: 0.6;\n  }\n\n  /* 输入框基础样式 */\n  input, textarea {\n    border: none;\n    outline: none;\n    font-family: inherit;\n    background: transparent;\n  }\n\n  /* 链接样式 */\n  a {\n    color: inherit;\n    text-decoration: none;\n  }\n\n  /* 选择文本样式 */\n  ::selection {\n    background: rgba(255, 107, 53, 0.3);\n    color: #ffffff;\n  }\n\n  /* 禁用拖拽 */\n  img, svg {\n    -webkit-user-drag: none;\n    user-drag: none;\n  }\n\n  /* 动画关键帧 */\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(20px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n\n  @keyframes slideInLeft {\n    from {\n      opacity: 0;\n      transform: translateX(-30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n\n  @keyframes slideInRight {\n    from {\n      opacity: 0;\n      transform: translateX(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n\n  @keyframes pulse {\n    0% {\n      transform: scale(1);\n    }\n    50% {\n      transform: scale(1.05);\n    }\n    100% {\n      transform: scale(1);\n    }\n  }\n\n  @keyframes spin {\n    from {\n      transform: rotate(0deg);\n    }\n    to {\n      transform: rotate(360deg);\n    }\n  }\n\n  /* 工具类 */\n  .fade-in {\n    animation: fadeIn 0.6s ease-out;\n  }\n\n  .slide-in-left {\n    animation: slideInLeft 0.6s ease-out;\n  }\n\n  .slide-in-right {\n    animation: slideInRight 0.6s ease-out;\n  }\n\n  .pulse {\n    animation: pulse 2s infinite;\n  }\n\n  .spin {\n    animation: spin 1s linear infinite;\n  }\n\n  /* 响应式断点 */\n  @media (max-width: 1024px) {\n    html {\n      font-size: 14px;\n    }\n  }\n\n  @media (max-width: 768px) {\n    html {\n      font-size: 12px;\n    }\n  }\n`;\n\nexport default GlobalStyle;"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mBAAmB;AAErD,MAAMC,WAAW,GAAGD,iBAAiB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}