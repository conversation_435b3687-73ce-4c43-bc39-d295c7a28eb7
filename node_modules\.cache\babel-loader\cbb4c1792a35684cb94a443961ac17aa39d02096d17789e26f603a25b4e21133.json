{"ast": null, "code": "import axios from 'axios';\n\n// 连接到真实后端API\nconst MOCK_MODE = false;\nconst API_BASE_URL = 'http://localhost:3004/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟用户数据\nconst mockUsers = [{\n  id: 1,\n  username: 'admin',\n  email: '<EMAIL>',\n  password: 'admin123',\n  role: 'admin',\n  permissions: ['all'],\n  authorizedModules: []\n}, {\n  id: 2,\n  username: 'demo',\n  email: '<EMAIL>',\n  password: 'demo123',\n  role: 'user',\n  permissions: [],\n  authorizedModules: []\n}];\n\n// 模拟模组类型数据\nconst mockModuleTypes = [{\n  id: 1,\n  name: '喷气发动机飞机',\n  code: 'aircraft-jet',\n  description: '现代喷气式战斗机和攻击机'\n}, {\n  id: 2,\n  name: '活塞发动机飞机',\n  code: 'aircraft-prop',\n  description: '二战时期的螺旋桨飞机'\n}, {\n  id: 3,\n  name: '地形',\n  code: 'terrain',\n  description: '游戏地图和地形'\n}, {\n  id: 4,\n  name: '战役',\n  code: 'campaign',\n  description: '单人战役任务'\n}];\n\n// 模拟所有用户数据（用于管理员管理）\nconst mockAllUsers = [...mockUsers];\n\n// 模拟API响应\nconst mockAPI = {\n  login: async credentials => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟\n    const user = mockUsers.find(u => (u.username === credentials.username || u.email === credentials.username) && u.password === credentials.password);\n    if (user) {\n      const token = 'mock_token_' + Date.now();\n      return {\n        success: true,\n        token,\n        user: {\n          id: user.id,\n          username: user.username,\n          email: user.email,\n          role: user.role,\n          permissions: user.permissions,\n          authorizedModules: user.authorizedModules\n        }\n      };\n    } else {\n      throw new Error('用户名或密码错误');\n    }\n  },\n  register: async userData => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockUsers.find(u => u.username === userData.username || u.email === userData.email);\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password,\n      role: 'user',\n      permissions: [],\n      authorizedModules: []\n    };\n    mockUsers.push(newUser);\n    mockAllUsers.push(newUser);\n    const token = 'mock_token_' + Date.now();\n    return {\n      success: true,\n      token,\n      user: {\n        id: newUser.id,\n        username: newUser.username,\n        email: newUser.email,\n        role: newUser.role,\n        permissions: newUser.permissions,\n        authorizedModules: newUser.authorizedModules\n      }\n    };\n  },\n  verifyToken: async token => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (token && token.startsWith('mock_token_')) {\n      return mockUsers[0]; // 返回默认用户\n    }\n    throw new Error('Invalid token');\n  },\n  // 管理员API - 获取所有用户\n  getAllUsers: async () => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    return mockAllUsers.map(user => ({\n      id: user.id,\n      username: user.username,\n      email: user.email,\n      role: user.role,\n      permissions: user.permissions,\n      authorizedModules: user.authorizedModules\n    }));\n  },\n  // 管理员API - 创建用户\n  createUser: async userData => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockAllUsers.find(u => u.username === userData.username || u.email === userData.email);\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockAllUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password || 'default123',\n      role: userData.role || 'user',\n      permissions: userData.permissions || [],\n      authorizedModules: userData.authorizedModules || []\n    };\n    mockAllUsers.push(newUser);\n    return {\n      id: newUser.id,\n      username: newUser.username,\n      email: newUser.email,\n      role: newUser.role,\n      permissions: newUser.permissions,\n      authorizedModules: newUser.authorizedModules\n    };\n  },\n  // 管理员API - 更新用户\n  updateUser: async (userId, userData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const userIndex = mockAllUsers.findIndex(u => u.id === userId);\n    if (userIndex === -1) {\n      throw new Error('用户不存在');\n    }\n    mockAllUsers[userIndex] = {\n      ...mockAllUsers[userIndex],\n      ...userData\n    };\n    return {\n      id: mockAllUsers[userIndex].id,\n      username: mockAllUsers[userIndex].username,\n      email: mockAllUsers[userIndex].email,\n      role: mockAllUsers[userIndex].role,\n      permissions: mockAllUsers[userIndex].permissions,\n      authorizedModules: mockAllUsers[userIndex].authorizedModules\n    };\n  },\n  // 管理员API - 删除用户\n  deleteUser: async userId => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const userIndex = mockAllUsers.findIndex(u => u.id === userId);\n    if (userIndex === -1) {\n      throw new Error('用户不存在');\n    }\n    if (mockAllUsers[userIndex].role === 'admin') {\n      throw new Error('不能删除管理员用户');\n    }\n    mockAllUsers.splice(userIndex, 1);\n    return {\n      success: true\n    };\n  },\n  // 管理员API - 获取模组类型\n  getModuleTypes: async () => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    return mockModuleTypes;\n  },\n  // 管理员API - 创建模组类型\n  createModuleType: async typeData => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const existingType = mockModuleTypes.find(t => t.code === typeData.code);\n    if (existingType) {\n      throw new Error('模组类型代码已存在');\n    }\n    const newType = {\n      id: mockModuleTypes.length + 1,\n      name: typeData.name,\n      code: typeData.code,\n      description: typeData.description\n    };\n    mockModuleTypes.push(newType);\n    return newType;\n  },\n  // 管理员API - 更新模组类型\n  updateModuleType: async (typeId, typeData) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);\n    if (typeIndex === -1) {\n      throw new Error('模组类型不存在');\n    }\n    mockModuleTypes[typeIndex] = {\n      ...mockModuleTypes[typeIndex],\n      ...typeData\n    };\n    return mockModuleTypes[typeIndex];\n  },\n  // 管理员API - 删除模组类型\n  deleteModuleType: async typeId => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);\n    if (typeIndex === -1) {\n      throw new Error('模组类型不存在');\n    }\n    mockModuleTypes.splice(typeIndex, 1);\n    return {\n      success: true\n    };\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token过期，清除本地存储并重定向到登录页\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      if (MOCK_MODE) {\n        const response = await mockAPI.login(credentials);\n        if (response.token) {\n          localStorage.setItem('dcs_token', response.token);\n        }\n        return response;\n      } else {\n        const response = await api.post('/auth/login', credentials);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 用户注册\n  async register(userData) {\n    try {\n      if (MOCK_MODE) {\n        const response = await mockAPI.register(userData);\n        if (response.token) {\n          localStorage.setItem('dcs_token', response.token);\n        }\n        return response;\n      } else {\n        const response = await api.post('/auth/register', userData);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 验证token\n  async verifyToken(token) {\n    try {\n      if (MOCK_MODE) {\n        const user = await mockAPI.verifyToken(token);\n        return user;\n      } else {\n        const response = await api.get('/auth/verify', {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', {\n        email\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const {\n        token\n      } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  },\n  // 管理员功能 - 获取所有用户\n  async getAllUsers() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAllUsers();\n      } else {\n        const response = await api.get('/admin/users');\n        return response.users;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 创建用户\n  async createUser(userData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.createUser(userData);\n      } else {\n        const response = await api.post('/admin/users', userData);\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 更新用户\n  async updateUser(userId, userData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateUser(userId, userData);\n      } else {\n        const response = await api.put(`/admin/users/${userId}`, userData);\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 删除用户\n  async deleteUser(userId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteUser(userId);\n      } else {\n        const response = await api.delete(`/admin/users/${userId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 获取模组类型\n  async getModuleTypes() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getModuleTypes();\n      } else {\n        const response = await api.get('/admin/module-types');\n        return response.types;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 创建模组类型\n  async createModuleType(typeData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.createModuleType(typeData);\n      } else {\n        const response = await api.post('/admin/module-types', typeData);\n        return response.type;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 更新模组类型\n  async updateModuleType(typeId, typeData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateModuleType(typeId, typeData);\n      } else {\n        const response = await api.put(`/admin/module-types/${typeId}`, typeData);\n        return response.type;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 管理员功能 - 删除模组类型\n  async deleteModuleType(typeId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteModuleType(typeId);\n      } else {\n        const response = await api.delete(`/admin/module-types/${typeId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "MOCK_MODE", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "mockUsers", "id", "username", "email", "password", "role", "permissions", "authorizedModules", "mockModuleTypes", "name", "code", "description", "mockAllUsers", "mockAPI", "login", "credentials", "Promise", "resolve", "setTimeout", "user", "find", "u", "token", "Date", "now", "success", "Error", "register", "userData", "existingUser", "newUser", "length", "push", "verifyToken", "startsWith", "getAllUsers", "map", "createUser", "updateUser", "userId", "userIndex", "findIndex", "deleteUser", "splice", "getModuleTypes", "createModuleType", "typeData", "existingType", "t", "newType", "updateModuleType", "typeId", "typeIndex", "deleteModuleType", "interceptors", "request", "use", "config", "localStorage", "getItem", "Authorization", "error", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "authService", "setItem", "post", "get", "getProfile", "updateProfile", "profileData", "put", "changePassword", "passwordData", "requestPasswordReset", "resetPassword", "resetData", "refreshToken", "logout", "users", "delete", "types", "type"], "sources": ["D:/Test/Battle Launcher/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\n// 连接到真实后端API\nconst MOCK_MODE = false;\nconst API_BASE_URL = 'http://localhost:3004/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟用户数据\nconst mockUsers = [\n  {\n    id: 1,\n    username: 'admin',\n    email: '<EMAIL>',\n    password: 'admin123',\n    role: 'admin',\n    permissions: ['all'],\n    authorizedModules: []\n  },\n  {\n    id: 2,\n    username: 'demo',\n    email: '<EMAIL>',\n    password: 'demo123',\n    role: 'user',\n    permissions: [],\n    authorizedModules: []\n  }\n];\n\n// 模拟模组类型数据\nconst mockModuleTypes = [\n  { id: 1, name: '喷气发动机飞机', code: 'aircraft-jet', description: '现代喷气式战斗机和攻击机' },\n  { id: 2, name: '活塞发动机飞机', code: 'aircraft-prop', description: '二战时期的螺旋桨飞机' },\n  { id: 3, name: '地形', code: 'terrain', description: '游戏地图和地形' },\n  { id: 4, name: '战役', code: 'campaign', description: '单人战役任务' }\n];\n\n// 模拟所有用户数据（用于管理员管理）\nconst mockAllUsers = [...mockUsers];\n\n// 模拟API响应\nconst mockAPI = {\n  login: async (credentials) => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟\n    const user = mockUsers.find(u => \n      (u.username === credentials.username || u.email === credentials.username) && \n      u.password === credentials.password\n    );\n    if (user) {\n      const token = 'mock_token_' + Date.now();\n      return {\n        success: true,\n        token,\n        user: { \n          id: user.id, \n          username: user.username, \n          email: user.email,\n          role: user.role,\n          permissions: user.permissions,\n          authorizedModules: user.authorizedModules\n        }\n      };\n    } else {\n      throw new Error('用户名或密码错误');\n    }\n  },\n  register: async (userData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockUsers.find(u => \n      u.username === userData.username || u.email === userData.email\n    );\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password,\n      role: 'user',\n      permissions: [],\n      authorizedModules: []\n    };\n    mockUsers.push(newUser);\n    mockAllUsers.push(newUser);\n    const token = 'mock_token_' + Date.now();\n    return {\n      success: true,\n      token,\n      user: { \n        id: newUser.id, \n        username: newUser.username, \n        email: newUser.email,\n        role: newUser.role,\n        permissions: newUser.permissions,\n        authorizedModules: newUser.authorizedModules\n      }\n    };\n  },\n  verifyToken: async (token) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (token && token.startsWith('mock_token_')) {\n      return mockUsers[0]; // 返回默认用户\n    }\n    throw new Error('Invalid token');\n  },\n\n  // 管理员API - 获取所有用户\n  getAllUsers: async () => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    return mockAllUsers.map(user => ({\n      id: user.id,\n      username: user.username,\n      email: user.email,\n      role: user.role,\n      permissions: user.permissions,\n      authorizedModules: user.authorizedModules\n    }));\n  },\n\n  // 管理员API - 创建用户\n  createUser: async (userData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockAllUsers.find(u => \n      u.username === userData.username || u.email === userData.email\n    );\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockAllUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password || 'default123',\n      role: userData.role || 'user',\n      permissions: userData.permissions || [],\n      authorizedModules: userData.authorizedModules || []\n    };\n    mockAllUsers.push(newUser);\n    return {\n      id: newUser.id,\n      username: newUser.username,\n      email: newUser.email,\n      role: newUser.role,\n      permissions: newUser.permissions,\n      authorizedModules: newUser.authorizedModules\n    };\n  },\n\n  // 管理员API - 更新用户\n  updateUser: async (userId, userData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const userIndex = mockAllUsers.findIndex(u => u.id === userId);\n    if (userIndex === -1) {\n      throw new Error('用户不存在');\n    }\n    mockAllUsers[userIndex] = { ...mockAllUsers[userIndex], ...userData };\n    return {\n      id: mockAllUsers[userIndex].id,\n      username: mockAllUsers[userIndex].username,\n      email: mockAllUsers[userIndex].email,\n      role: mockAllUsers[userIndex].role,\n      permissions: mockAllUsers[userIndex].permissions,\n      authorizedModules: mockAllUsers[userIndex].authorizedModules\n    };\n  },\n\n  // 管理员API - 删除用户\n  deleteUser: async (userId) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const userIndex = mockAllUsers.findIndex(u => u.id === userId);\n    if (userIndex === -1) {\n      throw new Error('用户不存在');\n    }\n    if (mockAllUsers[userIndex].role === 'admin') {\n      throw new Error('不能删除管理员用户');\n    }\n    mockAllUsers.splice(userIndex, 1);\n    return { success: true };\n  },\n\n  // 管理员API - 获取模组类型\n  getModuleTypes: async () => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    return mockModuleTypes;\n  },\n\n  // 管理员API - 创建模组类型\n  createModuleType: async (typeData) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const existingType = mockModuleTypes.find(t => t.code === typeData.code);\n    if (existingType) {\n      throw new Error('模组类型代码已存在');\n    }\n    const newType = {\n      id: mockModuleTypes.length + 1,\n      name: typeData.name,\n      code: typeData.code,\n      description: typeData.description\n    };\n    mockModuleTypes.push(newType);\n    return newType;\n  },\n\n  // 管理员API - 更新模组类型\n  updateModuleType: async (typeId, typeData) => {\n    await new Promise(resolve => setTimeout(resolve, 300));\n    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);\n    if (typeIndex === -1) {\n      throw new Error('模组类型不存在');\n    }\n    mockModuleTypes[typeIndex] = { ...mockModuleTypes[typeIndex], ...typeData };\n    return mockModuleTypes[typeIndex];\n  },\n\n  // 管理员API - 删除模组类型\n  deleteModuleType: async (typeId) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);\n    if (typeIndex === -1) {\n      throw new Error('模组类型不存在');\n    }\n    mockModuleTypes.splice(typeIndex, 1);\n    return { success: true };\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期，清除本地存储并重定向到登录页\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      if (MOCK_MODE) {\n        const response = await mockAPI.login(credentials);\n        if (response.token) {\n          localStorage.setItem('dcs_token', response.token);\n        }\n        return response;\n      } else {\n        const response = await api.post('/auth/login', credentials);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 用户注册\n  async register(userData) {\n    try {\n      if (MOCK_MODE) {\n        const response = await mockAPI.register(userData);\n        if (response.token) {\n          localStorage.setItem('dcs_token', response.token);\n        }\n        return response;\n      } else {\n        const response = await api.post('/auth/register', userData);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 验证token\n  async verifyToken(token) {\n    try {\n      if (MOCK_MODE) {\n        const user = await mockAPI.verifyToken(token);\n        return user;\n      } else {\n        const response = await api.get('/auth/verify', {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        });\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', { email });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const { token } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 获取所有用户\n  async getAllUsers() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getAllUsers();\n      } else {\n        const response = await api.get('/admin/users');\n        return response.users;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 创建用户\n  async createUser(userData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.createUser(userData);\n      } else {\n        const response = await api.post('/admin/users', userData);\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 更新用户\n  async updateUser(userId, userData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateUser(userId, userData);\n      } else {\n        const response = await api.put(`/admin/users/${userId}`, userData);\n        return response.user;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 删除用户\n  async deleteUser(userId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteUser(userId);\n      } else {\n        const response = await api.delete(`/admin/users/${userId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 获取模组类型\n  async getModuleTypes() {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.getModuleTypes();\n      } else {\n        const response = await api.get('/admin/module-types');\n        return response.types;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 创建模组类型\n  async createModuleType(typeData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.createModuleType(typeData);\n      } else {\n        const response = await api.post('/admin/module-types', typeData);\n        return response.type;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 更新模组类型\n  async updateModuleType(typeId, typeData) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.updateModuleType(typeId, typeData);\n      } else {\n        const response = await api.put(`/admin/module-types/${typeId}`, typeData);\n        return response.type;\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 管理员功能 - 删除模组类型\n  async deleteModuleType(typeId) {\n    try {\n      if (MOCK_MODE) {\n        return await mockAPI.deleteModuleType(typeId);\n      } else {\n        const response = await api.delete(`/admin/module-types/${typeId}`);\n        return response;\n      }\n    } catch (error) {\n      throw error;\n    }\n  }\n};\n\nexport default authService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,SAAS,GAAG,KAAK;AACvB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG,CAChB;EACEC,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,OAAO;EACjBC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,OAAO;EACbC,WAAW,EAAE,CAAC,KAAK,CAAC;EACpBC,iBAAiB,EAAE;AACrB,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE,SAAS;EACnBC,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,EAAE;EACfC,iBAAiB,EAAE;AACrB,CAAC,CACF;;AAED;AACA,MAAMC,eAAe,GAAG,CACtB;EAAEP,EAAE,EAAE,CAAC;EAAEQ,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE;AAAe,CAAC,EAC7E;EAAEV,EAAE,EAAE,CAAC;EAAEQ,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE,eAAe;EAAEC,WAAW,EAAE;AAAa,CAAC,EAC5E;EAAEV,EAAE,EAAE,CAAC;EAAEQ,IAAI,EAAE,IAAI;EAAEC,IAAI,EAAE,SAAS;EAAEC,WAAW,EAAE;AAAU,CAAC,EAC9D;EAAEV,EAAE,EAAE,CAAC;EAAEQ,IAAI,EAAE,IAAI;EAAEC,IAAI,EAAE,UAAU;EAAEC,WAAW,EAAE;AAAS,CAAC,CAC/D;;AAED;AACA,MAAMC,YAAY,GAAG,CAAC,GAAGZ,SAAS,CAAC;;AAEnC;AACA,MAAMa,OAAO,GAAG;EACdC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACxD,MAAME,IAAI,GAAGnB,SAAS,CAACoB,IAAI,CAACC,CAAC,IAC3B,CAACA,CAAC,CAACnB,QAAQ,KAAKa,WAAW,CAACb,QAAQ,IAAImB,CAAC,CAAClB,KAAK,KAAKY,WAAW,CAACb,QAAQ,KACxEmB,CAAC,CAACjB,QAAQ,KAAKW,WAAW,CAACX,QAC7B,CAAC;IACD,IAAIe,IAAI,EAAE;MACR,MAAMG,KAAK,GAAG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACxC,OAAO;QACLC,OAAO,EAAE,IAAI;QACbH,KAAK;QACLH,IAAI,EAAE;UACJlB,EAAE,EAAEkB,IAAI,CAAClB,EAAE;UACXC,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;UACvBC,KAAK,EAAEgB,IAAI,CAAChB,KAAK;UACjBE,IAAI,EAAEc,IAAI,CAACd,IAAI;UACfC,WAAW,EAAEa,IAAI,CAACb,WAAW;UAC7BC,iBAAiB,EAAEY,IAAI,CAACZ;QAC1B;MACF,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAImB,KAAK,CAAC,UAAU,CAAC;IAC7B;EACF,CAAC;EACDC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMY,YAAY,GAAG7B,SAAS,CAACoB,IAAI,CAACC,CAAC,IACnCA,CAAC,CAACnB,QAAQ,KAAK0B,QAAQ,CAAC1B,QAAQ,IAAImB,CAAC,CAAClB,KAAK,KAAKyB,QAAQ,CAACzB,KAC3D,CAAC;IACD,IAAI0B,YAAY,EAAE;MAChB,MAAM,IAAIH,KAAK,CAAC,WAAW,CAAC;IAC9B;IACA,MAAMI,OAAO,GAAG;MACd7B,EAAE,EAAED,SAAS,CAAC+B,MAAM,GAAG,CAAC;MACxB7B,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;MAC3BC,KAAK,EAAEyB,QAAQ,CAACzB,KAAK;MACrBC,QAAQ,EAAEwB,QAAQ,CAACxB,QAAQ;MAC3BC,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE;IACrB,CAAC;IACDP,SAAS,CAACgC,IAAI,CAACF,OAAO,CAAC;IACvBlB,YAAY,CAACoB,IAAI,CAACF,OAAO,CAAC;IAC1B,MAAMR,KAAK,GAAG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACxC,OAAO;MACLC,OAAO,EAAE,IAAI;MACbH,KAAK;MACLH,IAAI,EAAE;QACJlB,EAAE,EAAE6B,OAAO,CAAC7B,EAAE;QACdC,QAAQ,EAAE4B,OAAO,CAAC5B,QAAQ;QAC1BC,KAAK,EAAE2B,OAAO,CAAC3B,KAAK;QACpBE,IAAI,EAAEyB,OAAO,CAACzB,IAAI;QAClBC,WAAW,EAAEwB,OAAO,CAACxB,WAAW;QAChCC,iBAAiB,EAAEuB,OAAO,CAACvB;MAC7B;IACF,CAAC;EACH,CAAC;EACD0B,WAAW,EAAE,MAAOX,KAAK,IAAK;IAC5B,MAAM,IAAIN,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,IAAIK,KAAK,IAAIA,KAAK,CAACY,UAAU,CAAC,aAAa,CAAC,EAAE;MAC5C,OAAOlC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,MAAM,IAAI0B,KAAK,CAAC,eAAe,CAAC;EAClC,CAAC;EAED;EACAS,WAAW,EAAE,MAAAA,CAAA,KAAY;IACvB,MAAM,IAAInB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOL,YAAY,CAACwB,GAAG,CAACjB,IAAI,KAAK;MAC/BlB,EAAE,EAAEkB,IAAI,CAAClB,EAAE;MACXC,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ;MACvBC,KAAK,EAAEgB,IAAI,CAAChB,KAAK;MACjBE,IAAI,EAAEc,IAAI,CAACd,IAAI;MACfC,WAAW,EAAEa,IAAI,CAACb,WAAW;MAC7BC,iBAAiB,EAAEY,IAAI,CAACZ;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED;EACA8B,UAAU,EAAE,MAAOT,QAAQ,IAAK;IAC9B,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMY,YAAY,GAAGjB,YAAY,CAACQ,IAAI,CAACC,CAAC,IACtCA,CAAC,CAACnB,QAAQ,KAAK0B,QAAQ,CAAC1B,QAAQ,IAAImB,CAAC,CAAClB,KAAK,KAAKyB,QAAQ,CAACzB,KAC3D,CAAC;IACD,IAAI0B,YAAY,EAAE;MAChB,MAAM,IAAIH,KAAK,CAAC,WAAW,CAAC;IAC9B;IACA,MAAMI,OAAO,GAAG;MACd7B,EAAE,EAAEW,YAAY,CAACmB,MAAM,GAAG,CAAC;MAC3B7B,QAAQ,EAAE0B,QAAQ,CAAC1B,QAAQ;MAC3BC,KAAK,EAAEyB,QAAQ,CAACzB,KAAK;MACrBC,QAAQ,EAAEwB,QAAQ,CAACxB,QAAQ,IAAI,YAAY;MAC3CC,IAAI,EAAEuB,QAAQ,CAACvB,IAAI,IAAI,MAAM;MAC7BC,WAAW,EAAEsB,QAAQ,CAACtB,WAAW,IAAI,EAAE;MACvCC,iBAAiB,EAAEqB,QAAQ,CAACrB,iBAAiB,IAAI;IACnD,CAAC;IACDK,YAAY,CAACoB,IAAI,CAACF,OAAO,CAAC;IAC1B,OAAO;MACL7B,EAAE,EAAE6B,OAAO,CAAC7B,EAAE;MACdC,QAAQ,EAAE4B,OAAO,CAAC5B,QAAQ;MAC1BC,KAAK,EAAE2B,OAAO,CAAC3B,KAAK;MACpBE,IAAI,EAAEyB,OAAO,CAACzB,IAAI;MAClBC,WAAW,EAAEwB,OAAO,CAACxB,WAAW;MAChCC,iBAAiB,EAAEuB,OAAO,CAACvB;IAC7B,CAAC;EACH,CAAC;EAED;EACA+B,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEX,QAAQ,KAAK;IACtC,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMuB,SAAS,GAAG5B,YAAY,CAAC6B,SAAS,CAACpB,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKsC,MAAM,CAAC;IAC9D,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAId,KAAK,CAAC,OAAO,CAAC;IAC1B;IACAd,YAAY,CAAC4B,SAAS,CAAC,GAAG;MAAE,GAAG5B,YAAY,CAAC4B,SAAS,CAAC;MAAE,GAAGZ;IAAS,CAAC;IACrE,OAAO;MACL3B,EAAE,EAAEW,YAAY,CAAC4B,SAAS,CAAC,CAACvC,EAAE;MAC9BC,QAAQ,EAAEU,YAAY,CAAC4B,SAAS,CAAC,CAACtC,QAAQ;MAC1CC,KAAK,EAAES,YAAY,CAAC4B,SAAS,CAAC,CAACrC,KAAK;MACpCE,IAAI,EAAEO,YAAY,CAAC4B,SAAS,CAAC,CAACnC,IAAI;MAClCC,WAAW,EAAEM,YAAY,CAAC4B,SAAS,CAAC,CAAClC,WAAW;MAChDC,iBAAiB,EAAEK,YAAY,CAAC4B,SAAS,CAAC,CAACjC;IAC7C,CAAC;EACH,CAAC;EAED;EACAmC,UAAU,EAAE,MAAOH,MAAM,IAAK;IAC5B,MAAM,IAAIvB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMuB,SAAS,GAAG5B,YAAY,CAAC6B,SAAS,CAACpB,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKsC,MAAM,CAAC;IAC9D,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAId,KAAK,CAAC,OAAO,CAAC;IAC1B;IACA,IAAId,YAAY,CAAC4B,SAAS,CAAC,CAACnC,IAAI,KAAK,OAAO,EAAE;MAC5C,MAAM,IAAIqB,KAAK,CAAC,WAAW,CAAC;IAC9B;IACAd,YAAY,CAAC+B,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;IACjC,OAAO;MAAEf,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC;EAED;EACAmB,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,OAAOT,eAAe;EACxB,CAAC;EAED;EACAqC,gBAAgB,EAAE,MAAOC,QAAQ,IAAK;IACpC,MAAM,IAAI9B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAM8B,YAAY,GAAGvC,eAAe,CAACY,IAAI,CAAC4B,CAAC,IAAIA,CAAC,CAACtC,IAAI,KAAKoC,QAAQ,CAACpC,IAAI,CAAC;IACxE,IAAIqC,YAAY,EAAE;MAChB,MAAM,IAAIrB,KAAK,CAAC,WAAW,CAAC;IAC9B;IACA,MAAMuB,OAAO,GAAG;MACdhD,EAAE,EAAEO,eAAe,CAACuB,MAAM,GAAG,CAAC;MAC9BtB,IAAI,EAAEqC,QAAQ,CAACrC,IAAI;MACnBC,IAAI,EAAEoC,QAAQ,CAACpC,IAAI;MACnBC,WAAW,EAAEmC,QAAQ,CAACnC;IACxB,CAAC;IACDH,eAAe,CAACwB,IAAI,CAACiB,OAAO,CAAC;IAC7B,OAAOA,OAAO;EAChB,CAAC;EAED;EACAC,gBAAgB,EAAE,MAAAA,CAAOC,MAAM,EAAEL,QAAQ,KAAK;IAC5C,MAAM,IAAI9B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMmC,SAAS,GAAG5C,eAAe,CAACiC,SAAS,CAACO,CAAC,IAAIA,CAAC,CAAC/C,EAAE,KAAKkD,MAAM,CAAC;IACjE,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAI1B,KAAK,CAAC,SAAS,CAAC;IAC5B;IACAlB,eAAe,CAAC4C,SAAS,CAAC,GAAG;MAAE,GAAG5C,eAAe,CAAC4C,SAAS,CAAC;MAAE,GAAGN;IAAS,CAAC;IAC3E,OAAOtC,eAAe,CAAC4C,SAAS,CAAC;EACnC,CAAC;EAED;EACAC,gBAAgB,EAAE,MAAOF,MAAM,IAAK;IAClC,MAAM,IAAInC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMmC,SAAS,GAAG5C,eAAe,CAACiC,SAAS,CAACO,CAAC,IAAIA,CAAC,CAAC/C,EAAE,KAAKkD,MAAM,CAAC;IACjE,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,MAAM,IAAI1B,KAAK,CAAC,SAAS,CAAC;IAC5B;IACAlB,eAAe,CAACmC,MAAM,CAACS,SAAS,EAAE,CAAC,CAAC;IACpC,OAAO;MAAE3B,OAAO,EAAE;IAAK,CAAC;EAC1B;AACF,CAAC;;AAED;AACA9B,GAAG,CAAC2D,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMnC,KAAK,GAAGoC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIrC,KAAK,EAAE;IACTmC,MAAM,CAAC1D,OAAO,CAAC6D,aAAa,GAAG,UAAUtC,KAAK,EAAE;EAClD;EACA,OAAOmC,MAAM;AACf,CAAC,EACAI,KAAK,IAAK;EACT,OAAO7C,OAAO,CAAC8C,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAlE,GAAG,CAAC2D,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC1BO,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACE,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOtD,OAAO,CAAC8C,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMU,WAAW,GAAG;EAClB;EACA,MAAMzD,KAAKA,CAACC,WAAW,EAAE;IACvB,IAAI;MACF,IAAItB,SAAS,EAAE;QACb,MAAMsE,QAAQ,GAAG,MAAMlD,OAAO,CAACC,KAAK,CAACC,WAAW,CAAC;QACjD,IAAIgD,QAAQ,CAACzC,KAAK,EAAE;UAClBoC,YAAY,CAACc,OAAO,CAAC,WAAW,EAAET,QAAQ,CAACzC,KAAK,CAAC;QACnD;QACA,OAAOyC,QAAQ;MACjB,CAAC,MAAM;QACL,MAAMA,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,aAAa,EAAE1D,WAAW,CAAC;QAC3D,OAAOgD,QAAQ;MACjB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMlC,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,MAAMsE,QAAQ,GAAG,MAAMlD,OAAO,CAACc,QAAQ,CAACC,QAAQ,CAAC;QACjD,IAAImC,QAAQ,CAACzC,KAAK,EAAE;UAClBoC,YAAY,CAACc,OAAO,CAAC,WAAW,EAAET,QAAQ,CAACzC,KAAK,CAAC;QACnD;QACA,OAAOyC,QAAQ;MACjB,CAAC,MAAM;QACL,MAAMA,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,gBAAgB,EAAE7C,QAAQ,CAAC;QAC3D,OAAOmC,QAAQ;MACjB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM5B,WAAWA,CAACX,KAAK,EAAE;IACvB,IAAI;MACF,IAAI7B,SAAS,EAAE;QACb,MAAM0B,IAAI,GAAG,MAAMN,OAAO,CAACoB,WAAW,CAACX,KAAK,CAAC;QAC7C,OAAOH,IAAI;MACb,CAAC,MAAM;QACL,MAAM4C,QAAQ,GAAG,MAAMpE,GAAG,CAAC+E,GAAG,CAAC,cAAc,EAAE;UAC7C3E,OAAO,EAAE;YACP6D,aAAa,EAAE,UAAUtC,KAAK;UAChC;QACF,CAAC,CAAC;QACF,OAAOyC,QAAQ,CAAC5C,IAAI;MACtB;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMc,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMpE,GAAG,CAAC+E,GAAG,CAAC,eAAe,CAAC;MAC/C,OAAOX,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMe,aAAaA,CAACC,WAAW,EAAE;IAC/B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMpE,GAAG,CAACmF,GAAG,CAAC,eAAe,EAAED,WAAW,CAAC;MAC5D,OAAOd,QAAQ,CAAC5C,IAAI;IACtB,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMkB,cAAcA,CAACC,YAAY,EAAE;IACjC,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMpE,GAAG,CAACmF,GAAG,CAAC,uBAAuB,EAAEE,YAAY,CAAC;MACrE,OAAOjB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMoB,oBAAoBA,CAAC9E,KAAK,EAAE;IAChC,IAAI;MACF,MAAM4D,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,8BAA8B,EAAE;QAAEtE;MAAM,CAAC,CAAC;MAC1E,OAAO4D,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMqB,aAAaA,CAACC,SAAS,EAAE;IAC7B,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,sBAAsB,EAAEU,SAAS,CAAC;MAClE,OAAOpB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMuB,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,eAAe,CAAC;MAChD,MAAM;QAAEnD;MAAM,CAAC,GAAGyC,QAAQ;MAC1BL,YAAY,CAACc,OAAO,CAAC,WAAW,EAAElD,KAAK,CAAC;MACxC,OAAOyC,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,MAAM1F,GAAG,CAAC8E,IAAI,CAAC,cAAc,CAAC;MAC9Bf,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC,CAAC,OAAON,KAAK,EAAE;MACd;MACAH,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;MACpC,MAAMN,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM1B,WAAWA,CAAA,EAAG;IAClB,IAAI;MACF,IAAI1C,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACsB,WAAW,CAAC,CAAC;MACpC,CAAC,MAAM;QACL,MAAM4B,QAAQ,GAAG,MAAMpE,GAAG,CAAC+E,GAAG,CAAC,cAAc,CAAC;QAC9C,OAAOX,QAAQ,CAACuB,KAAK;MACvB;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMxB,UAAUA,CAACT,QAAQ,EAAE;IACzB,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACwB,UAAU,CAACT,QAAQ,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMmC,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,cAAc,EAAE7C,QAAQ,CAAC;QACzD,OAAOmC,QAAQ,CAAC5C,IAAI;MACtB;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMvB,UAAUA,CAACC,MAAM,EAAEX,QAAQ,EAAE;IACjC,IAAI;MACF,IAAInC,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACyB,UAAU,CAACC,MAAM,EAAEX,QAAQ,CAAC;MACnD,CAAC,MAAM;QACL,MAAMmC,QAAQ,GAAG,MAAMpE,GAAG,CAACmF,GAAG,CAAC,gBAAgBvC,MAAM,EAAE,EAAEX,QAAQ,CAAC;QAClE,OAAOmC,QAAQ,CAAC5C,IAAI;MACtB;IACF,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMnB,UAAUA,CAACH,MAAM,EAAE;IACvB,IAAI;MACF,IAAI9C,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAAC6B,UAAU,CAACH,MAAM,CAAC;MACzC,CAAC,MAAM;QACL,MAAMwB,QAAQ,GAAG,MAAMpE,GAAG,CAAC4F,MAAM,CAAC,gBAAgBhD,MAAM,EAAE,CAAC;QAC3D,OAAOwB,QAAQ;MACjB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMjB,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,IAAInD,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAAC+B,cAAc,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,MAAMmB,QAAQ,GAAG,MAAMpE,GAAG,CAAC+E,GAAG,CAAC,qBAAqB,CAAC;QACrD,OAAOX,QAAQ,CAACyB,KAAK;MACvB;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMhB,gBAAgBA,CAACC,QAAQ,EAAE;IAC/B,IAAI;MACF,IAAIrD,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACgC,gBAAgB,CAACC,QAAQ,CAAC;MACjD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAMpE,GAAG,CAAC8E,IAAI,CAAC,qBAAqB,EAAE3B,QAAQ,CAAC;QAChE,OAAOiB,QAAQ,CAAC0B,IAAI;MACtB;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMX,gBAAgBA,CAACC,MAAM,EAAEL,QAAQ,EAAE;IACvC,IAAI;MACF,IAAIrD,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACqC,gBAAgB,CAACC,MAAM,EAAEL,QAAQ,CAAC;MACzD,CAAC,MAAM;QACL,MAAMiB,QAAQ,GAAG,MAAMpE,GAAG,CAACmF,GAAG,CAAC,uBAAuB3B,MAAM,EAAE,EAAEL,QAAQ,CAAC;QACzE,OAAOiB,QAAQ,CAAC0B,IAAI;MACtB;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMR,gBAAgBA,CAACF,MAAM,EAAE;IAC7B,IAAI;MACF,IAAI1D,SAAS,EAAE;QACb,OAAO,MAAMoB,OAAO,CAACwC,gBAAgB,CAACF,MAAM,CAAC;MAC/C,CAAC,MAAM;QACL,MAAMY,QAAQ,GAAG,MAAMpE,GAAG,CAAC4F,MAAM,CAAC,uBAAuBpC,MAAM,EAAE,CAAC;QAClE,OAAOY,QAAQ;MACjB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeU,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}