{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:3001/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token过期，清除本地存储并重定向到登录页\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      const response = await api.post('/auth/login', credentials);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 用户注册\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 验证token\n  async verifyToken(token) {\n    try {\n      const response = await api.get('/auth/verify', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', {\n        email\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const {\n        token\n      } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "authService", "login", "credentials", "post", "register", "userData", "verifyToken", "get", "user", "getProfile", "updateProfile", "profileData", "put", "changePassword", "passwordData", "requestPasswordReset", "email", "resetPassword", "resetData", "refreshToken", "setItem", "logout"], "sources": ["D:/Test/Battle Launcher/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:3001/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期，清除本地存储并重定向到登录页\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      const response = await api.post('/auth/login', credentials);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 用户注册\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 验证token\n  async verifyToken(token) {\n    try {\n      const response = await api.get('/auth/verify', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', { email });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const { token } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  }\n};\n\nexport default authService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAT,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOT,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMW,WAAW,GAAG;EAClB;EACA,MAAMC,KAAKA,CAACC,WAAW,EAAE;IACvB,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;MAC3D,OAAOV,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMe,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;MAC3D,OAAOb,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMiB,WAAWA,CAACrB,KAAK,EAAE;IACvB,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMhB,GAAG,CAAC+B,GAAG,CAAC,cAAc,EAAE;QAC7C3B,OAAO,EAAE;UACPQ,aAAa,EAAE,UAAUH,KAAK;QAChC;MACF,CAAC,CAAC;MACF,OAAOO,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMoB,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAMhB,GAAG,CAAC+B,GAAG,CAAC,eAAe,CAAC;MAC/C,OAAOf,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMqB,aAAaA,CAACC,WAAW,EAAE;IAC/B,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMhB,GAAG,CAACoC,GAAG,CAAC,eAAe,EAAED,WAAW,CAAC;MAC5D,OAAOnB,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,cAAcA,CAACC,YAAY,EAAE;IACjC,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMhB,GAAG,CAACoC,GAAG,CAAC,uBAAuB,EAAEE,YAAY,CAAC;MACrE,OAAOtB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM0B,oBAAoBA,CAACC,KAAK,EAAE;IAChC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,IAAI,CAAC,8BAA8B,EAAE;QAAEa;MAAM,CAAC,CAAC;MAC1E,OAAOxB,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM4B,aAAaA,CAACC,SAAS,EAAE;IAC7B,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,IAAI,CAAC,sBAAsB,EAAEe,SAAS,CAAC;MAClE,OAAO1B,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAM8B,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAM3B,QAAQ,GAAG,MAAMhB,GAAG,CAAC2B,IAAI,CAAC,eAAe,CAAC;MAChD,MAAM;QAAElB;MAAM,CAAC,GAAGO,QAAQ;MAC1BN,YAAY,CAACkC,OAAO,CAAC,WAAW,EAAEnC,KAAK,CAAC;MACxC,OAAOO,QAAQ;IACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMgC,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,MAAM7C,GAAG,CAAC2B,IAAI,CAAC,cAAc,CAAC;MAC9BjB,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd;MACAH,YAAY,CAACU,UAAU,CAAC,WAAW,CAAC;MACpC,MAAMP,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeW,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}