{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport App from './App';\nimport { AuthProvider } from './context/AuthContext';\nimport { GameResourceProvider } from './context/GameResourceContext';\nimport { ThemeProvider } from 'styled-components';\nimport GlobalStyle from './styles/GlobalStyle';\nimport theme from './styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(GlobalStyle, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(GameResourceProvider, {\n          children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 14,\n  columnNumber: 3\n}, this));\n\n// 移除加载动画\nsetTimeout(() => {\n  const loading = document.querySelector('.loading');\n  if (loading) {\n    loading.style.opacity = '0';\n    loading.style.transition = 'opacity 0.5s ease';\n    setTimeout(() => {\n      loading.remove();\n    }, 500);\n  }\n}, 1000);", "map": {"version": 3, "names": ["React", "ReactDOM", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "<PERSON>th<PERSON><PERSON><PERSON>", "GameResourceProvider", "ThemeProvider", "GlobalStyle", "theme", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "setTimeout", "loading", "querySelector", "style", "opacity", "transition", "remove"], "sources": ["D:/Test/Battle Launcher/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { BrowserRouter } from 'react-router-dom';\nimport App from './App';\nimport { AuthProvider } from './context/AuthContext';\nimport { GameResourceProvider } from './context/GameResourceContext';\nimport { ThemeProvider } from 'styled-components';\nimport GlobalStyle from './styles/GlobalStyle';\nimport theme from './styles/theme';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\n\nroot.render(\n  <React.StrictMode>\n    <BrowserRouter>\n      <ThemeProvider theme={theme}>\n        <GlobalStyle />\n        <AuthProvider>\n          <GameResourceProvider>\n            <App />\n          </GameResourceProvider>\n        </AuthProvider>\n      </ThemeProvider>\n    </BrowserRouter>\n  </React.StrictMode>\n);\n\n// 移除加载动画\nsetTimeout(() => {\n  const loading = document.querySelector('.loading');\n  if (loading) {\n    loading.style.opacity = '0';\n    loading.style.transition = 'opacity 0.5s ease';\n    setTimeout(() => {\n      loading.remove();\n    }, 500);\n  }\n}, 1000);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,KAAK,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,IAAI,GAAGV,QAAQ,CAACW,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AAEjEH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACV,KAAK,CAACgB,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACR,aAAa;IAAAe,QAAA,eACZP,OAAA,CAACJ,aAAa;MAACE,KAAK,EAAEA,KAAM;MAAAS,QAAA,gBAC1BP,OAAA,CAACH,WAAW;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfX,OAAA,CAACN,YAAY;QAAAa,QAAA,eACXP,OAAA,CAACL,oBAAoB;UAAAY,QAAA,eACnBP,OAAA,CAACP,GAAG;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CACpB,CAAC;;AAED;AACAC,UAAU,CAAC,MAAM;EACf,MAAMC,OAAO,GAAGV,QAAQ,CAACW,aAAa,CAAC,UAAU,CAAC;EAClD,IAAID,OAAO,EAAE;IACXA,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,GAAG;IAC3BH,OAAO,CAACE,KAAK,CAACE,UAAU,GAAG,mBAAmB;IAC9CL,UAAU,CAAC,MAAM;MACfC,OAAO,CAACK,MAAM,CAAC,CAAC;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;AACF,CAAC,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}