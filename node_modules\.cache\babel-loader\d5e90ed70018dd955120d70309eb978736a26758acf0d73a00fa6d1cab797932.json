{"ast": null, "code": "import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟用户数据\nconst mockUsers = [{\n  id: 1,\n  username: 'demo',\n  email: '<EMAIL>',\n  password: 'demo123'\n}];\n\n// 模拟API响应\nconst mockAPI = {\n  login: async credentials => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟\n    const user = mockUsers.find(u => (u.username === credentials.username || u.email === credentials.username) && u.password === credentials.password);\n    if (user) {\n      const token = 'mock_token_' + Date.now();\n      return {\n        success: true,\n        token,\n        user: {\n          id: user.id,\n          username: user.username,\n          email: user.email\n        }\n      };\n    } else {\n      throw new Error('用户名或密码错误');\n    }\n  },\n  register: async userData => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockUsers.find(u => u.username === userData.username || u.email === userData.email);\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password\n    };\n    mockUsers.push(newUser);\n    const token = 'mock_token_' + Date.now();\n    return {\n      success: true,\n      token,\n      user: {\n        id: newUser.id,\n        username: newUser.username,\n        email: newUser.email\n      }\n    };\n  },\n  verifyToken: async token => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (token && token.startsWith('mock_token_')) {\n      return mockUsers[0]; // 返回默认用户\n    }\n    throw new Error('Invalid token');\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('dcs_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token过期，清除本地存储并重定向到登录页\n    localStorage.removeItem('dcs_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      const response = await api.post('/auth/login', credentials);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 用户注册\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 验证token\n  async verifyToken(token) {\n    try {\n      const response = await api.get('/auth/verify', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', {\n        email\n      });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const {\n        token\n      } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "MOCK_MODE", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "mockUsers", "id", "username", "email", "password", "mockAPI", "login", "credentials", "Promise", "resolve", "setTimeout", "user", "find", "u", "token", "Date", "now", "success", "Error", "register", "userData", "existingUser", "newUser", "length", "push", "verifyToken", "startsWith", "interceptors", "request", "use", "config", "localStorage", "getItem", "Authorization", "error", "reject", "response", "data", "_error$response", "status", "removeItem", "window", "location", "href", "authService", "post", "get", "getProfile", "updateProfile", "profileData", "put", "changePassword", "passwordData", "requestPasswordReset", "resetPassword", "resetData", "refreshToken", "setItem", "logout"], "sources": ["D:/Test/Battle Launcher/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\n// 模拟API - 用于演示目的\nconst MOCK_MODE = true;\nconst API_BASE_URL = 'http://localhost:3000/api';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 模拟用户数据\nconst mockUsers = [\n  {\n    id: 1,\n    username: 'demo',\n    email: '<EMAIL>',\n    password: 'demo123'\n  }\n];\n\n// 模拟API响应\nconst mockAPI = {\n  login: async (credentials) => {\n    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟\n    const user = mockUsers.find(u => \n      (u.username === credentials.username || u.email === credentials.username) && \n      u.password === credentials.password\n    );\n    if (user) {\n      const token = 'mock_token_' + Date.now();\n      return {\n        success: true,\n        token,\n        user: { id: user.id, username: user.username, email: user.email }\n      };\n    } else {\n      throw new Error('用户名或密码错误');\n    }\n  },\n  register: async (userData) => {\n    await new Promise(resolve => setTimeout(resolve, 500));\n    const existingUser = mockUsers.find(u => \n      u.username === userData.username || u.email === userData.email\n    );\n    if (existingUser) {\n      throw new Error('用户名或邮箱已存在');\n    }\n    const newUser = {\n      id: mockUsers.length + 1,\n      username: userData.username,\n      email: userData.email,\n      password: userData.password\n    };\n    mockUsers.push(newUser);\n    const token = 'mock_token_' + Date.now();\n    return {\n      success: true,\n      token,\n      user: { id: newUser.id, username: newUser.username, email: newUser.email }\n    };\n  },\n  verifyToken: async (token) => {\n    await new Promise(resolve => setTimeout(resolve, 200));\n    if (token && token.startsWith('mock_token_')) {\n      return mockUsers[0]; // 返回默认用户\n    }\n    throw new Error('Invalid token');\n  }\n};\n\n// 请求拦截器 - 添加token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('dcs_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理token过期\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token过期，清除本地存储并重定向到登录页\n      localStorage.removeItem('dcs_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // 用户登录\n  async login(credentials) {\n    try {\n      const response = await api.post('/auth/login', credentials);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 用户注册\n  async register(userData) {\n    try {\n      const response = await api.post('/auth/register', userData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 验证token\n  async verifyToken(token) {\n    try {\n      const response = await api.get('/auth/verify', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取用户资料\n  async getProfile() {\n    try {\n      const response = await api.get('/auth/profile');\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新用户资料\n  async updateProfile(profileData) {\n    try {\n      const response = await api.put('/auth/profile', profileData);\n      return response.user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 修改密码\n  async changePassword(passwordData) {\n    try {\n      const response = await api.put('/auth/change-password', passwordData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码请求\n  async requestPasswordReset(email) {\n    try {\n      const response = await api.post('/auth/reset-password-request', { email });\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 重置密码\n  async resetPassword(resetData) {\n    try {\n      const response = await api.post('/auth/reset-password', resetData);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 刷新token\n  async refreshToken() {\n    try {\n      const response = await api.post('/auth/refresh');\n      const { token } = response;\n      localStorage.setItem('dcs_token', token);\n      return response;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 登出\n  async logout() {\n    try {\n      await api.post('/auth/logout');\n      localStorage.removeItem('dcs_token');\n    } catch (error) {\n      // 即使请求失败也要清除本地token\n      localStorage.removeItem('dcs_token');\n      throw error;\n    }\n  }\n};\n\nexport default authService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,SAAS,GAAG,IAAI;AACtB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG,CAChB;EACEC,EAAE,EAAE,CAAC;EACLC,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE,kBAAkB;EACzBC,QAAQ,EAAE;AACZ,CAAC,CACF;;AAED;AACA,MAAMC,OAAO,GAAG;EACdC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACxD,MAAME,IAAI,GAAGX,SAAS,CAACY,IAAI,CAACC,CAAC,IAC3B,CAACA,CAAC,CAACX,QAAQ,KAAKK,WAAW,CAACL,QAAQ,IAAIW,CAAC,CAACV,KAAK,KAAKI,WAAW,CAACL,QAAQ,KACxEW,CAAC,CAACT,QAAQ,KAAKG,WAAW,CAACH,QAC7B,CAAC;IACD,IAAIO,IAAI,EAAE;MACR,MAAMG,KAAK,GAAG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACxC,OAAO;QACLC,OAAO,EAAE,IAAI;QACbH,KAAK;QACLH,IAAI,EAAE;UAAEV,EAAE,EAAEU,IAAI,CAACV,EAAE;UAAEC,QAAQ,EAAES,IAAI,CAACT,QAAQ;UAAEC,KAAK,EAAEQ,IAAI,CAACR;QAAM;MAClE,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIe,KAAK,CAAC,UAAU,CAAC;IAC7B;EACF,CAAC;EACDC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,MAAMY,YAAY,GAAGrB,SAAS,CAACY,IAAI,CAACC,CAAC,IACnCA,CAAC,CAACX,QAAQ,KAAKkB,QAAQ,CAAClB,QAAQ,IAAIW,CAAC,CAACV,KAAK,KAAKiB,QAAQ,CAACjB,KAC3D,CAAC;IACD,IAAIkB,YAAY,EAAE;MAChB,MAAM,IAAIH,KAAK,CAAC,WAAW,CAAC;IAC9B;IACA,MAAMI,OAAO,GAAG;MACdrB,EAAE,EAAED,SAAS,CAACuB,MAAM,GAAG,CAAC;MACxBrB,QAAQ,EAAEkB,QAAQ,CAAClB,QAAQ;MAC3BC,KAAK,EAAEiB,QAAQ,CAACjB,KAAK;MACrBC,QAAQ,EAAEgB,QAAQ,CAAChB;IACrB,CAAC;IACDJ,SAAS,CAACwB,IAAI,CAACF,OAAO,CAAC;IACvB,MAAMR,KAAK,GAAG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACxC,OAAO;MACLC,OAAO,EAAE,IAAI;MACbH,KAAK;MACLH,IAAI,EAAE;QAAEV,EAAE,EAAEqB,OAAO,CAACrB,EAAE;QAAEC,QAAQ,EAAEoB,OAAO,CAACpB,QAAQ;QAAEC,KAAK,EAAEmB,OAAO,CAACnB;MAAM;IAC3E,CAAC;EACH,CAAC;EACDsB,WAAW,EAAE,MAAOX,KAAK,IAAK;IAC5B,MAAM,IAAIN,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACtD,IAAIK,KAAK,IAAIA,KAAK,CAACY,UAAU,CAAC,aAAa,CAAC,EAAE;MAC5C,OAAO1B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,MAAM,IAAIkB,KAAK,CAAC,eAAe,CAAC;EAClC;AACF,CAAC;;AAED;AACAvB,GAAG,CAACgC,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMhB,KAAK,GAAGiB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIlB,KAAK,EAAE;IACTgB,MAAM,CAAC/B,OAAO,CAACkC,aAAa,GAAG,UAAUnB,KAAK,EAAE;EAClD;EACA,OAAOgB,MAAM;AACf,CAAC,EACAI,KAAK,IAAK;EACT,OAAO1B,OAAO,CAAC2B,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAvC,GAAG,CAACgC,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC1BO,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACE,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOnC,OAAO,CAAC2B,MAAM,CAACD,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMU,WAAW,GAAG;EAClB;EACA,MAAMtC,KAAKA,CAACC,WAAW,EAAE;IACvB,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAMzC,GAAG,CAACkD,IAAI,CAAC,aAAa,EAAEtC,WAAW,CAAC;MAC3D,OAAO6B,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMf,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMzC,GAAG,CAACkD,IAAI,CAAC,gBAAgB,EAAEzB,QAAQ,CAAC;MAC3D,OAAOgB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMT,WAAWA,CAACX,KAAK,EAAE;IACvB,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMzC,GAAG,CAACmD,GAAG,CAAC,cAAc,EAAE;QAC7C/C,OAAO,EAAE;UACPkC,aAAa,EAAE,UAAUnB,KAAK;QAChC;MACF,CAAC,CAAC;MACF,OAAOsB,QAAQ,CAACzB,IAAI;IACtB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMa,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMzC,GAAG,CAACmD,GAAG,CAAC,eAAe,CAAC;MAC/C,OAAOV,QAAQ,CAACzB,IAAI;IACtB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMc,aAAaA,CAACC,WAAW,EAAE;IAC/B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMzC,GAAG,CAACuD,GAAG,CAAC,eAAe,EAAED,WAAW,CAAC;MAC5D,OAAOb,QAAQ,CAACzB,IAAI;IACtB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMiB,cAAcA,CAACC,YAAY,EAAE;IACjC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMzC,GAAG,CAACuD,GAAG,CAAC,uBAAuB,EAAEE,YAAY,CAAC;MACrE,OAAOhB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMmB,oBAAoBA,CAAClD,KAAK,EAAE;IAChC,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMzC,GAAG,CAACkD,IAAI,CAAC,8BAA8B,EAAE;QAAE1C;MAAM,CAAC,CAAC;MAC1E,OAAOiC,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMoB,aAAaA,CAACC,SAAS,EAAE;IAC7B,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMzC,GAAG,CAACkD,IAAI,CAAC,sBAAsB,EAAEU,SAAS,CAAC;MAClE,OAAOnB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMsB,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMzC,GAAG,CAACkD,IAAI,CAAC,eAAe,CAAC;MAChD,MAAM;QAAE/B;MAAM,CAAC,GAAGsB,QAAQ;MAC1BL,YAAY,CAAC0B,OAAO,CAAC,WAAW,EAAE3C,KAAK,CAAC;MACxC,OAAOsB,QAAQ;IACjB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA,MAAMwB,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,MAAM/D,GAAG,CAACkD,IAAI,CAAC,cAAc,CAAC;MAC9Bd,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACtC,CAAC,CAAC,OAAON,KAAK,EAAE;MACd;MACAH,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;MACpC,MAAMN,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeU,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}