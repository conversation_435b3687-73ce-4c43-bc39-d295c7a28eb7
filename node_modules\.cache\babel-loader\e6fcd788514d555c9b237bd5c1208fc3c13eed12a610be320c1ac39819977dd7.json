{"ast": null, "code": "/**\n * React Router DOM v6.30.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { UNSAFE_mapRouteProperties, UNSAFE_logV6DeprecationWarnings, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, Router, UNSAFE_useRoutesImpl, UNSAFE_NavigationContext, useHref, useResolvedPath, useLocation, useNavigate, createPath, UNSAFE_useRouteId, UNSAFE_RouteContext, useMatches, useNavigation, useBlocker } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAF<PERSON>_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_useRouteId, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, redirectDocument, renderMatches, replace, resolvePath, useActionData, useAsyncError, useAsyncValue, useBlocker, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { stripBasename, UNSAFE_warning, createRouter, createBrowserHistory, createHashHistory, UNSAFE_ErrorResponseImpl, UNSAFE_invariant, joinPaths, IDLE_FETCHER, matchPath } from '@remix-run/router';\nexport { UNSAFE_ErrorResponseImpl } from '@remix-run/router';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && (\n  // Ignore everything but left clicks\n  !target || target === \"_self\") &&\n  // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach(value => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n  return searchParams;\n}\n// One-time check for submitter support\nlet _formDataSupportsSubmitter = null;\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(document.createElement(\"form\"),\n      // @ts-expect-error if FormData supports the submitter parameter, this will throw\n      0);\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\nconst supportedFormEncTypes = new Set([\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"]);\nfunction getFormEncType(encType) {\n  if (encType != null && !supportedFormEncTypes.has(encType)) {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"\\\"\" + encType + \"\\\" is not a valid `encType` for `<Form>`/`<fetcher.Form>` \" + (\"and will default to \\\"\" + defaultEncType + \"\\\"\")) : void 0;\n    return null;\n  }\n  return encType;\n}\nfunction getFormSubmissionInfo(target, basename) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n  let body;\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n    formData = new FormData(target);\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    }\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"formenctype\")) || getFormEncType(form.getAttribute(\"enctype\")) || defaultEncType;\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let {\n        name,\n        type,\n        value\n      } = target;\n      if (type === \"image\") {\n        let prefix = name ? name + \".\" : \"\";\n        formData.append(prefix + \"x\", \"0\");\n        formData.append(prefix + \"y\", \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n  return {\n    action,\n    method: method.toLowerCase(),\n    encType,\n    formData,\n    body\n  };\n}\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\", \"viewTransition\"],\n  _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"viewTransition\", \"children\"],\n  _excluded3 = [\"fetcherKey\", \"navigate\", \"reloadDocument\", \"replace\", \"state\", \"method\", \"action\", \"onSubmit\", \"relative\", \"preventScrollReset\", \"viewTransition\"];\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"6\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes,\n    mapRouteProperties: UNSAFE_mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation,\n    window: opts == null ? void 0 : opts.window\n  }).initialize();\n}\nfunction parseHydrationData() {\n  var _window;\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n  return state;\n}\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new UNSAFE_ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nconst ViewTransitionContext = /*#__PURE__*/React.createContext({\n  isTransitioning: false\n});\nif (process.env.NODE_ENV !== \"production\") {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\nconst FetchersContext = /*#__PURE__*/React.createContext(new Map());\nif (process.env.NODE_ENV !== \"production\") {\n  FetchersContext.displayName = \"Fetchers\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\nfunction startTransitionSafe(cb) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\nfunction flushSyncSafe(cb) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\nclass Deferred {\n  constructor() {\n    this.status = \"pending\";\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = value => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = reason => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState();\n  let [vtContext, setVtContext] = React.useState({\n    isTransitioning: false\n  });\n  let [renderDfd, setRenderDfd] = React.useState();\n  let [transition, setTransition] = React.useState();\n  let [interruption, setInterruption] = React.useState();\n  let fetcherData = React.useRef(new Map());\n  let {\n    v7_startTransition\n  } = future || {};\n  let optInStartTransition = React.useCallback(cb => {\n    if (v7_startTransition) {\n      startTransitionSafe(cb);\n    } else {\n      cb();\n    }\n  }, [v7_startTransition]);\n  let setState = React.useCallback((newState, _ref2) => {\n    let {\n      deletedFetchers,\n      flushSync: flushSync,\n      viewTransitionOpts: viewTransitionOpts\n    } = _ref2;\n    newState.fetchers.forEach((fetcher, key) => {\n      if (fetcher.data !== undefined) {\n        fetcherData.current.set(key, fetcher.data);\n      }\n    });\n    deletedFetchers.forEach(key => fetcherData.current.delete(key));\n    let isViewTransitionUnavailable = router.window == null || router.window.document == null || typeof router.window.document.startViewTransition !== \"function\";\n    // If this isn't a view transition or it's not available in this browser,\n    // just update and be done with it\n    if (!viewTransitionOpts || isViewTransitionUnavailable) {\n      if (flushSync) {\n        flushSyncSafe(() => setStateImpl(newState));\n      } else {\n        optInStartTransition(() => setStateImpl(newState));\n      }\n      return;\n    }\n    // flushSync + startViewTransition\n    if (flushSync) {\n      // Flush through the context to mark DOM elements as transition=ing\n      flushSyncSafe(() => {\n        // Cancel any pending transitions\n        if (transition) {\n          renderDfd && renderDfd.resolve();\n          transition.skipTransition();\n        }\n        setVtContext({\n          isTransitioning: true,\n          flushSync: true,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation\n        });\n      });\n      // Update the DOM\n      let t = router.window.document.startViewTransition(() => {\n        flushSyncSafe(() => setStateImpl(newState));\n      });\n      // Clean up after the animation completes\n      t.finished.finally(() => {\n        flushSyncSafe(() => {\n          setRenderDfd(undefined);\n          setTransition(undefined);\n          setPendingState(undefined);\n          setVtContext({\n            isTransitioning: false\n          });\n        });\n      });\n      flushSyncSafe(() => setTransition(t));\n      return;\n    }\n    // startTransition + startViewTransition\n    if (transition) {\n      // Interrupting an in-progress transition, cancel and let everything flush\n      // out, and then kick off a new transition from the interruption state\n      renderDfd && renderDfd.resolve();\n      transition.skipTransition();\n      setInterruption({\n        state: newState,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    } else {\n      // Completed navigation update with opted-in view transitions, let 'er rip\n      setPendingState(newState);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: viewTransitionOpts.currentLocation,\n        nextLocation: viewTransitionOpts.nextLocation\n      });\n    }\n  }, [router.window, transition, renderDfd, fetcherData, optInStartTransition]);\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred());\n    }\n  }, [vtContext]);\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({\n          isTransitioning: false\n        });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (renderDfd && pendingState && state.location.key === pendingState.location.key) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n  React.useEffect(() => {\n    process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : void 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = React.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  let routerFuture = React.useMemo(() => ({\n    v7_relativeSplatPath: router.future.v7_relativeSplatPath\n  }), [router.future.v7_relativeSplatPath]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future, router.future), [future, router.future]);\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(UNSAFE_DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/React.createElement(UNSAFE_DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(FetchersContext.Provider, {\n    value: fetcherData.current\n  }, /*#__PURE__*/React.createElement(ViewTransitionContext.Provider, {\n    value: vtContext\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: routerFuture\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/React.createElement(MemoizedDataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))))), null);\n}\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = /*#__PURE__*/React.memo(DataRoutes);\nfunction DataRoutes(_ref3) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref3;\n  return UNSAFE_useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nfunction BrowserRouter(_ref4) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref4;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nfunction HashRouter(_ref5) {\n  let {\n    basename,\n    children,\n    future,\n    window\n  } = _ref5;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter(_ref6) {\n  let {\n    basename,\n    children,\n    future,\n    history\n  } = _ref6;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = React.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  React.useEffect(() => UNSAFE_logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref7, ref) {\n  let {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition\n    } = _ref7,\n    rest = _objectWithoutPropertiesLoose(_ref7, _excluded);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  // Rendered into <a href> for absolute URLs\n  let absoluteHref;\n  let isExternal = false;\n  if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n    // Render the absolute href server- and client-side\n    absoluteHref = to;\n    // Only check for external origins client-side\n    if (isBrowser) {\n      try {\n        let currentUrl = new URL(window.location.href);\n        let targetUrl = to.startsWith(\"//\") ? new URL(currentUrl.protocol + to) : new URL(to);\n        let path = stripBasename(targetUrl.pathname, basename);\n        if (targetUrl.origin === currentUrl.origin && path != null) {\n          // Strip the protocol/origin/basename for same-origin absolute URLs\n          to = path + targetUrl.search + targetUrl.hash;\n        } else {\n          isExternal = true;\n        }\n      } catch (e) {\n        // We can't do external URL detection without a valid URL\n        process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"<Link to=\\\"\" + to + \"\\\"> contains an invalid URL which will probably break \" + \"when clicked - please update to a valid URL path.\") : void 0;\n      }\n    }\n  }\n  // Rendered into <a href> for relative URLs\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative,\n    viewTransition\n  });\n  function handleClick(event) {\n    if (onClick) onClick(event);\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n  return (/*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: absoluteHref || href,\n      onClick: isExternal || reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref8, ref) {\n  let {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children\n    } = _ref8,\n    rest = _objectWithoutPropertiesLoose(_ref8, _excluded2);\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator,\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let isTransitioning = routerState != null &&\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  useViewTransitionState(path) && viewTransition === true;\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n  if (nextLocationPathname && basename) {\n    nextLocationPathname = stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n  }\n  // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n  // we're looking for a slash _after_ what's in `to`.  For example:\n  //\n  // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n  // both want to look for a / at index 6 to match URL `/users/matt`\n  const endSlashPosition = toPathname !== \"/\" && toPathname.endsWith(\"/\") ? toPathname.length - 1 : toPathname.length;\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(endSlashPosition) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let renderProps = {\n    isActive,\n    isPending,\n    isTransitioning\n  };\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp(renderProps);\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null, isTransitioning ? \"transitioning\" : null].filter(Boolean).join(\" \");\n  }\n  let style = typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to,\n    viewTransition: viewTransition\n  }), typeof children === \"function\" ? children(renderProps) : children);\n});\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nconst Form = /*#__PURE__*/React.forwardRef((_ref9, forwardedRef) => {\n  let {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition\n    } = _ref9,\n    props = _objectWithoutPropertiesLoose(_ref9, _excluded3);\n  let submit = useSubmit();\n  let formAction = useFormAction(action, {\n    relative\n  });\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      fetcherKey,\n      method: submitMethod,\n      navigate,\n      replace,\n      state,\n      relative,\n      preventScrollReset,\n      viewTransition\n    });\n  };\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nfunction ScrollRestoration(_ref10) {\n  let {\n    getKey,\n    storageKey\n  } = _ref10;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmit\"] = \"useSubmit\";\n  DataRouterHook[\"UseSubmitFetcher\"] = \"useSubmitFetcher\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterHook[\"useViewTransitionState\"] = \"useViewTransitionState\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetcher\"] = \"useFetcher\";\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\n// Internal hooks\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, getDataRouterConsoleError(hookName)) : UNSAFE_invariant(false) : void 0;\n  return state;\n}\n// External hooks\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault();\n      // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative,\n        viewTransition\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative, viewTransition]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n  let location = useLocation();\n  let searchParams = React.useMemo(() =>\n  // Only merge in the defaults if we haven't yet called setSearchParams.\n  // Once we call that we want those to take precedence, otherwise you can't\n  // remove a param with setSearchParams({}) if it has an initial value\n  getSearchParamsForLocation(location.search, hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    hasSetSearchParamsRef.current = true;\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n  }\n}\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => \"__\" + String(++fetcherId) + \"__\";\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nfunction useSubmit() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let currentRouteId = UNSAFE_useRouteId();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    validateClientSideSubmission();\n    let {\n      action,\n      method,\n      encType,\n      formData,\n      body\n    } = getFormSubmissionInfo(target, basename);\n    if (options.navigate === false) {\n      let key = options.fetcherKey || getUniqueFetcherId();\n      router.fetch(key, currentRouteId, options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        flushSync: options.flushSync\n      });\n    } else {\n      router.navigate(options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || method,\n        formEncType: options.encType || encType,\n        replace: options.replace,\n        state: options.state,\n        fromRouteId: currentRouteId,\n        flushSync: options.flushSync,\n        viewTransition: options.viewTransition\n      });\n    }\n  }, [router, basename, currentRouteId]);\n}\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFormAction must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  }));\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some(v => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter(v => v).forEach(v => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? \"?\" + qs : \"\";\n    }\n  }\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nfunction useFetcher(_temp3) {\n  var _route$matches;\n  let {\n    key\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(UNSAFE_RouteContext);\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !fetcherData ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a FetchersContext\") : UNSAFE_invariant(false) : void 0;\n  !route ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher must be used inside a RouteContext\") : UNSAFE_invariant(false) : void 0;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : UNSAFE_invariant(false) : void 0;\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  // Fetcher additions\n  let load = React.useCallback((href, opts) => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"No routeId available for fetcher.load()\") : UNSAFE_invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href, opts);\n  }, [fetcherKey, routeId, router]);\n  let submitImpl = useSubmit();\n  let submit = React.useCallback((target, opts) => {\n    submitImpl(target, _extends({}, opts, {\n      navigate: false,\n      fetcherKey\n    }));\n  }, [fetcherKey, submitImpl]);\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n      return /*#__PURE__*/React.createElement(Form, _extends({}, props, {\n        navigate: false,\n        fetcherKey: fetcherKey,\n        ref: ref\n      }));\n    });\n    if (process.env.NODE_ENV !== \"production\") {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form: FetcherForm,\n    submit,\n    load\n  }, fetcher, {\n    data\n  }), [FetcherForm, submit, load, fetcher, data]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(_ref11 => {\n    let [key, fetcher] = _ref11;\n    return _extends({}, fetcher, {\n      key\n    });\n  });\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration(_temp4) {\n  let {\n    getKey,\n    storageKey\n  } = _temp4 === void 0 ? {} : _temp4;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n  // Save positions on pagehide\n  usePageHide(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n    try {\n      sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    } catch (error) {\n      process.env.NODE_ENV !== \"production\" ? UNSAFE_warning(false, \"Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (\" + error + \").\") : void 0;\n    }\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches]));\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename = getKey && basename !== \"/\" ? (location, matches) => getKey(\n      // Strip the basename to match useLocation()\n      _extends({}, location, {\n        pathname: stripBasename(location.pathname, basename) || location.pathname\n      }), matches) : getKey;\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKeyWithoutBasename);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(decodeURIComponent(location.hash.slice(1)));\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction useBeforeUnload(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(callback, options) {\n  let {\n    capture\n  } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? {\n      capture\n    } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt(_ref12) {\n  let {\n    when,\n    message\n  } = _ref12;\n  let blocker = useBlocker(when);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(to, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  let vtContext = React.useContext(ViewTransitionContext);\n  !(vtContext != null) ? process.env.NODE_ENV !== \"production\" ? UNSAFE_invariant(false, \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" + \"Did you accidentally import `RouterProvider` from `react-router`?\") : UNSAFE_invariant(false) : void 0;\n  let {\n    basename\n  } = useDataRouterContext(DataRouterHook.useViewTransitionState);\n  let path = useResolvedPath(to, {\n    relative: opts.relative\n  });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n  let currentPath = stripBasename(vtContext.currentLocation.pathname, basename) || vtContext.currentLocation.pathname;\n  let nextPath = stripBasename(vtContext.nextLocation.pathname, basename) || vtContext.nextLocation.pathname;\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return matchPath(path.pathname, nextPath) != null || matchPath(path.pathname, currentPath) != null;\n}\n//#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, RouterProvider, ScrollRestoration, FetchersContext as UNSAFE_FetchersContext, ViewTransitionContext as UNSAFE_ViewTransitionContext, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, usePrompt as unstable_usePrompt, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit, useViewTransitionState };", "map": {"version": 3, "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "env", "NODE_ENV", "UNSAFE_warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "REACT_ROUTER_VERSION", "window", "__reactRouterVersion", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "hydrationData", "parseHydrationData", "UNSAFE_mapRouteProperties", "dataStrategy", "patchRoutesOnNavigation", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "UNSAFE_ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "React", "createContext", "isTransitioning", "displayName", "FetchersContext", "Map", "START_TRANSITION", "startTransitionImpl", "FLUSH_SYNC", "flushSyncImpl", "ReactDOM", "USE_ID", "useIdImpl", "startTransitionSafe", "cb", "flushSyncSafe", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "fetcherData", "useRef", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "deletedFetchers", "flushSync", "viewTransitionOpts", "fetchers", "fetcher", "current", "set", "delete", "isViewTransitionUnavailable", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "t", "finished", "finally", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "location", "v7_partialHydration", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "routerFuture", "v7_relativeSplatPath", "UNSAFE_logV6DeprecationWarnings", "Fragment", "UNSAFE_DataRouterContext", "Provider", "UNSAFE_DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "MemoizedDataRoutes", "DataRoutes", "_ref3", "UNSAFE_useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "UNSAFE_NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "endSlashPosition", "endsWith", "length", "isActive", "char<PERSON>t", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "_ref9", "forwardedRef", "fetcher<PERSON>ey", "onSubmit", "props", "_excluded3", "submit", "useSubmit", "formAction", "useFormAction", "formMethod", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "UNSAFE_invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "fetcherId", "getUniqueFetcherId", "String", "UseSubmit", "currentRouteId", "UNSAFE_useRouteId", "options", "fetch", "formEncType", "fromRouteId", "_temp2", "routeContext", "UNSAFE_RouteContext", "match", "matches", "slice", "params", "indexValues", "hasNakedIndexParam", "some", "qs", "toString", "route", "index", "joinPaths", "useFetcher", "_temp3", "_route$matches", "UseFetcher", "routeId", "id", "defaultKey", "setFetcher<PERSON>ey", "getFetcher", "deleteFetcher", "load", "submitImpl", "FetcherForm", "get", "IDLE_FETCHER", "fetcherWithComponents", "useFetchers", "UseFetchers", "from", "_ref11", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp4", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref12", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"], "sources": ["D:\\Test\\Battle Launcher\\node_modules\\react-router-dom\\dom.ts", "D:\\Test\\Battle Launcher\\node_modules\\react-router-dom\\index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\n/**\n * Submit options shared by both navigations and fetchers\n */\ninterface SharedSubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable flushSync for this submission's state updates\n   */\n  flushSync?: boolean;\n}\n\n/**\n * Submit options available to fetchers\n */\nexport interface FetcherSubmitOptions extends SharedSubmitOptions {}\n\n/**\n * Submit options available to navigations\n */\nexport interface SubmitOptions extends FetcherSubmitOptions {\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProps,\n  RouterProviderProps,\n  To,\n  DataStrategyFunction,\n  PatchRoutesOnNavigationFunction,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_logV6DeprecationWarnings as logV6DeprecationWarnings,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  BrowserHistory,\n  Fetcher,\n  FormEncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n  BlockerFunction,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n  IDLE_FETCHER,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n  FetcherSubmitOptions,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams, ErrorResponseImpl as UNSAFE_ErrorResponseImpl };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker,\n  BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DataStrategyMatch,\n  DataStrategyResult,\n  ErrorResponse,\n  Fetcher,\n  FutureConfig,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  PatchRoutesOnNavigationFunction,\n  PatchRoutesOnNavigationFunctionArgs,\n  Path,\n  PathMatch,\n  Pathname,\n  PathParam,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  replace,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  var __reactRouterVersion: string;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n// HEY YOU! DON'T TOUCH THIS VARIABLE!\n//\n// It is replaced with the proper version at build time via a babel plugin in\n// the rollup config.\n//\n// Export a global property onto the window for React Router detection by the\n// Core Web Vitals Technology Report.  This way they can configure the `wappalyzer`\n// to detect and properly classify live websites as being built with React Router:\n// https://github.com/HTTPArchive/wappalyzer/blob/main/src/technologies/r.json\nconst REACT_ROUTER_VERSION = \"0\";\ntry {\n  window.__reactRouterVersion = REACT_ROUTER_VERSION;\n} catch (e) {\n  // no-op\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: PatchRoutesOnNavigationFunction;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts?.dataStrategy,\n    patchRoutesOnNavigation: opts?.patchRoutesOnNavigation,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      flushSync: boolean;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n// TODO: (v7) Change the useFetcher data from `any` to `unknown`\ntype FetchersContextObject = Map<string, any>;\n\nconst FetchersContext = React.createContext<FetchersContextObject>(new Map());\nif (__DEV__) {\n  FetchersContext.displayName = \"Fetchers\";\n}\n\nexport { FetchersContext as UNSAFE_FetchersContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\nconst FLUSH_SYNC = \"flushSync\";\nconst flushSyncImpl = ReactDOM[FLUSH_SYNC];\nconst USE_ID = \"useId\";\nconst useIdImpl = React[USE_ID];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\nfunction flushSyncSafe(cb: () => void) {\n  if (flushSyncImpl) {\n    flushSyncImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let fetcherData = React.useRef<Map<string, any>>(new Map());\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      {\n        deletedFetchers,\n        flushSync: flushSync,\n        viewTransitionOpts: viewTransitionOpts,\n      }\n    ) => {\n      newState.fetchers.forEach((fetcher, key) => {\n        if (fetcher.data !== undefined) {\n          fetcherData.current.set(key, fetcher.data);\n        }\n      });\n      deletedFetchers.forEach((key) => fetcherData.current.delete(key));\n\n      let isViewTransitionUnavailable =\n        router.window == null ||\n        router.window.document == null ||\n        typeof router.window.document.startViewTransition !== \"function\";\n\n      // If this isn't a view transition or it's not available in this browser,\n      // just update and be done with it\n      if (!viewTransitionOpts || isViewTransitionUnavailable) {\n        if (flushSync) {\n          flushSyncSafe(() => setStateImpl(newState));\n        } else {\n          optInStartTransition(() => setStateImpl(newState));\n        }\n        return;\n      }\n\n      // flushSync + startViewTransition\n      if (flushSync) {\n        // Flush through the context to mark DOM elements as transition=ing\n        flushSyncSafe(() => {\n          // Cancel any pending transitions\n          if (transition) {\n            renderDfd && renderDfd.resolve();\n            transition.skipTransition();\n          }\n          setVtContext({\n            isTransitioning: true,\n            flushSync: true,\n            currentLocation: viewTransitionOpts.currentLocation,\n            nextLocation: viewTransitionOpts.nextLocation,\n          });\n        });\n\n        // Update the DOM\n        let t = router.window!.document.startViewTransition(() => {\n          flushSyncSafe(() => setStateImpl(newState));\n        });\n\n        // Clean up after the animation completes\n        t.finished.finally(() => {\n          flushSyncSafe(() => {\n            setRenderDfd(undefined);\n            setTransition(undefined);\n            setPendingState(undefined);\n            setVtContext({ isTransitioning: false });\n          });\n        });\n\n        flushSyncSafe(() => setTransition(t));\n        return;\n      }\n\n      // startTransition + startViewTransition\n      if (transition) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd && renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          flushSync: false,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [router.window, transition, renderDfd, fetcherData, optInStartTransition]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning && !vtContext.flushSync) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        flushSync: false,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  React.useEffect(() => {\n    warning(\n      fallbackElement == null || !router.future.v7_partialHydration,\n      \"`<RouterProvider fallbackElement>` is deprecated when using \" +\n        \"`v7_partialHydration`, use a `HydrateFallback` component instead\"\n    );\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  let routerFuture = React.useMemo<RouterProps[\"future\"]>(\n    () => ({\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath,\n    }),\n    [router.future.v7_relativeSplatPath]\n  );\n\n  React.useEffect(\n    () => logV6DeprecationWarnings(future, router.future),\n    [future, router.future]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <FetchersContext.Provider value={fetcherData.current}>\n            <ViewTransitionContext.Provider value={vtContext}>\n              <Router\n                basename={basename}\n                location={state.location}\n                navigationType={state.historyAction}\n                navigator={navigator}\n                future={routerFuture}\n              >\n                {state.initialized || router.future.v7_partialHydration ? (\n                  <MemoizedDataRoutes\n                    routes={router.routes}\n                    future={router.future}\n                    state={state}\n                  />\n                ) : (\n                  fallbackElement\n                )}\n              </Router>\n            </ViewTransitionContext.Provider>\n          </FetchersContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\n// Memoize to avoid re-renders when updating `ViewTransitionContext`\nconst MemoizedDataRoutes = React.memo(DataRoutes);\n\nfunction DataRoutes({\n  routes,\n  future,\n  state,\n}: {\n  routes: DataRouteObject[];\n  future: RemixRouter[\"future\"];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state, future);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: Partial<FutureConfig>;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  React.useEffect(() => logV6DeprecationWarnings(future), [future]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n      future={future}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\nexport type NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator, basename } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    if (nextLocationPathname && basename) {\n      nextLocationPathname =\n        stripBasename(nextLocationPathname, basename) || nextLocationPathname;\n    }\n\n    // If the `to` has a trailing slash, look at that exact spot.  Otherwise,\n    // we're looking for a slash _after_ what's in `to`.  For example:\n    //\n    // <NavLink to=\"/users\"> and <NavLink to=\"/users/\">\n    // both want to look for a / at index 6 to match URL `/users/matt`\n    const endSlashPosition =\n      toPathname !== \"/\" && toPathname.endsWith(\"/\")\n        ? toPathname.length - 1\n        : toPathname.length;\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(endSlashPosition) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        viewTransition={viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\n/**\n * Form props shared by navigations and fetchers\n */\ninterface SharedFormProps extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\n/**\n * Form props available to fetchers\n */\nexport interface FetcherFormProps extends SharedFormProps {}\n\n/**\n * Form props available to navigations\n */\nexport interface FormProps extends SharedFormProps {\n  /**\n   * Indicate a specific fetcherKey to use when using navigate=false\n   */\n  fetcherKey?: string;\n\n  /**\n   * navigate=false will use a fetcher instead of a navigation\n   */\n  navigate?: boolean;\n\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  viewTransition?: boolean;\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (\n    {\n      fetcherKey,\n      navigate,\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      relative,\n      preventScrollReset,\n      viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let submit = useSubmit();\n    let formAction = useFormAction(action, { relative });\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        fetcherKey,\n        method: submitMethod,\n        navigate,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetcher = \"useFetcher\",\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\n// Internal hooks\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n// External hooks\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: FetcherSubmitOptions\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\nlet fetcherId = 0;\nlet getUniqueFetcherId = () => `__${String(++fetcherId)}__`;\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      if (options.navigate === false) {\n        let key = options.fetcherKey || getUniqueFetcherId();\n        router.fetch(key, currentRouteId, options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          flushSync: options.flushSync,\n        });\n      } else {\n        router.navigate(options.action || action, {\n          preventScrollReset: options.preventScrollReset,\n          formData,\n          body,\n          formMethod: options.method || (method as HTMLFormMethod),\n          formEncType: options.encType || (encType as FormEncType),\n          replace: options.replace,\n          state: options.state,\n          fromRouteId: currentRouteId,\n          flushSync: options.flushSync,\n          viewTransition: options.viewTransition,\n        });\n      }\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // If no action was specified, browsers will persist current search params\n  // when determining the path, so match that behavior\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove any included ?index param\n    // since it might not apply to our contextual route.  We add it back based\n    // on match.route.index below\n    let params = new URLSearchParams(path.search);\n    let indexValues = params.getAll(\"index\");\n    let hasNakedIndexParam = indexValues.some((v) => v === \"\");\n    if (hasNakedIndexParam) {\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: React.ForwardRefExoticComponent<\n    FetcherFormProps & React.RefAttributes<HTMLFormElement>\n  >;\n  submit: FetcherSubmitFunction;\n  load: (href: string, opts?: { flushSync?: boolean }) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>({\n  key,\n}: { key?: string } = {}): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let state = useDataRouterState(DataRouterStateHook.UseFetcher);\n  let fetcherData = React.useContext(FetchersContext);\n  let route = React.useContext(RouteContext);\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n\n  invariant(fetcherData, `useFetcher must be used inside a FetchersContext`);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  // Fetcher key handling\n  // OK to call conditionally to feature detect `useId`\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  let defaultKey = useIdImpl ? useIdImpl() : \"\";\n  let [fetcherKey, setFetcherKey] = React.useState<string>(key || defaultKey);\n  if (key && key !== fetcherKey) {\n    setFetcherKey(key);\n  } else if (!fetcherKey) {\n    // We will only fall through here when `useId` is not available\n    setFetcherKey(getUniqueFetcherId());\n  }\n\n  // Registration/cleanup\n  React.useEffect(() => {\n    router.getFetcher(fetcherKey);\n    return () => {\n      // Tell the router we've unmounted - if v7_fetcherPersist is enabled this\n      // will not delete immediately but instead queue up a delete after the\n      // fetcher returns to an `idle` state\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  // Fetcher additions\n  let load = React.useCallback(\n    (href: string, opts?: { flushSync?: boolean }) => {\n      invariant(routeId, \"No routeId available for fetcher.load()\");\n      router.fetch(fetcherKey, routeId, href, opts);\n    },\n    [fetcherKey, routeId, router]\n  );\n\n  let submitImpl = useSubmit();\n  let submit = React.useCallback<FetcherSubmitFunction>(\n    (target, opts) => {\n      submitImpl(target, {\n        ...opts,\n        navigate: false,\n        fetcherKey,\n      });\n    },\n    [fetcherKey, submitImpl]\n  );\n\n  let FetcherForm = React.useMemo(() => {\n    let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n      (props, ref) => {\n        return (\n          <Form {...props} navigate={false} fetcherKey={fetcherKey} ref={ref} />\n        );\n      }\n    );\n    if (__DEV__) {\n      FetcherForm.displayName = \"fetcher.Form\";\n    }\n    return FetcherForm;\n  }, [fetcherKey]);\n\n  // Exposed FetcherWithComponents\n  let fetcher = state.fetchers.get(fetcherKey) || IDLE_FETCHER;\n  let data = fetcherData.get(fetcherKey);\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form: FetcherForm,\n      submit,\n      load,\n      ...fetcher,\n      data,\n    }),\n    [FetcherForm, submit, load, fetcher, data]\n  );\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): (Fetcher & { key: string })[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return Array.from(state.fetchers.entries()).map(([key, fetcher]) => ({\n    ...fetcher,\n    key,\n  }));\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({\n  when,\n  message,\n}: {\n  when: boolean | BlockerFunction;\n  message: string;\n}) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as useViewTransitionState };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAMA,aAAa,GAAmB,KAAK;AAClD,MAAMC,cAAc,GAAgB,mCAAmC;AAEjE,SAAUC,aAAaA,CAACC,MAAW;EACvC,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,QAAQ;AAC7D;AAEM,SAAUC,eAAeA,CAACF,MAAW;EACzC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,QAAQ;AAC3E;AAEM,SAAUC,aAAaA,CAACJ,MAAW;EACvC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,MAAM;AACzE;AAEM,SAAUE,cAAcA,CAACL,MAAW;EACxC,OAAOD,aAAa,CAACC,MAAM,CAAC,IAAIA,MAAM,CAACC,OAAO,CAACE,WAAW,EAAE,KAAK,OAAO;AAC1E;AAOA,SAASG,eAAeA,CAACC,KAAwB;EAC/C,OAAO,CAAC,EAAEA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,QAAQ,CAAC;AAC7E;AAEgB,SAAAC,sBAAsBA,CACpCL,KAAwB,EACxBM,MAAe;EAEf,OACEN,KAAK,CAACO,MAAM,KAAK,CAAC;EAAI;EACrB,CAACD,MAAM,IAAIA,MAAM,KAAK,OAAO,CAAC;EAAI;EACnC,CAACP,eAAe,CAACC,KAAK,CAAC;EAAA;AAE3B;AAUA;;;;;;;;;;;;;;;;;;;;AAoBG;AACa,SAAAQ,kBAAkBA,CAChCC,IAAA,EAA8B;EAAA,IAA9BA,IAAA;IAAAA,IAAA,GAA4B,EAAE;EAAA;EAE9B,OAAO,IAAIC,eAAe,CACxB,OAAOD,IAAI,KAAK,QAAQ,IACxBE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,IACnBA,IAAI,YAAYC,eAAe,GAC3BD,IAAI,GACJI,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAI;IACrC,IAAIC,KAAK,GAAGT,IAAI,CAACQ,GAAG,CAAC;IACrB,OAAOD,IAAI,CAACG,MAAM,CAChBR,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,GAAGA,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAK,CAACJ,GAAG,EAAEI,CAAC,CAAC,CAAC,GAAG,CAAC,CAACJ,GAAG,EAAEC,KAAK,CAAC,CAAC,CACnE;GACF,EAAE,EAAyB,CAAC,CAClC;AACH;AAEgB,SAAAI,0BAA0BA,CACxCC,cAAsB,EACtBC,mBAA2C;EAE3C,IAAIC,YAAY,GAAGjB,kBAAkB,CAACe,cAAc,CAAC;EAErD,IAAIC,mBAAmB,EAAE;IACvB;IACA;IACA;IACA;IACA;IACAA,mBAAmB,CAACE,OAAO,CAAC,CAACC,CAAC,EAAEV,GAAG,KAAI;MACrC,IAAI,CAACQ,YAAY,CAACG,GAAG,CAACX,GAAG,CAAC,EAAE;QAC1BO,mBAAmB,CAACK,MAAM,CAACZ,GAAG,CAAC,CAACS,OAAO,CAAER,KAAK,IAAI;UAChDO,YAAY,CAACK,MAAM,CAACb,GAAG,EAAEC,KAAK,CAAC;QACjC,CAAC,CAAC;MACH;IACH,CAAC,CAAC;EACH;EAED,OAAOO,YAAY;AACrB;AAmBA;AACA,IAAIM,0BAA0B,GAAmB,IAAI;AAErD,SAASC,4BAA4BA,CAAA;EACnC,IAAID,0BAA0B,KAAK,IAAI,EAAE;IACvC,IAAI;MACF,IAAIE,QAAQ,CACVC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAC9B;MACA,CAAC,CACF;MACDJ,0BAA0B,GAAG,KAAK;KACnC,CAAC,OAAOK,CAAC,EAAE;MACVL,0BAA0B,GAAG,IAAI;IAClC;EACF;EACD,OAAOA,0BAA0B;AACnC;AAgFA,MAAMM,qBAAqB,GAAqB,IAAIC,GAAG,CAAC,CACtD,mCAAmC,EACnC,qBAAqB,EACrB,YAAY,CACb,CAAC;AAEF,SAASC,cAAcA,CAACC,OAAsB;EAC5C,IAAIA,OAAO,IAAI,IAAI,IAAI,CAACH,qBAAqB,CAACT,GAAG,CAACY,OAAsB,CAAC,EAAE;IACzEC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,EACL,IAAI,GAAAJ,OAAO,GACe,2FAAAjD,cAAc,QAAG,CAC5C;IAED,OAAO,IAAI;EACZ;EACD,OAAOiD,OAAO;AAChB;AAEgB,SAAAK,qBAAqBA,CACnCvC,MAAoB,EACpBwC,QAAgB;EAQhB,IAAIC,MAAc;EAClB,IAAIC,MAAqB;EACzB,IAAIR,OAAe;EACnB,IAAIS,QAA8B;EAClC,IAAIC,IAAS;EAEb,IAAIrD,aAAa,CAACS,MAAM,CAAC,EAAE;IACzB;IACA;IACA;IACA,IAAI6C,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC;IACxCJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI;IACpDC,MAAM,GAAGzC,MAAM,CAAC8C,YAAY,CAAC,QAAQ,CAAC,IAAI9D,aAAa;IACvDkD,OAAO,GAAGD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI7D,cAAc;IAE1E0D,QAAQ,GAAG,IAAIhB,QAAQ,CAAC3B,MAAM,CAAC;GAChC,MAAM,IACLX,eAAe,CAACW,MAAM,CAAC,IACtBR,cAAc,CAACQ,MAAM,CAAC,KACpBA,MAAM,CAACgD,IAAI,KAAK,QAAQ,IAAIhD,MAAM,CAACgD,IAAI,KAAK,OAAO,CAAE,EACxD;IACA,IAAIC,IAAI,GAAGjD,MAAM,CAACiD,IAAI;IAEtB,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,MAAM,IAAIC,KAAK,uEACuD,CACrE;IACF;IAED;IAEA;IACA;IACA;IACA,IAAIL,IAAI,GAAG7C,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IAAIG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC;IAC3EJ,MAAM,GAAGG,IAAI,GAAGE,aAAa,CAACF,IAAI,EAAEL,QAAQ,CAAC,GAAG,IAAI;IAEpDC,MAAM,GACJzC,MAAM,CAAC8C,YAAY,CAAC,YAAY,CAAC,IACjCG,IAAI,CAACH,YAAY,CAAC,QAAQ,CAAC,IAC3B9D,aAAa;IACfkD,OAAO,GACLD,cAAc,CAACjC,MAAM,CAAC8C,YAAY,CAAC,aAAa,CAAC,CAAC,IAClDb,cAAc,CAACgB,IAAI,CAACH,YAAY,CAAC,SAAS,CAAC,CAAC,IAC5C7D,cAAc;IAEhB;IACA0D,QAAQ,GAAG,IAAIhB,QAAQ,CAACsB,IAAI,EAAEjD,MAAM,CAAC;IAErC;IACA;IACA;IACA;IACA,IAAI,CAAC0B,4BAA4B,EAAE,EAAE;MACnC,IAAI;QAAEyB,IAAI;QAAEH,IAAI;QAAEpC;MAAK,CAAE,GAAGZ,MAAM;MAClC,IAAIgD,IAAI,KAAK,OAAO,EAAE;QACpB,IAAII,MAAM,GAAGD,IAAI,GAAMA,IAAI,SAAM,EAAE;QACnCR,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,QAAK,GAAG,CAAC;QAClCT,QAAQ,CAACnB,MAAM,CAAI4B,MAAM,QAAK,GAAG,CAAC;OACnC,MAAM,IAAID,IAAI,EAAE;QACfR,QAAQ,CAACnB,MAAM,CAAC2B,IAAI,EAAEvC,KAAK,CAAC;MAC7B;IACF;EACF,OAAM,IAAI1B,aAAa,CAACc,MAAM,CAAC,EAAE;IAChC,MAAM,IAAIkD,KAAK,CACb,2FAC+B,CAChC;EACF,OAAM;IACLT,MAAM,GAAGzD,aAAa;IACtB0D,MAAM,GAAG,IAAI;IACbR,OAAO,GAAGjD,cAAc;IACxB2D,IAAI,GAAG5C,MAAM;EACd;EAED;EACA,IAAI2C,QAAQ,IAAIT,OAAO,KAAK,YAAY,EAAE;IACxCU,IAAI,GAAGD,QAAQ;IACfA,QAAQ,GAAGU,SAAS;EACrB;EAED,OAAO;IAAEX,MAAM;IAAED,MAAM,EAAEA,MAAM,CAACnD,WAAW,EAAE;IAAE4C,OAAO;IAAES,QAAQ;IAAEC;GAAM;AAC1E;;;;ACvGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,MAAAU,oBAAA;AAEA,IAAI;EACFC,MAAM,CAACC,oBAAoB,GAAGF,oBAAoB;AACnD,EAAC,OAAOxB,CAAC,EAAE;EACV;AAAA;AAgBc,SAAA2B,mBAAmBA,CACjCC,MAAqB,EACrBC,IAAoB;EAEpB,OAAOC,YAAY,CAAC;IAClBpB,QAAQ,EAAEmB,IAAI,IAAJ,gBAAAA,IAAI,CAAEnB,QAAQ;IACxBqB,MAAM,EAAAC,QAAA,KACDH,IAAI,IAAJ,gBAAAA,IAAI,CAAEE,MAAM;MACfE,kBAAkB,EAAE;KACrB;IACDC,OAAO,EAAEC,oBAAoB,CAAC;MAAEV,MAAM,EAAEI,IAAI,IAAJ,gBAAAA,IAAI,CAAEJ;IAAM,CAAE,CAAC;IACvDW,aAAa,EAAE,CAAAP,IAAI,IAAJ,gBAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;IAC1DT,MAAM;wBACNU,yBAAkB;IAClBC,YAAY,EAAEV,IAAI,IAAJ,gBAAAA,IAAI,CAAEU,YAAY;IAChCC,uBAAuB,EAAEX,IAAI,IAAJ,gBAAAA,IAAI,CAAEW,uBAAuB;IACtDf,MAAM,EAAEI,IAAI,IAAJ,gBAAAA,IAAI,CAAEJ;GACf,CAAC,CAACgB,UAAU,EAAE;AACjB;AAEgB,SAAAC,gBAAgBA,CAC9Bd,MAAqB,EACrBC,IAAoB;EAEpB,OAAOC,YAAY,CAAC;IAClBpB,QAAQ,EAAEmB,IAAI,IAAJ,gBAAAA,IAAI,CAAEnB,QAAQ;IACxBqB,MAAM,EAAAC,QAAA,KACDH,IAAI,IAAJ,gBAAAA,IAAI,CAAEE,MAAM;MACfE,kBAAkB,EAAE;KACrB;IACDC,OAAO,EAAES,iBAAiB,CAAC;MAAElB,MAAM,EAAEI,IAAI,IAAJ,gBAAAA,IAAI,CAAEJ;IAAM,CAAE,CAAC;IACpDW,aAAa,EAAE,CAAAP,IAAI,IAAJ,gBAAAA,IAAI,CAAEO,aAAa,KAAIC,kBAAkB,EAAE;IAC1DT,MAAM;wBACNU,yBAAkB;IAClBC,YAAY,EAAEV,IAAI,IAAJ,gBAAAA,IAAI,CAAEU,YAAY;IAChCC,uBAAuB,EAAEX,IAAI,IAAJ,gBAAAA,IAAI,CAAEW,uBAAuB;IACtDf,MAAM,EAAEI,IAAI,IAAJ,gBAAAA,IAAI,CAAEJ;GACf,CAAC,CAACgB,UAAU,EAAE;AACjB;AAEA,SAASJ,kBAAkBA,CAAA;EAAA,IAAAO,OAAA;EACzB,IAAIC,KAAK,IAAAD,OAAA,GAAGnB,MAAM,KAAN,gBAAAmB,OAAA,CAAQE,2BAA2B;EAC/C,IAAID,KAAK,IAAIA,KAAK,CAACE,MAAM,EAAE;IACzBF,KAAK,GAAAb,QAAA,KACAa,KAAK;MACRE,MAAM,EAAEC,iBAAiB,CAACH,KAAK,CAACE,MAAM;KACvC;EACF;EACD,OAAOF,KAAK;AACd;AAEA,SAASG,iBAAiBA,CACxBD,MAAsC;EAEtC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIE,OAAO,GAAGxE,MAAM,CAACwE,OAAO,CAACF,MAAM,CAAC;EACpC,IAAIG,UAAU,GAAmC,EAAE;EACnD,KAAK,IAAI,CAACrE,GAAG,EAAEsE,GAAG,CAAC,IAAIF,OAAO,EAAE;IAC9B;IACA;IACA,IAAIE,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACrE,GAAG,CAAC,GAAG,IAAIwE,wBAAiB,CACrCF,GAAG,CAACG,MAAM,EACVH,GAAG,CAACI,UAAU,EACdJ,GAAG,CAACK,IAAI,EACRL,GAAG,CAACM,QAAQ,KAAK,IAAI,CACtB;KACF,MAAM,IAAIN,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC;MACA,IAAID,GAAG,CAACO,SAAS,EAAE;QACjB,IAAIC,gBAAgB,GAAGlC,MAAM,CAAC0B,GAAG,CAACO,SAAS,CAAC;QAC5C,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF;YACA,IAAIC,KAAK,GAAG,IAAID,gBAAgB,CAACR,GAAG,CAACU,OAAO,CAAC;YAC7C;YACA;YACAD,KAAK,CAACE,KAAK,GAAG,EAAE;YAChBZ,UAAU,CAACrE,GAAG,CAAC,GAAG+E,KAAK;WACxB,CAAC,OAAO5D,CAAC,EAAE;YACV;UAAA;QAEH;MACF;MAED,IAAIkD,UAAU,CAACrE,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAI+E,KAAK,GAAG,IAAIxC,KAAK,CAAC+B,GAAG,CAACU,OAAO,CAAC;QAClC;QACA;QACAD,KAAK,CAACE,KAAK,GAAG,EAAE;QAChBZ,UAAU,CAACrE,GAAG,CAAC,GAAG+E,KAAK;MACxB;IACF,OAAM;MACLV,UAAU,CAACrE,GAAG,CAAC,GAAGsE,GAAG;IACtB;EACF;EACD,OAAOD,UAAU;AACnB;AAmBA,MAAMa,qBAAqB,gBAAGC,KAAK,CAACC,aAAa,CAA8B;EAC7EC,eAAe,EAAE;AAClB;AACD,IAAA7D,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXwD,qBAAqB,CAACI,WAAW,GAAG,gBAAgB;AACrD;AAOK,MAAAC,eAAe,gBAAGJ,KAAK,CAACC,aAAa,CAAwB,IAAII,GAAG,EAAE;AAC5E,IAAAhE,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACX6D,eAAe,CAACD,WAAW,GAAG,UAAU;AACzC;AAID;AAEA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;AAoBE;AACF,MAAMG,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,mBAAmB,GAAGP,KAAK,CAACM,gBAAgB,CAAC;AACnD,MAAME,UAAU,GAAG,WAAW;AAC9B,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,CAAC;AAC1C,MAAMG,MAAM,GAAG,OAAO;AACtB,MAAMC,SAAS,GAAGZ,KAAK,CAACW,MAAM,CAAC;AAE/B,SAASE,mBAAmBA,CAACC,EAAc;EACzC,IAAIP,mBAAmB,EAAE;IACvBA,mBAAmB,CAACO,EAAE,CAAC;EACxB,OAAM;IACLA,EAAE,EAAE;EACL;AACH;AAEA,SAASC,aAAaA,CAACD,EAAc;EACnC,IAAIL,aAAa,EAAE;IACjBA,aAAa,CAACK,EAAE,CAAC;EAClB,OAAM;IACLA,EAAE,EAAE;EACL;AACH;AASA,MAAME,QAAQ;EAOZC,YAAA;IANA,IAAM,CAAA3B,MAAA,GAAwC,SAAS;IAOrD,IAAI,CAAC4B,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC7C,IAAI,CAACD,OAAO,GAAItG,KAAK,IAAI;QACvB,IAAI,IAAI,CAACwE,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU;UACxB8B,OAAO,CAACtG,KAAK,CAAC;QACf;OACF;MACD,IAAI,CAACuG,MAAM,GAAIC,MAAM,IAAI;QACvB,IAAI,IAAI,CAAChC,MAAM,KAAK,SAAS,EAAE;UAC7B,IAAI,CAACA,MAAM,GAAG,UAAU;UACxB+B,MAAM,CAACC,MAAM,CAAC;QACf;OACF;IACH,CAAC,CAAC;EACJ;AACD;AAED;;AAEG;AACG,SAAUC,cAAcA,CAAAC,IAAA,EAIR;EAAA,IAJS;IAC7BC,eAAe;IACfC,MAAM;IACN3D;EACoB,IAAAyD,IAAA;EACpB,IAAI,CAAC3C,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAACF,MAAM,CAAC7C,KAAK,CAAC;EACxD,IAAI,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAG9B,KAAK,CAAC4B,QAAQ,EAAe;EACnE,IAAI,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGhC,KAAK,CAAC4B,QAAQ,CAA8B;IAC1E1B,eAAe,EAAE;EAClB,EAAC;EACF,IAAI,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGlC,KAAK,CAAC4B,QAAQ,EAAkB;EAChE,IAAI,CAACO,UAAU,EAAEC,aAAa,CAAC,GAAGpC,KAAK,CAAC4B,QAAQ,EAAkB;EAClE,IAAI,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGtC,KAAK,CAAC4B,QAAQ,EAIhD;EACJ,IAAIW,WAAW,GAAGvC,KAAK,CAACwC,MAAM,CAAmB,IAAInC,GAAG,EAAE,CAAC;EAC3D,IAAI;IAAEoC;EAAkB,CAAE,GAAG1E,MAAM,IAAI,EAAE;EAEzC,IAAI2E,oBAAoB,GAAG1C,KAAK,CAAC2C,WAAW,CACzC7B,EAAc,IAAI;IACjB,IAAI2B,kBAAkB,EAAE;MACtB5B,mBAAmB,CAACC,EAAE,CAAC;IACxB,OAAM;MACLA,EAAE,EAAE;IACL;EACH,CAAC,EACD,CAAC2B,kBAAkB,CAAC,CACrB;EAED,IAAIG,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC9B,CACEE,QAAqB,EAAAC,KAAA,KAMnB;IAAA,IALF;MACEC,eAAe;MACfC,SAAS,EAAEA,SAAS;MACpBC,kBAAkB,EAAEA;IACrB,IAAAH,KAAA;IAEDD,QAAQ,CAACK,QAAQ,CAAC5H,OAAO,CAAC,CAAC6H,OAAO,EAAEtI,GAAG,KAAI;MACzC,IAAIsI,OAAO,CAAC3D,IAAI,KAAKjC,SAAS,EAAE;QAC9BgF,WAAW,CAACa,OAAO,CAACC,GAAG,CAACxI,GAAG,EAAEsI,OAAO,CAAC3D,IAAI,CAAC;MAC3C;IACH,CAAC,CAAC;IACFuD,eAAe,CAACzH,OAAO,CAAET,GAAG,IAAK0H,WAAW,CAACa,OAAO,CAACE,MAAM,CAACzI,GAAG,CAAC,CAAC;IAEjE,IAAI0I,2BAA2B,GAC7B7B,MAAM,CAACjE,MAAM,IAAI,IAAI,IACrBiE,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,IAAI,IAAI,IAC9B,OAAO4F,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,CAAC0H,mBAAmB,KAAK,UAAU;IAElE;IACA;IACA,IAAI,CAACP,kBAAkB,IAAIM,2BAA2B,EAAE;MACtD,IAAIP,SAAS,EAAE;QACbjC,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC;MAC5C,OAAM;QACLH,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC;MACnD;MACD;IACD;IAED;IACA,IAAIG,SAAS,EAAE;MACb;MACAjC,aAAa,CAAC,MAAK;QACjB;QACA,IAAIoB,UAAU,EAAE;UACdF,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE;UAChCe,UAAU,CAACsB,cAAc,EAAE;QAC5B;QACDzB,YAAY,CAAC;UACX9B,eAAe,EAAE,IAAI;UACrB8C,SAAS,EAAE,IAAI;UACfU,eAAe,EAAET,kBAAkB,CAACS,eAAe;UACnDC,YAAY,EAAEV,kBAAkB,CAACU;QAClC,EAAC;MACJ,CAAC,CAAC;MAEF;MACA,IAAIC,CAAC,GAAGlC,MAAM,CAACjE,MAAO,CAAC3B,QAAQ,CAAC0H,mBAAmB,CAAC,MAAK;QACvDzC,aAAa,CAAC,MAAMY,YAAY,CAACkB,QAAQ,CAAC,CAAC;MAC7C,CAAC,CAAC;MAEF;MACAe,CAAC,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAK;QACtB/C,aAAa,CAAC,MAAK;UACjBmB,YAAY,CAAC3E,SAAS,CAAC;UACvB6E,aAAa,CAAC7E,SAAS,CAAC;UACxBuE,eAAe,CAACvE,SAAS,CAAC;UAC1ByE,YAAY,CAAC;YAAE9B,eAAe,EAAE;UAAK,CAAE,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFa,aAAa,CAAC,MAAMqB,aAAa,CAACwB,CAAC,CAAC,CAAC;MACrC;IACD;IAED;IACA,IAAIzB,UAAU,EAAE;MACd;MACA;MACAF,SAAS,IAAIA,SAAS,CAACb,OAAO,EAAE;MAChCe,UAAU,CAACsB,cAAc,EAAE;MAC3BnB,eAAe,CAAC;QACdzD,KAAK,EAAEgE,QAAQ;QACfa,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU;MAClC,EAAC;IACH,OAAM;MACL;MACA7B,eAAe,CAACe,QAAQ,CAAC;MACzBb,YAAY,CAAC;QACX9B,eAAe,EAAE,IAAI;QACrB8C,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAET,kBAAkB,CAACS,eAAe;QACnDC,YAAY,EAAEV,kBAAkB,CAACU;MAClC,EAAC;IACH;EACH,CAAC,EACD,CAACjC,MAAM,CAACjE,MAAM,EAAE0E,UAAU,EAAEF,SAAS,EAAEM,WAAW,EAAEG,oBAAoB,CAAC,CAC1E;EAED;EACA;EACA1C,KAAK,CAAC+D,eAAe,CAAC,MAAMrC,MAAM,CAACsC,SAAS,CAACpB,QAAQ,CAAC,EAAE,CAAClB,MAAM,EAAEkB,QAAQ,CAAC,CAAC;EAE3E;EACA;EACA5C,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIlC,SAAS,CAAC7B,eAAe,IAAI,CAAC6B,SAAS,CAACiB,SAAS,EAAE;MACrDd,YAAY,CAAC,IAAIlB,QAAQ,EAAQ,CAAC;IACnC;EACH,CAAC,EAAE,CAACe,SAAS,CAAC,CAAC;EAEf;EACA;EACA;EACA/B,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIhC,SAAS,IAAIJ,YAAY,IAAIH,MAAM,CAACjE,MAAM,EAAE;MAC9C,IAAIoF,QAAQ,GAAGhB,YAAY;MAC3B,IAAIqC,aAAa,GAAGjC,SAAS,CAACf,OAAO;MACrC,IAAIiB,UAAU,GAAGT,MAAM,CAACjE,MAAM,CAAC3B,QAAQ,CAAC0H,mBAAmB,CAAC,YAAW;QACrEd,oBAAoB,CAAC,MAAMf,YAAY,CAACkB,QAAQ,CAAC,CAAC;QAClD,MAAMqB,aAAa;MACrB,CAAC,CAAC;MACF/B,UAAU,CAAC0B,QAAQ,CAACC,OAAO,CAAC,MAAK;QAC/B5B,YAAY,CAAC3E,SAAS,CAAC;QACvB6E,aAAa,CAAC7E,SAAS,CAAC;QACxBuE,eAAe,CAACvE,SAAS,CAAC;QAC1ByE,YAAY,CAAC;UAAE9B,eAAe,EAAE;QAAK,CAAE,CAAC;MAC1C,CAAC,CAAC;MACFkC,aAAa,CAACD,UAAU,CAAC;IAC1B;EACH,CAAC,EAAE,CAACO,oBAAoB,EAAEb,YAAY,EAAEI,SAAS,EAAEP,MAAM,CAACjE,MAAM,CAAC,CAAC;EAElE;EACA;EACAuC,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IACEhC,SAAS,IACTJ,YAAY,IACZhD,KAAK,CAACsF,QAAQ,CAACtJ,GAAG,KAAKgH,YAAY,CAACsC,QAAQ,CAACtJ,GAAG,EAChD;MACAoH,SAAS,CAACb,OAAO,EAAE;IACpB;EACH,CAAC,EAAE,CAACa,SAAS,EAAEE,UAAU,EAAEtD,KAAK,CAACsF,QAAQ,EAAEtC,YAAY,CAAC,CAAC;EAEzD;EACA;EACA7B,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAI,CAAClC,SAAS,CAAC7B,eAAe,IAAImC,YAAY,EAAE;MAC9CP,eAAe,CAACO,YAAY,CAACxD,KAAK,CAAC;MACnCmD,YAAY,CAAC;QACX9B,eAAe,EAAE,IAAI;QACrB8C,SAAS,EAAE,KAAK;QAChBU,eAAe,EAAErB,YAAY,CAACqB,eAAe;QAC7CC,YAAY,EAAEtB,YAAY,CAACsB;MAC5B,EAAC;MACFrB,eAAe,CAAC/E,SAAS,CAAC;IAC3B;GACF,EAAE,CAACwE,SAAS,CAAC7B,eAAe,EAAEmC,YAAY,CAAC,CAAC;EAE7CrC,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB5H,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACLiF,eAAe,IAAI,IAAI,IAAI,CAACC,MAAM,CAAC3D,MAAM,CAACqG,mBAAmB,EAC7D,8DAA8D,GAC5D,kEAAkE,CACrE;IACD;IACA;GACD,EAAE,EAAE,CAAC;EAEN,IAAIC,SAAS,GAAGrE,KAAK,CAACsE,OAAO,CAAC,MAAgB;IAC5C,OAAO;MACLC,UAAU,EAAE7C,MAAM,CAAC6C,UAAU;MAC7BC,cAAc,EAAE9C,MAAM,CAAC8C,cAAc;MACrCC,EAAE,EAAGC,CAAC,IAAKhD,MAAM,CAACiD,QAAQ,CAACD,CAAC,CAAC;MAC7BE,IAAI,EAAEA,CAACC,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACpB6D,MAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;QAClBhG,KAAK;QACLiG,kBAAkB,EAAEjH,IAAI,IAAJ,gBAAAA,IAAI,CAAEiH;OAC3B,CAAC;MACJC,OAAO,EAAEA,CAACF,EAAE,EAAEhG,KAAK,EAAEhB,IAAI,KACvB6D,MAAM,CAACiD,QAAQ,CAACE,EAAE,EAAE;QAClBE,OAAO,EAAE,IAAI;QACblG,KAAK;QACLiG,kBAAkB,EAAEjH,IAAI,IAAJ,gBAAAA,IAAI,CAAEiH;OAC3B;KACJ;EACH,CAAC,EAAE,CAACpD,MAAM,CAAC,CAAC;EAEZ,IAAIhF,QAAQ,GAAGgF,MAAM,CAAChF,QAAQ,IAAI,GAAG;EAErC,IAAIsI,iBAAiB,GAAGhF,KAAK,CAACsE,OAAO,CACnC,OAAO;IACL5C,MAAM;IACN2C,SAAS;IACTY,MAAM,EAAE,KAAK;IACbvI;GACD,CAAC,EACF,CAACgF,MAAM,EAAE2C,SAAS,EAAE3H,QAAQ,CAAC,CAC9B;EAED,IAAIwI,YAAY,GAAGlF,KAAK,CAACsE,OAAO,CAC9B,OAAO;IACLa,oBAAoB,EAAEzD,MAAM,CAAC3D,MAAM,CAACoH;GACrC,CAAC,EACF,CAACzD,MAAM,CAAC3D,MAAM,CAACoH,oBAAoB,CAAC,CACrC;EAEDnF,KAAK,CAACiE,SAAS,CACb,MAAMmB,+BAAwB,CAACrH,MAAM,EAAE2D,MAAM,CAAC3D,MAAM,CAAC,EACrD,CAACA,MAAM,EAAE2D,MAAM,CAAC3D,MAAM,CAAC,CACxB;EAED;EACA;EACA;EACA;EACA;EACA;EACA,oBACEiC,KAAA,CAAAjE,aAAA,CAAAiE,KAAA,CAAAqF,QAAA,qBACErF,KAAA,CAAAjE,aAAA,CAACuJ,wBAAiB,CAACC,QAAQ,EAAC;IAAAzK,KAAK,EAAEkK;GAAiB,eAClDhF,KAAA,CAAAjE,aAAA,CAACyJ,6BAAsB,CAACD,QAAQ,EAAC;IAAAzK,KAAK,EAAE+D;GAAK,eAC3CmB,KAAC,CAAAjE,aAAA,CAAAqE,eAAe,CAACmF,QAAQ;IAACzK,KAAK,EAAEyH,WAAW,CAACa;GAAO,eAClDpD,KAAA,CAAAjE,aAAA,CAACgE,qBAAqB,CAACwF,QAAQ,EAAC;IAAAzK,KAAK,EAAEiH;EAAS,gBAC9C/B,KAAA,CAAAjE,aAAA,CAAC0J,MAAM;IACL/I,QAAQ,EAAEA,QAAQ;IAClByH,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAAC8G,aAAa;IACnCtB,SAAS,EAAEA,SAAS;IACpBtG,MAAM,EAAEmH;EAEP,GAAArG,KAAK,CAAC+G,WAAW,IAAIlE,MAAM,CAAC3D,MAAM,CAACqG,mBAAmB,gBACrDpE,KAAA,CAAAjE,aAAA,CAAC8J,kBAAkB,EACjB;IAAAjI,MAAM,EAAE8D,MAAM,CAAC9D,MAAM;IACrBG,MAAM,EAAE2D,MAAM,CAAC3D,MAAM;IACrBc,KAAK,EAAEA;GAAK,CACZ,GAEF4C,eACD,CACM,CACsB,CACR,CACK,CACP,EAC5B,IAAI,CACJ;AAEP;AAEA;AACA,MAAMoE,kBAAkB,gBAAG7F,KAAK,CAACpF,IAAI,CAACkL,UAAU,CAAC;AAEjD,SAASA,UAAUA,CAAAC,KAAA,EAQlB;EAAA,IARmB;IAClBnI,MAAM;IACNG,MAAM;IACNc;EAKD,IAAAkH,KAAA;EACC,OAAOC,oBAAa,CAACpI,MAAM,EAAEL,SAAS,EAAEsB,KAAK,EAAEd,MAAM,CAAC;AACxD;AASA;;AAEG;AACG,SAAUkI,aAAaA,CAAAC,KAAA,EAKR;EAAA,IALS;IAC5BxJ,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;IACNN;EACmB,IAAAyI,KAAA;EACnB,IAAIE,UAAU,GAAGpG,KAAK,CAACwC,MAAM,EAAkB;EAC/C,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;IAC9BgD,UAAU,CAAChD,OAAO,GAAGjF,oBAAoB,CAAC;MAAEV,MAAM;MAAE4I,QAAQ,EAAE;IAAI,CAAE,CAAC;EACtE;EAED,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO;EAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG;EACnB,EAAC;EACF,IAAI;IAAE1B;EAAkB,CAAE,GAAG1E,MAAM,IAAI,EAAE;EACzC,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;IAC3DJ,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC;EAC5B,CAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC;EAEDzC,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC;EAE1E5C,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEjE,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM;IACL/I,QAAQ,EAAEA,QAAQ;IAClByJ,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;IAC5ByH,SAAS,EAAEnG,OAAO;IAClBH,MAAM,EAAEA;EAAM,EACd;AAEN;AASA;;;AAGG;AACG,SAAUwI,UAAUA,CAAAC,KAAA,EAKR;EAAA,IALS;IACzB9J,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;IACNN;EACgB,IAAA+I,KAAA;EAChB,IAAIJ,UAAU,GAAGpG,KAAK,CAACwC,MAAM,EAAe;EAC5C,IAAI4D,UAAU,CAAChD,OAAO,IAAI,IAAI,EAAE;IAC9BgD,UAAU,CAAChD,OAAO,GAAGzE,iBAAiB,CAAC;MAAElB,MAAM;MAAE4I,QAAQ,EAAE;IAAI,CAAE,CAAC;EACnE;EAED,IAAInI,OAAO,GAAGkI,UAAU,CAAChD,OAAO;EAChC,IAAI,CAACvE,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG;EACnB,EAAC;EACF,IAAI;IAAE1B;EAAkB,CAAE,GAAG1E,MAAM,IAAI,EAAE;EACzC,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;IAC3DJ,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC;EAC5B,CAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC;EAEDzC,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC;EAE1E5C,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEjE,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM;IACL/I,QAAQ,EAAEA,QAAQ;IAClByJ,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;IAC5ByH,SAAS,EAAEnG,OAAO;IAClBH,MAAM,EAAEA;EAAM,EACd;AAEN;AASA;;;;;AAKG;AACH,SAAS0I,aAAaA,CAAAC,KAAA,EAKD;EAAA,IALE;IACrBhK,QAAQ;IACRyJ,QAAQ;IACRpI,MAAM;IACNG;EACmB,IAAAwI,KAAA;EACnB,IAAI,CAAC7H,KAAK,EAAE8C,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC;IACzChF,MAAM,EAAEsB,OAAO,CAACtB,MAAM;IACtBuH,QAAQ,EAAEjG,OAAO,CAACiG;EACnB,EAAC;EACF,IAAI;IAAE1B;EAAkB,CAAE,GAAG1E,MAAM,IAAI,EAAE;EACzC,IAAI6E,QAAQ,GAAG5C,KAAK,CAAC2C,WAAW,CAC7BE,QAAwD,IAAI;IAC3DJ,kBAAkB,IAAIlC,mBAAmB,GACrCA,mBAAmB,CAAC,MAAMoB,YAAY,CAACkB,QAAQ,CAAC,CAAC,GACjDlB,YAAY,CAACkB,QAAQ,CAAC;EAC5B,CAAC,EACD,CAAClB,YAAY,EAAEc,kBAAkB,CAAC,CACnC;EAEDzC,KAAK,CAAC+D,eAAe,CAAC,MAAM7F,OAAO,CAACoI,MAAM,CAAC1D,QAAQ,CAAC,EAAE,CAAC1E,OAAO,EAAE0E,QAAQ,CAAC,CAAC;EAE1E5C,KAAK,CAACiE,SAAS,CAAC,MAAMmB,+BAAwB,CAACrH,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEjE,oBACEiC,KAAA,CAAAjE,aAAA,CAAC0J,MAAM;IACL/I,QAAQ,EAAEA,QAAQ;IAClByJ,QAAQ,EAAEA,QAAQ;IAClBhC,QAAQ,EAAEtF,KAAK,CAACsF,QAAQ;IACxBuB,cAAc,EAAE7G,KAAK,CAACjC,MAAM;IAC5ByH,SAAS,EAAEnG,OAAO;IAClBH,MAAM,EAAEA;EAAM,EACd;AAEN;AAEA,IAAA1B,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXkK,aAAa,CAACtG,WAAW,GAAG,wBAAwB;AACrD;AAeD,MAAMwG,SAAS,GACb,OAAOlJ,MAAM,KAAK,WAAW,IAC7B,OAAOA,MAAM,CAAC3B,QAAQ,KAAK,WAAW,IACtC,OAAO2B,MAAM,CAAC3B,QAAQ,CAACC,aAAa,KAAK,WAAW;AAEtD,MAAM6K,kBAAkB,GAAG,+BAA+B;AAE1D;;AAEG;AACU,MAAAC,IAAI,gBAAG7G,KAAK,CAAC8G,UAAU,CAClC,SAASC,WAAWA,CAAAC,KAAA,EAalBC,GAAG;EAAA,IAZH;MACEC,OAAO;MACPC,QAAQ;MACRC,cAAc;MACdrC,OAAO;MACPlG,KAAK;MACL3E,MAAM;MACN2K,EAAE;MACFC,kBAAkB;MAClBuC;IACO,CACR,GAAAL,KAAA;IADIM,IAAI,GAAAC,6BAAA,CAAAP,KAAA,EAAAQ,SAAA;EAIT,IAAI;IAAE9K;EAAQ,CAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC;EAEtD;EACA,IAAIC,YAAY;EAChB,IAAIC,UAAU,GAAG,KAAK;EAEtB,IAAI,OAAO/C,EAAE,KAAK,QAAQ,IAAI+B,kBAAkB,CAACiB,IAAI,CAAChD,EAAE,CAAC,EAAE;IACzD;IACA8C,YAAY,GAAG9C,EAAE;IAEjB;IACA,IAAI8B,SAAS,EAAE;MACb,IAAI;QACF,IAAImB,UAAU,GAAG,IAAIC,GAAG,CAACtK,MAAM,CAAC0G,QAAQ,CAAC6D,IAAI,CAAC;QAC9C,IAAIC,SAAS,GAAGpD,EAAE,CAACqD,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIH,GAAG,CAACD,UAAU,CAACK,QAAQ,GAAGtD,EAAE,CAAC,GACjC,IAAIkD,GAAG,CAAClD,EAAE,CAAC;QACf,IAAIuD,IAAI,GAAGnL,aAAa,CAACgL,SAAS,CAACI,QAAQ,EAAE3L,QAAQ,CAAC;QAEtD,IAAIuL,SAAS,CAACK,MAAM,KAAKR,UAAU,CAACQ,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;UAC1D;UACAvD,EAAE,GAAGuD,IAAI,GAAGH,SAAS,CAACM,MAAM,GAAGN,SAAS,CAACO,IAAI;QAC9C,OAAM;UACLZ,UAAU,GAAG,IAAI;QAClB;OACF,CAAC,OAAO5L,CAAC,EAAE;QACV;QACAK,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,EACL,gBAAaqI,EAAE,iHACsC,CACtD;MACF;IACF;EACF;EAED;EACA,IAAImD,IAAI,GAAGS,OAAO,CAAC5D,EAAE,EAAE;IAAEsC;EAAU,EAAC;EAEpC,IAAIuB,eAAe,GAAGC,mBAAmB,CAAC9D,EAAE,EAAE;IAC5CE,OAAO;IACPlG,KAAK;IACL3E,MAAM;IACN4K,kBAAkB;IAClBqC,QAAQ;IACRE;EACD,EAAC;EACF,SAASuB,WAAWA,CAClBhP,KAAsD;IAEtD,IAAIsN,OAAO,EAAEA,OAAO,CAACtN,KAAK,CAAC;IAC3B,IAAI,CAACA,KAAK,CAACiP,gBAAgB,EAAE;MAC3BH,eAAe,CAAC9O,KAAK,CAAC;IACvB;EACH;EAEA;IACE;IACAoG,KAAA,CAAAjE,aAAA,MAAAiC,QAAA,KACMsJ,IAAI;MACRU,IAAI,EAAEL,YAAY,IAAIK,IAAI;MAC1Bd,OAAO,EAAEU,UAAU,IAAIR,cAAc,GAAGF,OAAO,GAAG0B,WAAW;MAC7D3B,GAAG,EAAEA,GAAG;MACR/M,MAAM,EAAEA;KAAM;EAAA;AAGpB,CAAC;AAGH,IAAAmC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXsK,IAAI,CAAC1G,WAAW,GAAG,MAAM;AAC1B;AAmBD;;AAEG;AACU,MAAA2I,OAAO,gBAAG9I,KAAK,CAAC8G,UAAU,CACrC,SAASiC,cAAcA,CAAAC,KAAA,EAYrB/B,GAAG;EAAA,IAXH;MACE,cAAc,EAAEgC,eAAe,GAAG,MAAM;MACxCC,aAAa,GAAG,KAAK;MACrBC,SAAS,EAAEC,aAAa,GAAG,EAAE;MAC7BC,GAAG,GAAG,KAAK;MACXC,KAAK,EAAEC,SAAS;MAChB1E,EAAE;MACFwC,cAAc;MACdlB;IAED,IAAA6C,KAAA;IADI1B,IAAI,GAAAC,6BAAA,CAAAyB,KAAA,EAAAQ,UAAA;EAIT,IAAIpB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;IAAEsC,QAAQ,EAAEG,IAAI,CAACH;EAAQ,CAAE,CAAC;EAC3D,IAAIhD,QAAQ,GAAGuF,WAAW,EAAE;EAC5B,IAAIC,WAAW,GAAG3J,KAAK,CAACyH,UAAU,CAACjC,6BAAsB,CAAC;EAC1D,IAAI;IAAEnB,SAAS;IAAE3H;EAAU,IAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC;EACjE,IAAIxH,eAAe,GACjByJ,WAAW,IAAI,IAAI;EACnB;EACA;EACAC,sBAAsB,CAACxB,IAAI,CAAC,IAC5Bf,cAAc,KAAK,IAAI;EAEzB,IAAIwC,UAAU,GAAGxF,SAAS,CAACG,cAAc,GACrCH,SAAS,CAACG,cAAc,CAAC4D,IAAI,CAAC,CAACC,QAAQ,GACvCD,IAAI,CAACC,QAAQ;EACjB,IAAIyB,gBAAgB,GAAG3F,QAAQ,CAACkE,QAAQ;EACxC,IAAI0B,oBAAoB,GACtBJ,WAAW,IAAIA,WAAW,CAACK,UAAU,IAAIL,WAAW,CAACK,UAAU,CAAC7F,QAAQ,GACpEwF,WAAW,CAACK,UAAU,CAAC7F,QAAQ,CAACkE,QAAQ,GACxC,IAAI;EAEV,IAAI,CAACa,aAAa,EAAE;IAClBY,gBAAgB,GAAGA,gBAAgB,CAACtQ,WAAW,EAAE;IACjDuQ,oBAAoB,GAAGA,oBAAoB,GACvCA,oBAAoB,CAACvQ,WAAW,EAAE,GAClC,IAAI;IACRqQ,UAAU,GAAGA,UAAU,CAACrQ,WAAW,EAAE;EACtC;EAED,IAAIuQ,oBAAoB,IAAIrN,QAAQ,EAAE;IACpCqN,oBAAoB,GAClB9M,aAAa,CAAC8M,oBAAoB,EAAErN,QAAQ,CAAC,IAAIqN,oBAAoB;EACxE;EAED;EACA;EACA;EACA;EACA;EACA,MAAME,gBAAgB,GACpBJ,UAAU,KAAK,GAAG,IAAIA,UAAU,CAACK,QAAQ,CAAC,GAAG,CAAC,GAC1CL,UAAU,CAACM,MAAM,GAAG,CAAC,GACrBN,UAAU,CAACM,MAAM;EACvB,IAAIC,QAAQ,GACVN,gBAAgB,KAAKD,UAAU,IAC9B,CAACR,GAAG,IACHS,gBAAgB,CAAC5B,UAAU,CAAC2B,UAAU,CAAC,IACvCC,gBAAgB,CAACO,MAAM,CAACJ,gBAAgB,CAAC,KAAK,GAAI;EAEtD,IAAIK,SAAS,GACXP,oBAAoB,IAAI,IAAI,KAC3BA,oBAAoB,KAAKF,UAAU,IACjC,CAACR,GAAG,IACHU,oBAAoB,CAAC7B,UAAU,CAAC2B,UAAU,CAAC,IAC3CE,oBAAoB,CAACM,MAAM,CAACR,UAAU,CAACM,MAAM,CAAC,KAAK,GAAI,CAAC;EAE9D,IAAII,WAAW,GAAG;IAChBH,QAAQ;IACRE,SAAS;IACTpK;GACD;EAED,IAAIsK,WAAW,GAAGJ,QAAQ,GAAGnB,eAAe,GAAG1L,SAAS;EAExD,IAAI4L,SAA6B;EACjC,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;IACvCD,SAAS,GAAGC,aAAa,CAACmB,WAAW,CAAC;EACvC,OAAM;IACL;IACA;IACA;IACA;IACA;IACApB,SAAS,GAAG,CACVC,aAAa,EACbgB,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAC1BE,SAAS,GAAG,SAAS,GAAG,IAAI,EAC5BpK,eAAe,GAAG,eAAe,GAAG,IAAI,CACzC,CACEuK,MAAM,CAACC,OAAO,CAAC,CACfC,IAAI,CAAC,GAAG,CAAC;EACb;EAED,IAAIrB,KAAK,GACP,OAAOC,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACgB,WAAW,CAAC,GAAGhB,SAAS;EAEtE,oBACEvJ,KAAA,CAAAjE,aAAA,CAAC8K,IAAI,EAAA7I,QAAA,KACCsJ,IAAI;IACM,gBAAAkD,WAAW;IACzBrB,SAAS,EAAEA,SAAS;IACpBlC,GAAG,EAAEA,GAAG;IACRqC,KAAK,EAAEA,KAAK;IACZzE,EAAE,EAAEA,EAAE;IACNwC,cAAc,EAAEA;GAEf,UAAOlB,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACoE,WAAW,CAAC,GAAGpE,QAAQ,CAC7D;AAEX,CAAC;AAGH,IAAA9J,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXuM,OAAO,CAAC3I,WAAW,GAAG,SAAS;AAChC;AAgGD;;;;;AAKG;AACI,MAAMyK,IAAI,gBAAG5K,KAAK,CAAC8G,UAAU,CAClC,CAAA+D,KAAA,EAeEC,YAAY,KACV;EAAA,IAfF;MACEC,UAAU;MACVpG,QAAQ;MACRyC,cAAc;MACdrC,OAAO;MACPlG,KAAK;MACLlC,MAAM,GAAGzD,aAAa;MACtB0D,MAAM;MACNoO,QAAQ;MACR7D,QAAQ;MACRrC,kBAAkB;MAClBuC;KAED,GAAAwD,KAAA;IADII,KAAK,GAAA1D,6BAAA,CAAAsD,KAAA,EAAAK,UAAA;EAIV,IAAIC,MAAM,GAAGC,SAAS,EAAE;EACxB,IAAIC,UAAU,GAAGC,aAAa,CAAC1O,MAAM,EAAE;IAAEuK;EAAU,EAAC;EACpD,IAAIoE,UAAU,GACZ5O,MAAM,CAACnD,WAAW,EAAE,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;EAEjD,IAAIgS,aAAa,GAA6C5R,KAAK,IAAI;IACrEoR,QAAQ,IAAIA,QAAQ,CAACpR,KAAK,CAAC;IAC3B,IAAIA,KAAK,CAACiP,gBAAgB,EAAE;IAC5BjP,KAAK,CAAC6R,cAAc,EAAE;IAEtB,IAAIC,SAAS,GAAI9R,KAAoC,CAAC+R,WAAW,CAC9DD,SAAqC;IAExC,IAAIE,YAAY,GACb,CAAAF,SAAS,IAAT,gBAAAA,SAAS,CAAE1O,YAAY,CAAC,YAAY,CAAgC,KACrEL,MAAM;IAERwO,MAAM,CAACO,SAAS,IAAI9R,KAAK,CAACiS,aAAa,EAAE;MACvCd,UAAU;MACVpO,MAAM,EAAEiP,YAAY;MACpBjH,QAAQ;MACRI,OAAO;MACPlG,KAAK;MACLsI,QAAQ;MACRrC,kBAAkB;MAClBuC;IACD,EAAC;GACH;EAED,oBACErH,KAAA,CAAAjE,aAAA,SAAAiC,QAAA;IACEiJ,GAAG,EAAE6D,YAAY;IACjBnO,MAAM,EAAE4O,UAAU;IAClB3O,MAAM,EAAEyO,UAAU;IAClBL,QAAQ,EAAE5D,cAAc,GAAG4D,QAAQ,GAAGQ;GAClC,EAAAP,KAAK,EACT;AAEN,CAAC;AAGH,IAAA5O,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXqO,IAAI,CAACzK,WAAW,GAAG,MAAM;AAC1B;AAOD;;;AAGG;SACa2L,iBAAiBA,CAAAC,MAAA,EAGR;EAAA,IAHS;IAChCC,MAAM;IACNC;EACuB,IAAAF,MAAA;EACvBG,oBAAoB,CAAC;IAAEF,MAAM;IAAEC;EAAU,CAAE,CAAC;EAC5C,OAAO,IAAI;AACb;AAEA,IAAA5P,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;EACXuP,iBAAiB,CAAC3L,WAAW,GAAG,mBAAmB;AACpD;AACD;AAEA;AACA;AACA;AAEA,IAAKgM,cAMJ;AAND,WAAKA,cAAc;EACjBA,cAAA,iDAA6C;EAC7CA,cAAA,2BAAuB;EACvBA,cAAA,yCAAqC;EACrCA,cAAA,6BAAyB;EACzBA,cAAA,qDAAiD;AACnD,CAAC,EANIA,cAAc,KAAdA,cAAc,GAMlB;AAED,IAAKC,mBAIJ;AAJD,WAAKA,mBAAmB;EACtBA,mBAAA,6BAAyB;EACzBA,mBAAA,+BAA2B;EAC3BA,mBAAA,iDAA6C;AAC/C,CAAC,EAJIA,mBAAmB,KAAnBA,mBAAmB,GAIvB;AAED;AAEA,SAASC,yBAAyBA,CAChCC,QAA8C;EAE9C,OAAUA,QAAQ;AACpB;AAEA,SAASC,oBAAoBA,CAACD,QAAwB;EACpD,IAAIE,GAAG,GAAGxM,KAAK,CAACyH,UAAU,CAACnC,wBAAiB,CAAC;EAC7C,CAAUkH,GAAG,GAAAnQ,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAb,eAAAkQ,gBAAS,QAAMJ,yBAAyB,CAACC,QAAQ,CAAC,IAAlDG,gBAAS;EACT,OAAOD,GAAG;AACZ;AAEA,SAASE,kBAAkBA,CAACJ,QAA6B;EACvD,IAAIzN,KAAK,GAAGmB,KAAK,CAACyH,UAAU,CAACjC,6BAAsB,CAAC;EACpD,CAAU3G,KAAK,GAAAxC,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAf,eAAAkQ,gBAAS,QAAQJ,yBAAyB,CAACC,QAAQ,CAAC,IAApDG,gBAAS;EACT,OAAO5N,KAAK;AACd;AAEA;AAEA;;;;AAIG;AACG,SAAU8J,mBAAmBA,CACjC9D,EAAM,EAAA8H,KAAA,EAeA;EAAA,IAdN;IACEzS,MAAM;IACN6K,OAAO,EAAE6H,WAAW;IACpB/N,KAAK;IACLiG,kBAAkB;IAClBqC,QAAQ;IACRE;yBAQE,EAAE,GAAAsF,KAAA;EAEN,IAAIhI,QAAQ,GAAGkI,WAAW,EAAE;EAC5B,IAAI1I,QAAQ,GAAGuF,WAAW,EAAE;EAC5B,IAAItB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;IAAEsC;EAAU,EAAC;EAE5C,OAAOnH,KAAK,CAAC2C,WAAW,CACrB/I,KAAsC,IAAI;IACzC,IAAIK,sBAAsB,CAACL,KAAK,EAAEM,MAAM,CAAC,EAAE;MACzCN,KAAK,CAAC6R,cAAc,EAAE;MAEtB;MACA;MACA,IAAI1G,OAAO,GACT6H,WAAW,KAAKrP,SAAS,GACrBqP,WAAW,GACXE,UAAU,CAAC3I,QAAQ,CAAC,KAAK2I,UAAU,CAAC1E,IAAI,CAAC;MAE/CzD,QAAQ,CAACE,EAAE,EAAE;QACXE,OAAO;QACPlG,KAAK;QACLiG,kBAAkB;QAClBqC,QAAQ;QACRE;MACD,EAAC;IACH;GACF,EACD,CACElD,QAAQ,EACRQ,QAAQ,EACRyD,IAAI,EACJwE,WAAW,EACX/N,KAAK,EACL3E,MAAM,EACN2K,EAAE,EACFC,kBAAkB,EAClBqC,QAAQ,EACRE,cAAc,CACf,CACF;AACH;AAEA;;;AAGG;AACG,SAAU0F,eAAeA,CAC7BC,WAAiC;EAEjC3Q,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,OAAOlC,eAAe,KAAK,WAAW,EACtC,uEACqE,iIACX,gDACX,CAChD;EAED,IAAI2S,sBAAsB,GAAGjN,KAAK,CAACwC,MAAM,CAACpI,kBAAkB,CAAC4S,WAAW,CAAC,CAAC;EAC1E,IAAIE,qBAAqB,GAAGlN,KAAK,CAACwC,MAAM,CAAC,KAAK,CAAC;EAE/C,IAAI2B,QAAQ,GAAGuF,WAAW,EAAE;EAC5B,IAAIrO,YAAY,GAAG2E,KAAK,CAACsE,OAAO,CAC9B;EACE;EACA;EACA;EACApJ,0BAA0B,CACxBiJ,QAAQ,CAACoE,MAAM,EACf2E,qBAAqB,CAAC9J,OAAO,GAAG,IAAI,GAAG6J,sBAAsB,CAAC7J,OAAO,CACtE,EACH,CAACe,QAAQ,CAACoE,MAAM,CAAC,CAClB;EAED,IAAI5D,QAAQ,GAAGkI,WAAW,EAAE;EAC5B,IAAIM,eAAe,GAAGnN,KAAK,CAAC2C,WAAW,CACrC,CAACyK,QAAQ,EAAEC,eAAe,KAAI;IAC5B,MAAMC,eAAe,GAAGlT,kBAAkB,CACxC,OAAOgT,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC/R,YAAY,CAAC,GAAG+R,QAAQ,CACnE;IACDF,qBAAqB,CAAC9J,OAAO,GAAG,IAAI;IACpCuB,QAAQ,CAAC,GAAG,GAAG2I,eAAe,EAAED,eAAe,CAAC;EAClD,CAAC,EACD,CAAC1I,QAAQ,EAAEtJ,YAAY,CAAC,CACzB;EAED,OAAO,CAACA,YAAY,EAAE8R,eAAe,CAAC;AACxC;AA2CA,SAASI,4BAA4BA,CAAA;EACnC,IAAI,OAAOzR,QAAQ,KAAK,WAAW,EAAE;IACnC,MAAM,IAAIsB,KAAK,CACb,mDAAmD,GACjD,8DAA8D,CACjE;EACF;AACH;AAEA,IAAIoQ,SAAS,GAAG,CAAC;AACjB,IAAIC,kBAAkB,GAAGA,CAAA,YAAWC,MAAM,CAAC,EAAEF,SAAS,CAAC,GAAI;AAE3D;;;AAGG;SACapC,SAASA,CAAA;EACvB,IAAI;IAAE1J;EAAM,CAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACwB,SAAS,CAAC;EAC/D,IAAI;IAAEjR;EAAQ,CAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAIkG,cAAc,GAAGC,iBAAU,EAAE;EAEjC,OAAO7N,KAAK,CAAC2C,WAAW,CACtB,UAACzI,MAAM,EAAE4T,OAAO,EAAS;IAAA,IAAhBA,OAAO;MAAPA,OAAO,GAAG,EAAE;IAAA;IACnBP,4BAA4B,EAAE;IAE9B,IAAI;MAAE3Q,MAAM;MAAED,MAAM;MAAEP,OAAO;MAAES,QAAQ;MAAEC;IAAI,CAAE,GAAGL,qBAAqB,CACrEvC,MAAM,EACNwC,QAAQ,CACT;IAED,IAAIoR,OAAO,CAACnJ,QAAQ,KAAK,KAAK,EAAE;MAC9B,IAAI9J,GAAG,GAAGiT,OAAO,CAAC/C,UAAU,IAAI0C,kBAAkB,EAAE;MACpD/L,MAAM,CAACqM,KAAK,CAAClT,GAAG,EAAE+S,cAAc,EAAEE,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;QAC1DkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;QAC9CjI,QAAQ;QACRC,IAAI;QACJyO,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;QACxDqR,WAAW,EAAEF,OAAO,CAAC1R,OAAO,IAAKA,OAAuB;QACxD4G,SAAS,EAAE8K,OAAO,CAAC9K;MACpB,EAAC;IACH,OAAM;MACLtB,MAAM,CAACiD,QAAQ,CAACmJ,OAAO,CAAClR,MAAM,IAAIA,MAAM,EAAE;QACxCkI,kBAAkB,EAAEgJ,OAAO,CAAChJ,kBAAkB;QAC9CjI,QAAQ;QACRC,IAAI;QACJyO,UAAU,EAAEuC,OAAO,CAACnR,MAAM,IAAKA,MAAyB;QACxDqR,WAAW,EAAEF,OAAO,CAAC1R,OAAO,IAAKA,OAAuB;QACxD2I,OAAO,EAAE+I,OAAO,CAAC/I,OAAO;QACxBlG,KAAK,EAAEiP,OAAO,CAACjP,KAAK;QACpBoP,WAAW,EAAEL,cAAc;QAC3B5K,SAAS,EAAE8K,OAAO,CAAC9K,SAAS;QAC5BqE,cAAc,EAAEyG,OAAO,CAACzG;MACzB,EAAC;IACH;GACF,EACD,CAAC3F,MAAM,EAAEhF,QAAQ,EAAEkR,cAAc,CAAC,CACnC;AACH;AAEA;AACA;AACM,SAAUtC,aAAaA,CAC3B1O,MAAe,EAAAsR,MAAA,EACsC;EAAA,IAArD;IAAE/G;0BAAiD,EAAE,GAAA+G,MAAA;EAErD,IAAI;IAAExR;EAAQ,CAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAIyG,YAAY,GAAGnO,KAAK,CAACyH,UAAU,CAAC2G,mBAAY,CAAC;EACjD,CAAUD,YAAY,GAAA9R,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAtBkQ,gBAAS,QAAe,kDAAkD,IAA1EA,gBAAS;EAET,IAAI,CAAC4B,KAAK,CAAC,GAAGF,YAAY,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5C;EACA;EACA,IAAInG,IAAI,GAAApK,QAAA,CAAQ,IAAAyL,eAAe,CAAC7M,MAAM,GAAGA,MAAM,GAAG,GAAG,EAAE;IAAEuK;EAAQ,CAAE,CAAC,CAAE;EAEtE;EACA;EACA;EACA,IAAIhD,QAAQ,GAAGuF,WAAW,EAAE;EAC5B,IAAI9M,MAAM,IAAI,IAAI,EAAE;IAClB;IACA;IACAwL,IAAI,CAACG,MAAM,GAAGpE,QAAQ,CAACoE,MAAM;IAE7B;IACA;IACA;IACA,IAAIiG,MAAM,GAAG,IAAIlU,eAAe,CAAC8N,IAAI,CAACG,MAAM,CAAC;IAC7C,IAAIkG,WAAW,GAAGD,MAAM,CAAC/S,MAAM,CAAC,OAAO,CAAC;IACxC,IAAIiT,kBAAkB,GAAGD,WAAW,CAACE,IAAI,CAAE1T,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;IAC1D,IAAIyT,kBAAkB,EAAE;MACtBF,MAAM,CAAClL,MAAM,CAAC,OAAO,CAAC;MACtBmL,WAAW,CAAChE,MAAM,CAAExP,CAAC,IAAKA,CAAC,CAAC,CAACK,OAAO,CAAEL,CAAC,IAAKuT,MAAM,CAAC9S,MAAM,CAAC,OAAO,EAAET,CAAC,CAAC,CAAC;MACtE,IAAI2T,EAAE,GAAGJ,MAAM,CAACK,QAAQ,EAAE;MAC1BzG,IAAI,CAACG,MAAM,GAAGqG,EAAE,GAAO,MAAAA,EAAE,GAAK,EAAE;IACjC;EACF;EAED,IAAI,CAAC,CAAChS,MAAM,IAAIA,MAAM,KAAK,GAAG,KAAKyR,KAAK,CAACS,KAAK,CAACC,KAAK,EAAE;IACpD3G,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,GACrBH,IAAI,CAACG,MAAM,CAACxD,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ;EACb;EAED;EACA;EACA;EACA;EACA,IAAIrI,QAAQ,KAAK,GAAG,EAAE;IACpB0L,IAAI,CAACC,QAAQ,GACXD,IAAI,CAACC,QAAQ,KAAK,GAAG,GAAG3L,QAAQ,GAAGsS,SAAS,CAAC,CAACtS,QAAQ,EAAE0L,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC1E;EAED,OAAOyE,UAAU,CAAC1E,IAAI,CAAC;AACzB;AAUA;AAEA;;;AAGG;SACa6G,UAAUA,CAAAC,MAAA,EAEF;EAAA,IAAAC,cAAA;EAAA,IAFgB;IACtCtU;0BACoB,EAAE,GAAAqU,MAAA;EACtB,IAAI;IAAExN;EAAM,CAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACiD,UAAU,CAAC;EAChE,IAAIvQ,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAACgD,UAAU,CAAC;EAC9D,IAAI7M,WAAW,GAAGvC,KAAK,CAACyH,UAAU,CAACrH,eAAe,CAAC;EACnD,IAAI0O,KAAK,GAAG9O,KAAK,CAACyH,UAAU,CAAC2G,mBAAY,CAAC;EAC1C,IAAIiB,OAAO,IAAAF,cAAA,GAAGL,KAAK,CAACR,OAAO,CAACQ,KAAK,CAACR,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC,qBAAvCgF,cAAA,CAAyCL,KAAK,CAACQ,EAAE;EAE/D,CAAU/M,WAAW,GAAAlG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAArBkQ,gBAAS,8DAATA,gBAAS;EACT,CAAUqC,KAAK,GAAAzS,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAfkQ,gBAAS,2DAATA,gBAAS;EACT,EACE4C,OAAO,IAAI,IAAI,IAAAhT,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADjBkQ,gBAAS,gFAATA,gBAAS;EAKT;EACA;EACA;EACA,IAAI8C,UAAU,GAAG3O,SAAS,GAAGA,SAAS,EAAE,GAAG,EAAE;EAC7C,IAAI,CAACmK,UAAU,EAAEyE,aAAa,CAAC,GAAGxP,KAAK,CAAC4B,QAAQ,CAAS/G,GAAG,IAAI0U,UAAU,CAAC;EAC3E,IAAI1U,GAAG,IAAIA,GAAG,KAAKkQ,UAAU,EAAE;IAC7ByE,aAAa,CAAC3U,GAAG,CAAC;EACnB,OAAM,IAAI,CAACkQ,UAAU,EAAE;IACtB;IACAyE,aAAa,CAAC/B,kBAAkB,EAAE,CAAC;EACpC;EAED;EACAzN,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnBvC,MAAM,CAAC+N,UAAU,CAAC1E,UAAU,CAAC;IAC7B,OAAO,MAAK;MACV;MACA;MACA;MACArJ,MAAM,CAACgO,aAAa,CAAC3E,UAAU,CAAC;KACjC;EACH,CAAC,EAAE,CAACrJ,MAAM,EAAEqJ,UAAU,CAAC,CAAC;EAExB;EACA,IAAI4E,IAAI,GAAG3P,KAAK,CAAC2C,WAAW,CAC1B,CAACqF,IAAY,EAAEnK,IAA8B,KAAI;IAC/C,CAAUwR,OAAO,GAAAhT,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAjBkQ,gBAAS,QAAU,yCAAyC,IAA5DA,gBAAS;IACT/K,MAAM,CAACqM,KAAK,CAAChD,UAAU,EAAEsE,OAAO,EAAErH,IAAI,EAAEnK,IAAI,CAAC;GAC9C,EACD,CAACkN,UAAU,EAAEsE,OAAO,EAAE3N,MAAM,CAAC,CAC9B;EAED,IAAIkO,UAAU,GAAGxE,SAAS,EAAE;EAC5B,IAAID,MAAM,GAAGnL,KAAK,CAAC2C,WAAW,CAC5B,CAACzI,MAAM,EAAE2D,IAAI,KAAI;IACf+R,UAAU,CAAC1V,MAAM,EAAA8D,QAAA,KACZH,IAAI;MACP8G,QAAQ,EAAE,KAAK;MACfoG;IAAU,EACX,CAAC;EACJ,CAAC,EACD,CAACA,UAAU,EAAE6E,UAAU,CAAC,CACzB;EAED,IAAIC,WAAW,GAAG7P,KAAK,CAACsE,OAAO,CAAC,MAAK;IACnC,IAAIuL,WAAW,gBAAG7P,KAAK,CAAC8G,UAAU,CAChC,CAACmE,KAAK,EAAEhE,GAAG,KAAI;MACb,oBACEjH,KAAC,CAAAjE,aAAA,CAAA6O,IAAI,EAAA5M,QAAA,KAAKiN,KAAK;QAAEtG,QAAQ,EAAE,KAAK;QAAEoG,UAAU,EAAEA,UAAU;QAAE9D,GAAG,EAAEA;MAAG,GAAI;IAE1E,CAAC,CACF;IACD,IAAA5K,OAAA,CAAAC,GAAA,CAAAC,QAAA,KAAa;MACXsT,WAAW,CAAC1P,WAAW,GAAG,cAAc;IACzC;IACD,OAAO0P,WAAW;EACpB,CAAC,EAAE,CAAC9E,UAAU,CAAC,CAAC;EAEhB;EACA,IAAI5H,OAAO,GAAGtE,KAAK,CAACqE,QAAQ,CAAC4M,GAAG,CAAC/E,UAAU,CAAC,IAAIgF,YAAY;EAC5D,IAAIvQ,IAAI,GAAG+C,WAAW,CAACuN,GAAG,CAAC/E,UAAU,CAAC;EACtC,IAAIiF,qBAAqB,GAAGhQ,KAAK,CAACsE,OAAO,CACvC,MAAAtG,QAAA;IACE4M,IAAI,EAAEiF,WAAW;IACjB1E,MAAM;IACNwE;EAAI,GACDxM,OAAO;IACV3D;EAAI,EACJ,EACF,CAACqQ,WAAW,EAAE1E,MAAM,EAAEwE,IAAI,EAAExM,OAAO,EAAE3D,IAAI,CAAC,CAC3C;EAED,OAAOwQ,qBAAqB;AAC9B;AAEA;;;AAGG;SACaC,WAAWA,CAAA;EACzB,IAAIpR,KAAK,GAAG6N,kBAAkB,CAACN,mBAAmB,CAAC8D,WAAW,CAAC;EAC/D,OAAO3V,KAAK,CAAC4V,IAAI,CAACtR,KAAK,CAACqE,QAAQ,CAACjE,OAAO,EAAE,CAAC,CAACjE,GAAG,CAACoV,MAAA;IAAA,IAAC,CAACvV,GAAG,EAAEsI,OAAO,CAAC,GAAAiN,MAAA;IAAA,OAAApS,QAAA,KAC1DmF,OAAO;MACVtI;IAAG;EAAA,CACH,CAAC;AACL;AAEA,MAAMwV,8BAA8B,GAAG,+BAA+B;AACtE,IAAIC,oBAAoB,GAA2B,EAAE;AAErD;;AAEG;AACH,SAASpE,oBAAoBA,CAAAqE,MAAA,EAMvB;EAAA,IANwB;IAC5BvE,MAAM;IACNC;0BAIE,EAAE,GAAAsE,MAAA;EACJ,IAAI;IAAE7O;EAAM,CAAE,GAAG6K,oBAAoB,CAACJ,cAAc,CAACqE,oBAAoB,CAAC;EAC1E,IAAI;IAAEC,qBAAqB;IAAE3L;EAAoB,IAAG4H,kBAAkB,CACpEN,mBAAmB,CAACoE,oBAAoB,CACzC;EACD,IAAI;IAAE9T;EAAQ,CAAE,GAAGsD,KAAK,CAACyH,UAAU,CAACC,wBAAiB,CAAC;EACtD,IAAIvD,QAAQ,GAAGuF,WAAW,EAAE;EAC5B,IAAI4E,OAAO,GAAGoC,UAAU,EAAE;EAC1B,IAAI1G,UAAU,GAAG2G,aAAa,EAAE;EAEhC;EACA3Q,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnBxG,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,QAAQ;IAC3C,OAAO,MAAK;MACVnT,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM;KAC1C;GACF,EAAE,EAAE,CAAC;EAEN;EACAC,WAAW,CACT7Q,KAAK,CAAC2C,WAAW,CAAC,MAAK;IACrB,IAAIqH,UAAU,CAACnL,KAAK,KAAK,MAAM,EAAE;MAC/B,IAAIhE,GAAG,GAAG,CAACmR,MAAM,GAAGA,MAAM,CAAC7H,QAAQ,EAAEmK,OAAO,CAAC,GAAG,IAAI,KAAKnK,QAAQ,CAACtJ,GAAG;MACrEyV,oBAAoB,CAACzV,GAAG,CAAC,GAAG4C,MAAM,CAACqT,OAAO;IAC3C;IACD,IAAI;MACFC,cAAc,CAACC,OAAO,CACpB/E,UAAU,IAAIoE,8BAA8B,EAC5CY,IAAI,CAACC,SAAS,CAACZ,oBAAoB,CAAC,CACrC;KACF,CAAC,OAAO1Q,KAAK,EAAE;MACdvD,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAC,cAAO,CACL,KAAK,wGAC+FoD,KAAK,OAAI,CAC9G;IACF;IACDnC,MAAM,CAACS,OAAO,CAAC0S,iBAAiB,GAAG,MAAM;EAC3C,CAAC,EAAE,CAAC3E,UAAU,EAAED,MAAM,EAAEhC,UAAU,CAACnL,KAAK,EAAEsF,QAAQ,EAAEmK,OAAO,CAAC,CAAC,CAC9D;EAED;EACA,IAAI,OAAOxS,QAAQ,KAAK,WAAW,EAAE;IACnC;IACAkE,KAAK,CAAC+D,eAAe,CAAC,MAAK;MACzB,IAAI;QACF,IAAIoN,gBAAgB,GAAGJ,cAAc,CAACK,OAAO,CAC3CnF,UAAU,IAAIoE,8BAA8B,CAC7C;QACD,IAAIc,gBAAgB,EAAE;UACpBb,oBAAoB,GAAGW,IAAI,CAACI,KAAK,CAACF,gBAAgB,CAAC;QACpD;OACF,CAAC,OAAOnV,CAAC,EAAE;QACV;MAAA;IAEJ,CAAC,EAAE,CAACiQ,UAAU,CAAC,CAAC;IAEhB;IACA;IACAjM,KAAK,CAAC+D,eAAe,CAAC,MAAK;MACzB,IAAIuN,qBAAqB,GACvBtF,MAAM,IAAItP,QAAQ,KAAK,GAAG,GACtB,CAACyH,QAAQ,EAAEmK,OAAO,KAChBtC,MAAM;MAAA;MACJhO,QAAA,KAEKmG,QAAQ;QACXkE,QAAQ,EACNpL,aAAa,CAACkH,QAAQ,CAACkE,QAAQ,EAAE3L,QAAQ,CAAC,IAC1CyH,QAAQ,CAACkE;OAEb,GAAAiG,OAAO,CACR,GACHtC,MAAM;MACZ,IAAIuF,wBAAwB,GAAG7P,MAAM,IAAN,gBAAAA,MAAM,CAAE8P,uBAAuB,CAC5DlB,oBAAoB,EACpB,MAAM7S,MAAM,CAACqT,OAAO,EACpBQ,qBAAqB,CACtB;MACD,OAAO,MAAMC,wBAAwB,IAAIA,wBAAwB,EAAE;KACpE,EAAE,CAAC7P,MAAM,EAAEhF,QAAQ,EAAEsP,MAAM,CAAC,CAAC;IAE9B;IACA;IACAhM,KAAK,CAAC+D,eAAe,CAAC,MAAK;MACzB;MACA,IAAI0M,qBAAqB,KAAK,KAAK,EAAE;QACnC;MACD;MAED;MACA,IAAI,OAAOA,qBAAqB,KAAK,QAAQ,EAAE;QAC7ChT,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAEhB,qBAAqB,CAAC;QACzC;MACD;MAED;MACA,IAAItM,QAAQ,CAACqE,IAAI,EAAE;QACjB,IAAIkJ,EAAE,GAAG5V,QAAQ,CAAC6V,cAAc,CAC9BC,kBAAkB,CAACzN,QAAQ,CAACqE,IAAI,CAAC+F,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3C;QACD,IAAImD,EAAE,EAAE;UACNA,EAAE,CAACG,cAAc,EAAE;UACnB;QACD;MACF;MAED;MACA,IAAI/M,kBAAkB,KAAK,IAAI,EAAE;QAC/B;MACD;MAED;MACArH,MAAM,CAACgU,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;KACtB,EAAE,CAACtN,QAAQ,EAAEsM,qBAAqB,EAAE3L,kBAAkB,CAAC,CAAC;EAC1D;AACH;AAIA;;;;;;;AAOG;AACa,SAAAgN,eAAeA,CAC7BC,QAA2C,EAC3CjE,OAA+B;EAE/B,IAAI;IAAEkE;EAAO,CAAE,GAAGlE,OAAO,IAAI,EAAE;EAC/B9N,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;MAAEA;IAAS,IAAGzU,SAAS;IACpDE,MAAM,CAACwU,gBAAgB,CAAC,cAAc,EAAEF,QAAQ,EAAElU,IAAI,CAAC;IACvD,OAAO,MAAK;MACVJ,MAAM,CAACyU,mBAAmB,CAAC,cAAc,EAAEH,QAAQ,EAAElU,IAAI,CAAC;KAC3D;EACH,CAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACzB;AAEA;;;;;;;AAOG;AACH,SAASnB,WAAWA,CAClBkB,QAA6C,EAC7CjE,OAA+B;EAE/B,IAAI;IAAEkE;EAAO,CAAE,GAAGlE,OAAO,IAAI,EAAE;EAC/B9N,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIpG,IAAI,GAAGmU,OAAO,IAAI,IAAI,GAAG;MAAEA;IAAS,IAAGzU,SAAS;IACpDE,MAAM,CAACwU,gBAAgB,CAAC,UAAU,EAAEF,QAAQ,EAAElU,IAAI,CAAC;IACnD,OAAO,MAAK;MACVJ,MAAM,CAACyU,mBAAmB,CAAC,UAAU,EAAEH,QAAQ,EAAElU,IAAI,CAAC;KACvD;EACH,CAAC,EAAE,CAACkU,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACzB;AAEA;;;;;;;AAOG;AACH,SAASG,SAASA,CAAAC,MAAA,EAMjB;EAAA,IANkB;IACjBC,IAAI;IACJxS;EAID,IAAAuS,MAAA;EACC,IAAIE,OAAO,GAAGC,UAAU,CAACF,IAAI,CAAC;EAE9BrS,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,EAAE;MAC/B,IAAI2T,OAAO,GAAG/U,MAAM,CAACgV,OAAO,CAAC5S,OAAO,CAAC;MACrC,IAAI2S,OAAO,EAAE;QACX;QACA;QACA;QACAE,UAAU,CAACJ,OAAO,CAACE,OAAO,EAAE,CAAC,CAAC;MAC/B,OAAM;QACLF,OAAO,CAACK,KAAK,EAAE;MAChB;IACF;EACH,CAAC,EAAE,CAACL,OAAO,EAAEzS,OAAO,CAAC,CAAC;EAEtBG,KAAK,CAACiE,SAAS,CAAC,MAAK;IACnB,IAAIqO,OAAO,CAACzT,KAAK,KAAK,SAAS,IAAI,CAACwT,IAAI,EAAE;MACxCC,OAAO,CAACK,KAAK,EAAE;IAChB;EACH,CAAC,EAAE,CAACL,OAAO,EAAED,IAAI,CAAC,CAAC;AACrB;AAIA;;;;;;;AAOG;AACH,SAASzI,sBAAsBA,CAC7B/E,EAAM,EACNhH,IAAA,EAA6C;EAAA,IAA7CA,IAAA;IAAAA,IAAA,GAA2C,EAAE;EAAA;EAE7C,IAAIkE,SAAS,GAAG/B,KAAK,CAACyH,UAAU,CAAC1H,qBAAqB,CAAC;EAEvD,EACEgC,SAAS,IAAI,IAAI,IAAA1F,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBADnBkQ,gBAAS,CAEP,8FAAuF,GACrF,mEAAmE,IAHvEA,gBAAS;EAMT,IAAI;IAAE/P;EAAQ,CAAE,GAAG6P,oBAAoB,CACrCJ,cAAc,CAACvC,sBAAsB,CACtC;EACD,IAAIxB,IAAI,GAAGqB,eAAe,CAAC5E,EAAE,EAAE;IAAEsC,QAAQ,EAAEtJ,IAAI,CAACsJ;EAAQ,CAAE,CAAC;EAC3D,IAAI,CAACpF,SAAS,CAAC7B,eAAe,EAAE;IAC9B,OAAO,KAAK;EACb;EAED,IAAI0S,WAAW,GACb3V,aAAa,CAAC8E,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ,EAAE3L,QAAQ,CAAC,IAC3DqF,SAAS,CAAC2B,eAAe,CAAC2E,QAAQ;EACpC,IAAIwK,QAAQ,GACV5V,aAAa,CAAC8E,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ,EAAE3L,QAAQ,CAAC,IACxDqF,SAAS,CAAC4B,YAAY,CAAC0E,QAAQ;EAEjC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OACEyK,SAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEwK,QAAQ,CAAC,IAAI,IAAI,IAC1CC,SAAS,CAAC1K,IAAI,CAACC,QAAQ,EAAEuK,WAAW,CAAC,IAAI,IAAI;AAEjD;AAIA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}