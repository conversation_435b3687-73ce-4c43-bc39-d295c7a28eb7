{"ast": null, "code": "function memo(callback) {\n  let result;\n  return () => {\n    if (result === undefined) result = callback();\n    return result;\n  };\n}\nexport { memo };", "map": {"version": 3, "names": ["memo", "callback", "result", "undefined"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/utils/memo.mjs"], "sourcesContent": ["function memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,QAAQ,EAAE;EACpB,IAAIC,MAAM;EACV,OAAO,MAAM;IACT,IAAIA,MAAM,KAAKC,SAAS,EACpBD,MAAM,GAAGD,QAAQ,CAAC,CAAC;IACvB,OAAOC,MAAM;EACjB,CAAC;AACL;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}