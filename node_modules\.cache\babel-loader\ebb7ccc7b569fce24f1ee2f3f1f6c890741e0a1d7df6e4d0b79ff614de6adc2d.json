{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n_c = SidebarContainer;\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n_c2 = SidebarHeader;\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c3 = Logo;\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c4 = LogoText;\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n_c5 = Navigation;\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n_c6 = NavItem;\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n_c7 = NavLink;\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n_c8 = NavIcon;\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n_c9 = NavText;\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n_c0 = NavBadge;\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n_c1 = SidebarFooter;\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n_c10 = UserProfile;\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n_c11 = UserAvatar;\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n_c12 = UserInfo;\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n_c13 = UserName;\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n_c14 = UserStatus;\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n_c15 = ToggleButton;\nconst Sidebar = ({\n  collapsed,\n  onToggle\n}) => {\n  _s();\n  var _user$username;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    getStats\n  } = useGameResources();\n  const stats = getStats();\n  const navigationItems = [{\n    path: '/',\n    icon: '🏠',\n    label: '主页',\n    badge: null\n  }, {\n    path: '/modules',\n    icon: '📦',\n    label: '游戏文件',\n    badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n  }, {\n    path: '/store',\n    icon: '🛒',\n    label: '试玩和购买',\n    badge: null\n  }, {\n    path: '/settings',\n    icon: '⚙️',\n    label: '系统选项',\n    badge: null\n  }];\n\n  // 管理员专用菜单项\n  const adminNavigationItems = [{\n    path: '/admin/users',\n    icon: '👥',\n    label: '用户管理',\n    badge: null\n  }, {\n    path: '/admin/modules',\n    icon: '🔧',\n    label: '模组管理',\n    badge: null\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  const handleUserClick = () => {\n    // 可以打开用户菜单或导航到用户设置页面\n    console.log('User profile clicked');\n  };\n  return /*#__PURE__*/_jsxDEV(SidebarContainer, {\n    collapsed: collapsed,\n    initial: {\n      x: -240\n    },\n    animate: {\n      x: 0\n    },\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: \"D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        collapsed: collapsed,\n        children: \"DCS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      children: [navigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n        whileHover: {\n          scale: 1.02\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        children: /*#__PURE__*/_jsxDEV(NavLink, {\n          active: location.pathname === item.path,\n          onClick: () => handleNavigation(item.path),\n          children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavText, {\n            collapsed: collapsed,\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), item.badge && /*#__PURE__*/_jsxDEV(NavBadge, {\n            collapsed: collapsed,\n            children: item.badge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)\n      }, item.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this)), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0 8px 0',\n            padding: '0 20px',\n            fontSize: '12px',\n            color: '#666',\n            opacity: collapsed ? 0 : 1,\n            transition: 'opacity 0.3s ease'\n          },\n          children: \"\\u7BA1\\u7406\\u5458\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), adminNavigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n          whileHover: {\n            scale: 1.02\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          children: /*#__PURE__*/_jsxDEV(NavLink, {\n            active: location.pathname === item.path,\n            onClick: () => handleNavigation(item.path),\n            children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(NavText, {\n              collapsed: collapsed,\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this), item.badge && /*#__PURE__*/_jsxDEV(NavBadge, {\n              collapsed: collapsed,\n              children: item.badge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 17\n          }, this)\n        }, item.path, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidebarFooter, {\n      children: /*#__PURE__*/_jsxDEV(UserProfile, {\n        onClick: handleUserClick,\n        children: [/*#__PURE__*/_jsxDEV(UserAvatar, {\n          children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          collapsed: collapsed,\n          children: [/*#__PURE__*/_jsxDEV(UserName, {\n            children: (user === null || user === void 0 ? void 0 : user.username) || 'Guest'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserStatus, {\n            children: \"\\u5728\\u7EBF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n      onClick: onToggle,\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: collapsed ? '→' : '←'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 249,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"f/TFxXB2hVOLSqwg3WD3c2HKcaQ=\", false, function () {\n  return [useLocation, useNavigate, useAuth, useGameResources];\n});\n_c16 = Sidebar;\nexport default Sidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"SidebarContainer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"Navigation\");\n$RefreshReg$(_c6, \"NavItem\");\n$RefreshReg$(_c7, \"NavLink\");\n$RefreshReg$(_c8, \"NavIcon\");\n$RefreshReg$(_c9, \"NavText\");\n$RefreshReg$(_c0, \"NavBadge\");\n$RefreshReg$(_c1, \"SidebarFooter\");\n$RefreshReg$(_c10, \"UserProfile\");\n$RefreshReg$(_c11, \"UserAvatar\");\n$RefreshReg$(_c12, \"UserInfo\");\n$RefreshReg$(_c13, \"UserName\");\n$RefreshReg$(_c14, \"UserStatus\");\n$RefreshReg$(_c15, \"ToggleButton\");\n$RefreshReg$(_c16, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useLocation", "useNavigate", "styled", "motion", "useAuth", "useGameResources", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SidebarContainer", "aside", "props", "collapsed", "theme", "components", "sidebar", "collapsedWidth", "width", "colors", "border", "primary", "_c", "SidebarHeader", "div", "secondary", "_c2", "Logo", "_c3", "LogoText", "fontSizes", "lg", "fontWeights", "bold", "text", "_c4", "Navigation", "nav", "_c5", "NavItem", "_c6", "NavLink", "button", "active", "sm", "medium", "normal", "_c7", "NavIcon", "span", "_c8", "NavText", "_c9", "NavBadge", "_c0", "SidebarFooter", "_c1", "UserProfile", "_c10", "UserAvatar", "_c11", "UserInfo", "_c12", "UserName", "_c13", "UserStatus", "xs", "tertiary", "_c14", "ToggleButton", "background", "_c15", "Sidebar", "onToggle", "_s", "_user$username", "location", "navigate", "user", "logout", "getStats", "stats", "navigationItems", "path", "icon", "label", "badge", "hasUpdates", "adminNavigationItems", "handleNavigation", "handleUserClick", "console", "log", "initial", "x", "animate", "transition", "duration", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "whileHover", "scale", "whileTap", "pathname", "onClick", "role", "style", "margin", "padding", "fontSize", "color", "opacity", "username", "char<PERSON>t", "toUpperCase", "_c16", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/components/layout/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGameResources } from '../../context/GameResourceContext';\n\nconst SidebarContainer = styled(motion.aside)`\n  width: ${props => props.collapsed ? props.theme.components.sidebar.collapsedWidth : props.theme.components.sidebar.width};\n  height: 100%;\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid ${props => props.theme.colors.border.primary};\n  display: flex;\n  flex-direction: column;\n  transition: width 0.3s ease;\n  backdrop-filter: blur(10px);\n`;\n\nconst SidebarHeader = styled.div`\n  padding: 20px;\n  border-bottom: 1px solid ${props => props.theme.colors.border.secondary};\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst Logo = styled.div`\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst LogoText = styled.div`\n  font-size: ${props => props.theme.fontSizes.lg};\n  font-weight: ${props => props.theme.fontWeights.bold};\n  color: ${props => props.theme.colors.text.primary};\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst Navigation = styled.nav`\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n`;\n\nconst NavItem = styled(motion.div)`\n  margin: 4px 12px;\n  border-radius: 8px;\n  overflow: hidden;\n`;\n\nconst NavLink = styled.button`\n  width: 100%;\n  padding: 12px 16px;\n  background: ${props => props.active ? 'rgba(255, 107, 53, 0.1)' : 'transparent'};\n  border: none;\n  border-left: 3px solid ${props => props.active ? props.theme.colors.primary : 'transparent'};\n  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.active ? props.theme.fontWeights.medium : props.theme.fontWeights.normal};\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  \n  &:hover {\n    background: rgba(255, 107, 53, 0.05);\n    color: ${props => props.theme.colors.text.primary};\n  }\n`;\n\nconst NavIcon = styled.span`\n  font-size: 18px;\n  flex-shrink: 0;\n  width: 20px;\n  text-align: center;\n`;\n\nconst NavText = styled.span`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  white-space: nowrap;\n  overflow: hidden;\n`;\n\nconst NavBadge = styled.span`\n  background: ${props => props.theme.colors.primary};\n  color: white;\n  font-size: 10px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  margin-left: auto;\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n`;\n\nconst SidebarFooter = styled.div`\n  padding: 20px;\n  border-top: 1px solid ${props => props.theme.colors.border.secondary};\n`;\n\nconst UserProfile = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst UserAvatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #4a9eff 0%, #6bb0ff 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: bold;\n  color: white;\n  flex-shrink: 0;\n`;\n\nconst UserInfo = styled.div`\n  opacity: ${props => props.collapsed ? 0 : 1};\n  transition: opacity 0.3s ease;\n  overflow: hidden;\n`;\n\nconst UserName = styled.div`\n  font-size: ${props => props.theme.fontSizes.sm};\n  font-weight: ${props => props.theme.fontWeights.medium};\n  color: ${props => props.theme.colors.text.primary};\n  white-space: nowrap;\n`;\n\nconst UserStatus = styled.div`\n  font-size: ${props => props.theme.fontSizes.xs};\n  color: ${props => props.theme.colors.text.tertiary};\n  white-space: nowrap;\n`;\n\nconst ToggleButton = styled(motion.button)`\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  background: ${props => props.theme.colors.background.secondary};\n  border: 1px solid ${props => props.theme.colors.border.primary};\n  border-radius: 50%;\n  color: ${props => props.theme.colors.text.secondary};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: ${props => props.theme.colors.primary};\n    color: white;\n    border-color: ${props => props.theme.colors.primary};\n  }\n`;\n\nconst Sidebar = ({ collapsed, onToggle }) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const { getStats } = useGameResources();\n  \n  const stats = getStats();\n\n  const navigationItems = [\n    {\n      path: '/',\n      icon: '🏠',\n      label: '主页',\n      badge: null\n    },\n    {\n      path: '/modules',\n      icon: '📦',\n      label: '游戏文件',\n      badge: stats.hasUpdates > 0 ? stats.hasUpdates : null\n    },\n    {\n      path: '/store',\n      icon: '🛒',\n      label: '试玩和购买',\n      badge: null\n    },\n    {\n      path: '/settings',\n      icon: '⚙️',\n      label: '系统选项',\n      badge: null\n    }\n  ];\n\n  // 管理员专用菜单项\n  const adminNavigationItems = [\n    {\n      path: '/admin/users',\n      icon: '👥',\n      label: '用户管理',\n      badge: null\n    },\n    {\n      path: '/admin/modules',\n      icon: '🔧',\n      label: '模组管理',\n      badge: null\n    }\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  const handleUserClick = () => {\n    // 可以打开用户菜单或导航到用户设置页面\n    console.log('User profile clicked');\n  };\n\n  return (\n    <SidebarContainer\n      collapsed={collapsed}\n      initial={{ x: -240 }}\n      animate={{ x: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <SidebarHeader>\n        <Logo>D</Logo>\n        <LogoText collapsed={collapsed}>DCS</LogoText>\n      </SidebarHeader>\n\n      <Navigation>\n        {navigationItems.map((item) => (\n          <NavItem\n            key={item.path}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <NavLink\n              active={location.pathname === item.path}\n              onClick={() => handleNavigation(item.path)}\n            >\n              <NavIcon>{item.icon}</NavIcon>\n              <NavText collapsed={collapsed}>{item.label}</NavText>\n              {item.badge && (\n                <NavBadge collapsed={collapsed}>{item.badge}</NavBadge>\n              )}\n            </NavLink>\n          </NavItem>\n        ))}\n        \n        {/* 管理员专用菜单 */}\n        {user?.role === 'admin' && (\n          <>\n            <div style={{\n              margin: '16px 0 8px 0',\n              padding: '0 20px',\n              fontSize: '12px',\n              color: '#666',\n              opacity: collapsed ? 0 : 1,\n              transition: 'opacity 0.3s ease'\n            }}>\n              管理员功能\n            </div>\n            {adminNavigationItems.map((item) => (\n              <NavItem\n                key={item.path}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <NavLink\n                  active={location.pathname === item.path}\n                  onClick={() => handleNavigation(item.path)}\n                >\n                  <NavIcon>{item.icon}</NavIcon>\n                  <NavText collapsed={collapsed}>{item.label}</NavText>\n                  {item.badge && (\n                    <NavBadge collapsed={collapsed}>{item.badge}</NavBadge>\n                  )}\n                </NavLink>\n              </NavItem>\n            ))}\n          </>\n        )}\n      </Navigation>\n\n      <SidebarFooter>\n        <UserProfile onClick={handleUserClick}>\n          <UserAvatar>\n            {user?.username?.charAt(0).toUpperCase() || 'U'}\n          </UserAvatar>\n          <UserInfo collapsed={collapsed}>\n            <UserName>{user?.username || 'Guest'}</UserName>\n            <UserStatus>在线</UserStatus>\n          </UserInfo>\n        </UserProfile>\n      </SidebarFooter>\n\n      <ToggleButton\n        onClick={onToggle}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {collapsed ? '→' : '←'}\n      </ToggleButton>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,gBAAgB,GAAGR,MAAM,CAACC,MAAM,CAACQ,KAAK,CAAC;AAC7C,WAAWC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACC,cAAc,GAAGL,KAAK,CAACE,KAAK,CAACC,UAAU,CAACC,OAAO,CAACE,KAAK;AAC1H;AACA;AACA,4BAA4BN,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AACtE;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIZ,gBAAgB;AAWtB,MAAMa,aAAa,GAAGrB,MAAM,CAACsB,GAAG;AAChC;AACA,6BAA6BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACzE;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,aAAa;AAQnB,MAAMI,IAAI,GAAGzB,MAAM,CAACsB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAZID,IAAI;AAcV,MAAME,QAAQ,GAAG3B,MAAM,CAACsB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACC,EAAE;AAChD,iBAAiBnB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACC,IAAI;AACtD,WAAWrB,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GARIN,QAAQ;AAUd,MAAMO,UAAU,GAAGlC,MAAM,CAACmC,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,OAAO,GAAGrC,MAAM,CAACC,MAAM,CAACqB,GAAG,CAAC;AAClC;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GAJID,OAAO;AAMb,MAAME,OAAO,GAAGvC,MAAM,CAACwC,MAAM;AAC7B;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG,yBAAyB,GAAG,aAAa;AACjF;AACA,2BAA2B/B,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAG,aAAa;AAC7F,WAAWT,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO,GAAGT,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACjG,eAAeb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAAC+B,MAAM,GAAG/B,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM,GAAGjC,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACc,MAAM;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAalC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACrD;AACA,CAAC;AAAC0B,GAAA,GApBIN,OAAO;AAsBb,MAAMO,OAAO,GAAG9C,MAAM,CAAC+C,IAAI;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,OAAO;AAOb,MAAMG,OAAO,GAAGjD,MAAM,CAAC+C,IAAI;AAC3B,aAAarC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA;AACA,CAAC;AAACuC,GAAA,GALID,OAAO;AAOb,MAAME,QAAQ,GAAGnD,MAAM,CAAC+C,IAAI;AAC5B,gBAAgBrC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACnD;AACA;AACA;AACA;AACA;AACA,aAAaT,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA,CAAC;AAACyC,GAAA,GATID,QAAQ;AAWd,MAAME,aAAa,GAAGrD,MAAM,CAACsB,GAAG;AAChC;AACA,0BAA0BZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACK,SAAS;AACtE,CAAC;AAAC+B,GAAA,GAHID,aAAa;AAKnB,MAAME,WAAW,GAAGvD,MAAM,CAACsB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAbID,WAAW;AAejB,MAAME,UAAU,GAAGzD,MAAM,CAACsB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAZID,UAAU;AAchB,MAAME,QAAQ,GAAG3D,MAAM,CAACsB,GAAG;AAC3B,aAAaZ,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC;AAC7C;AACA;AACA,CAAC;AAACiD,IAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAG7D,MAAM,CAACsB,GAAG;AAC3B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACc,EAAE;AAChD,iBAAiBhC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACkB,WAAW,CAACa,MAAM;AACxD,WAAWjC,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACb,OAAO;AACnD;AACA,CAAC;AAAC2C,IAAA,GALID,QAAQ;AAOd,MAAME,UAAU,GAAG/D,MAAM,CAACsB,GAAG;AAC7B,eAAeZ,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACgB,SAAS,CAACoC,EAAE;AAChD,WAAWtD,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACiC,QAAQ;AACpD;AACA,CAAC;AAACC,IAAA,GAJIH,UAAU;AAMhB,MAAMI,YAAY,GAAGnE,MAAM,CAACC,MAAM,CAACuC,MAAM,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB9B,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACmD,UAAU,CAAC7C,SAAS;AAChE,sBAAsBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACC,MAAM,CAACC,OAAO;AAChE;AACA,WAAWT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACe,IAAI,CAACT,SAAS;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkBb,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACrD;AACA,oBAAoBT,KAAK,IAAIA,KAAK,CAACE,KAAK,CAACK,MAAM,CAACE,OAAO;AACvD;AACA,CAAC;AAACkD,IAAA,GAvBIF,YAAY;AAyBlB,MAAMG,OAAO,GAAGA,CAAC;EAAE3D,SAAS;EAAE4D;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,cAAA;EAC3C,MAAMC,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAC9B,MAAM6E,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6E,IAAI;IAAEC;EAAO,CAAC,GAAG3E,OAAO,CAAC,CAAC;EAClC,MAAM;IAAE4E;EAAS,CAAC,GAAG3E,gBAAgB,CAAC,CAAC;EAEvC,MAAM4E,KAAK,GAAGD,QAAQ,CAAC,CAAC;EAExB,MAAME,eAAe,GAAG,CACtB;IACEC,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAEL,KAAK,CAACM,UAAU,GAAG,CAAC,GAAGN,KAAK,CAACM,UAAU,GAAG;EACnD,CAAC,EACD;IACEJ,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAME,oBAAoB,GAAG,CAC3B;IACEL,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,gBAAgB,GAAIN,IAAI,IAAK;IACjCN,QAAQ,CAACM,IAAI,CAAC;EAChB,CAAC;EAED,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC,CAAC;EAED,oBACErF,OAAA,CAACG,gBAAgB;IACfG,SAAS,EAAEA,SAAU;IACrBgF,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9B3F,OAAA,CAACgB,aAAa;MAAA2E,QAAA,gBACZ3F,OAAA,CAACoB,IAAI;QAAAuE,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACd/F,OAAA,CAACsB,QAAQ;QAAChB,SAAS,EAAEA,SAAU;QAAAqF,QAAA,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEhB/F,OAAA,CAAC6B,UAAU;MAAA8D,QAAA,GACRhB,eAAe,CAACqB,GAAG,CAAEC,IAAI,iBACxBjG,OAAA,CAACgC,OAAO;QAENkE,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAAAR,QAAA,eAE1B3F,OAAA,CAACkC,OAAO;UACNE,MAAM,EAAEiC,QAAQ,CAACgC,QAAQ,KAAKJ,IAAI,CAACrB,IAAK;UACxC0B,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACe,IAAI,CAACrB,IAAI,CAAE;UAAAe,QAAA,gBAE3C3F,OAAA,CAACyC,OAAO;YAAAkD,QAAA,EAAEM,IAAI,CAACpB;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9B/F,OAAA,CAAC4C,OAAO;YAACtC,SAAS,EAAEA,SAAU;YAAAqF,QAAA,EAAEM,IAAI,CAACnB;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACpDE,IAAI,CAAClB,KAAK,iBACT/E,OAAA,CAAC8C,QAAQ;YAACxC,SAAS,EAAEA,SAAU;YAAAqF,QAAA,EAAEM,IAAI,CAAClB;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CACvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC,GAbLE,IAAI,CAACrB,IAAI;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcP,CACV,CAAC,EAGD,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgC,IAAI,MAAK,OAAO,iBACrBvG,OAAA,CAAAE,SAAA;QAAAyF,QAAA,gBACE3F,OAAA;UAAKwG,KAAK,EAAE;YACVC,MAAM,EAAE,cAAc;YACtBC,OAAO,EAAE,QAAQ;YACjBC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAEvG,SAAS,GAAG,CAAC,GAAG,CAAC;YAC1BmF,UAAU,EAAE;UACd,CAAE;UAAAE,QAAA,EAAC;QAEH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EACLd,oBAAoB,CAACe,GAAG,CAAEC,IAAI,iBAC7BjG,OAAA,CAACgC,OAAO;UAENkE,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAAAR,QAAA,eAE1B3F,OAAA,CAACkC,OAAO;YACNE,MAAM,EAAEiC,QAAQ,CAACgC,QAAQ,KAAKJ,IAAI,CAACrB,IAAK;YACxC0B,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACe,IAAI,CAACrB,IAAI,CAAE;YAAAe,QAAA,gBAE3C3F,OAAA,CAACyC,OAAO;cAAAkD,QAAA,EAAEM,IAAI,CAACpB;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9B/F,OAAA,CAAC4C,OAAO;cAACtC,SAAS,EAAEA,SAAU;cAAAqF,QAAA,EAAEM,IAAI,CAACnB;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACpDE,IAAI,CAAClB,KAAK,iBACT/E,OAAA,CAAC8C,QAAQ;cAACxC,SAAS,EAAEA,SAAU;cAAAqF,QAAA,EAAEM,IAAI,CAAClB;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CACvD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC,GAbLE,IAAI,CAACrB,IAAI;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcP,CACV,CAAC;MAAA,eACF,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAEb/F,OAAA,CAACgD,aAAa;MAAA2C,QAAA,eACZ3F,OAAA,CAACkD,WAAW;QAACoD,OAAO,EAAEnB,eAAgB;QAAAQ,QAAA,gBACpC3F,OAAA,CAACoD,UAAU;UAAAuC,QAAA,EACR,CAAApB,IAAI,aAAJA,IAAI,wBAAAH,cAAA,GAAJG,IAAI,CAAEuC,QAAQ,cAAA1C,cAAA,uBAAdA,cAAA,CAAgB2C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACb/F,OAAA,CAACsD,QAAQ;UAAChD,SAAS,EAAEA,SAAU;UAAAqF,QAAA,gBAC7B3F,OAAA,CAACwD,QAAQ;YAAAmC,QAAA,EAAE,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuC,QAAQ,KAAI;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAChD/F,OAAA,CAAC0D,UAAU;YAAAiC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEhB/F,OAAA,CAAC8D,YAAY;MACXwC,OAAO,EAAEpC,QAAS;MAClBgC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MAAAR,QAAA,EAExBrF,SAAS,GAAG,GAAG,GAAG;IAAG;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEvB,CAAC;AAAC5B,EAAA,CApJIF,OAAO;EAAA,QACMxE,WAAW,EACXC,WAAW,EACHG,OAAO,EACXC,gBAAgB;AAAA;AAAAmH,IAAA,GAJjChD,OAAO;AAsJb,eAAeA,OAAO;AAAC,IAAAlD,EAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAiD,IAAA;AAAAC,YAAA,CAAAnG,EAAA;AAAAmG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}