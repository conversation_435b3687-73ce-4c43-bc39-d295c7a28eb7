{"ast": null, "code": "import { useRef, useState, useCallback } from 'react';\nimport { wrap } from './wrap.mjs';\n\n/**\n * Cycles through a series of visual properties. Can be used to toggle between or cycle through animations. It works similar to `useState` in React. It is provided an initial array of possible states, and returns an array of two arguments.\n *\n * An index value can be passed to the returned `cycle` function to cycle to a specific index.\n *\n * ```jsx\n * import * as React from \"react\"\n * import { motion, useCycle } from \"framer-motion\"\n *\n * export const MyComponent = () => {\n *   const [x, cycleX] = useCycle(0, 50, 100)\n *\n *   return (\n *     <motion.div\n *       animate={{ x: x }}\n *       onTap={() => cycleX()}\n *      />\n *    )\n * }\n * ```\n *\n * @param items - items to cycle through\n * @returns [currentState, cycleState]\n *\n * @public\n */\nfunction useCycle(...items) {\n  const index = useRef(0);\n  const [item, setItem] = useState(items[index.current]);\n  const runCycle = useCallback(next => {\n    index.current = typeof next !== \"number\" ? wrap(0, items.length, index.current + 1) : next;\n    setItem(items[index.current]);\n  },\n  // The array will change on each call, but by putting items.length at\n  // the front of this array, we guarantee the dependency comparison will match up\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [items.length, ...items]);\n  return [item, runCycle];\n}\nexport { useCycle };", "map": {"version": 3, "names": ["useRef", "useState", "useCallback", "wrap", "useCycle", "items", "index", "item", "setItem", "current", "runCycle", "next", "length"], "sources": ["D:/Test/Battle Launcher/node_modules/framer-motion/dist/es/utils/use-cycle.mjs"], "sourcesContent": ["import { useRef, useState, useCallback } from 'react';\nimport { wrap } from './wrap.mjs';\n\n/**\n * Cycles through a series of visual properties. Can be used to toggle between or cycle through animations. It works similar to `useState` in React. It is provided an initial array of possible states, and returns an array of two arguments.\n *\n * An index value can be passed to the returned `cycle` function to cycle to a specific index.\n *\n * ```jsx\n * import * as React from \"react\"\n * import { motion, useCycle } from \"framer-motion\"\n *\n * export const MyComponent = () => {\n *   const [x, cycleX] = useCycle(0, 50, 100)\n *\n *   return (\n *     <motion.div\n *       animate={{ x: x }}\n *       onTap={() => cycleX()}\n *      />\n *    )\n * }\n * ```\n *\n * @param items - items to cycle through\n * @returns [currentState, cycleState]\n *\n * @public\n */\nfunction useCycle(...items) {\n    const index = useRef(0);\n    const [item, setItem] = useState(items[index.current]);\n    const runCycle = useCallback((next) => {\n        index.current =\n            typeof next !== \"number\"\n                ? wrap(0, items.length, index.current + 1)\n                : next;\n        setItem(items[index.current]);\n    }, \n    // The array will change on each call, but by putting items.length at\n    // the front of this array, we guarantee the dependency comparison will match up\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [items.length, ...items]);\n    return [item, runCycle];\n}\n\nexport { useCycle };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACrD,SAASC,IAAI,QAAQ,YAAY;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC,GAAGC,KAAK,EAAE;EACxB,MAAMC,KAAK,GAAGN,MAAM,CAAC,CAAC,CAAC;EACvB,MAAM,CAACO,IAAI,EAAEC,OAAO,CAAC,GAAGP,QAAQ,CAACI,KAAK,CAACC,KAAK,CAACG,OAAO,CAAC,CAAC;EACtD,MAAMC,QAAQ,GAAGR,WAAW,CAAES,IAAI,IAAK;IACnCL,KAAK,CAACG,OAAO,GACT,OAAOE,IAAI,KAAK,QAAQ,GAClBR,IAAI,CAAC,CAAC,EAAEE,KAAK,CAACO,MAAM,EAAEN,KAAK,CAACG,OAAO,GAAG,CAAC,CAAC,GACxCE,IAAI;IACdH,OAAO,CAACH,KAAK,CAACC,KAAK,CAACG,OAAO,CAAC,CAAC;EACjC,CAAC;EACD;EACA;EACA;EACA,CAACJ,KAAK,CAACO,MAAM,EAAE,GAAGP,KAAK,CAAC,CAAC;EACzB,OAAO,CAACE,IAAI,EAAEG,QAAQ,CAAC;AAC3B;AAEA,SAASN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}