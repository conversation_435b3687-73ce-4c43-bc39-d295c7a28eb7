{"ast": null, "code": "var _jsxFileName = \"D:\\\\Test\\\\Battle Launcher\\\\src\\\\components\\\\layout\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Sidebar from './Sidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LayoutContainer = styled.div`\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n`;\n_c = LayoutContainer;\nconst MainContent = styled(motion.main)`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  position: relative;\n`;\n_c2 = MainContent;\nconst ContentArea = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding: 24px;\n  \n  /* 自定义滚动条 */\n  &::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.05);\n    border-radius: 4px;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(255, 107, 53, 0.6);\n    border-radius: 4px;\n    transition: background 0.3s ease;\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: rgba(255, 107, 53, 0.8);\n  }\n`;\n_c3 = ContentArea;\nconst BackgroundPattern = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0.03;\n  background-image: \n    radial-gradient(circle at 25% 25%, #ff6b35 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, #4a9eff 0%, transparent 50%);\n  background-size: 400px 400px;\n  background-position: 0 0, 200px 200px;\n  pointer-events: none;\n`;\n_c4 = BackgroundPattern;\nconst Layout = ({\n  children\n}) => {\n  _s();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n  return /*#__PURE__*/_jsxDEV(LayoutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      collapsed: sidebarCollapsed,\n      onToggle: toggleSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      initial: {\n        opacity: 0,\n        x: 20\n      },\n      animate: {\n        opacity: 1,\n        x: 0\n      },\n      transition: {\n        duration: 0.3\n      },\n      children: [/*#__PURE__*/_jsxDEV(BackgroundPattern, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: children\n          }, window.location.pathname, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"RBWGVlQtqnwMoScoawcFwyZtGjQ=\");\n_c5 = Layout;\nexport default Layout;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"LayoutContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"ContentArea\");\n$RefreshReg$(_c4, \"BackgroundPattern\");\n$RefreshReg$(_c5, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "styled", "motion", "AnimatePresence", "Sidebar", "jsxDEV", "_jsxDEV", "LayoutContainer", "div", "_c", "MainContent", "main", "_c2", "ContentArea", "_c3", "BackgroundPattern", "_c4", "Layout", "children", "_s", "sidebarCollapsed", "setSidebarCollapsed", "toggleSidebar", "collapsed", "onToggle", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "x", "animate", "transition", "duration", "mode", "y", "exit", "window", "location", "pathname", "_c5", "$RefreshReg$"], "sources": ["D:/Test/Battle Launcher/src/components/layout/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Sidebar from './Sidebar';\n\nconst LayoutContainer = styled.div`\n  display: flex;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n`;\n\nconst MainContent = styled(motion.main)`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n  position: relative;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding: 24px;\n  \n  /* 自定义滚动条 */\n  &::-webkit-scrollbar {\n    width: 8px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.05);\n    border-radius: 4px;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(255, 107, 53, 0.6);\n    border-radius: 4px;\n    transition: background 0.3s ease;\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: rgba(255, 107, 53, 0.8);\n  }\n`;\n\nconst BackgroundPattern = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  opacity: 0.03;\n  background-image: \n    radial-gradient(circle at 25% 25%, #ff6b35 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, #4a9eff 0%, transparent 50%);\n  background-size: 400px 400px;\n  background-position: 0 0, 200px 200px;\n  pointer-events: none;\n`;\n\nconst Layout = ({ children }) => {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed);\n  };\n\n  return (\n    <LayoutContainer>\n      <Sidebar \n        collapsed={sidebarCollapsed} \n        onToggle={toggleSidebar}\n      />\n      \n      <MainContent\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <BackgroundPattern />\n        \n        <ContentArea>\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={window.location.pathname}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {children}\n            </motion.div>\n          </AnimatePresence>\n        </ContentArea>\n      </MainContent>\n    </LayoutContainer>\n  );\n};\n\nexport default Layout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,OAAO,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,eAAe,GAAGN,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,WAAW,GAAGT,MAAM,CAACC,MAAM,CAACS,IAAI,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,WAAW;AASjB,MAAMG,WAAW,GAAGZ,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAzBID,WAAW;AA2BjB,MAAME,iBAAiB,GAAGd,MAAM,CAACO,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAbID,iBAAiB;AAevB,MAAME,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1BD,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,oBACEd,OAAA,CAACC,eAAe;IAAAW,QAAA,gBACdZ,OAAA,CAACF,OAAO;MACNmB,SAAS,EAAEH,gBAAiB;MAC5BI,QAAQ,EAAEF;IAAc;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAEFtB,OAAA,CAACI,WAAW;MACVmB,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAhB,QAAA,gBAE9BZ,OAAA,CAACS,iBAAiB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAErBtB,OAAA,CAACO,WAAW;QAAAK,QAAA,eACVZ,OAAA,CAACH,eAAe;UAACgC,IAAI,EAAC,MAAM;UAAAjB,QAAA,eAC1BZ,OAAA,CAACJ,MAAM,CAACM,GAAG;YAETqB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAG,CAAE;YAC/BJ,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEP,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BH,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAhB,QAAA,EAE7BA;UAAQ,GANJoB,MAAM,CAACC,QAAQ,CAACC,QAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEtB,CAAC;AAACT,EAAA,CArCIF,MAAM;AAAAwB,GAAA,GAANxB,MAAM;AAuCZ,eAAeA,MAAM;AAAC,IAAAR,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAAjC,EAAA;AAAAiC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}