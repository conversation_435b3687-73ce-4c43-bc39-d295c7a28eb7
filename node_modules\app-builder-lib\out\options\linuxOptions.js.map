{"version": 3, "file": "linuxOptions.js", "sourceRoot": "", "sources": ["../../src/options/linuxOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PlatformSpecificBuildOptions, TargetConfigType, TargetSpecificOptions } from \"../index\"\n\nexport interface LinuxConfiguration extends CommonLinuxOptions, PlatformSpecificBuildOptions {\n  /**\n   * Target package type: list of `AppImage`, `flatpak`, `snap`, `deb`, `rpm`, `freebsd`, `pacman`, `p5p`, `apk`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`.\n   *\n   * electron-builder [docker image](/multi-platform-build#docker) can be used to build Linux targets on any platform.\n   *\n   * Please [do not put an AppImage into another archive](https://github.com/probonopd/AppImageKit/wiki/Creating-AppImages#common-mistake) like a .zip or .tar.gz.\n   * @default AppImage\n   */\n  readonly target?: TargetConfigType\n\n  /**\n   * The maintainer. Defaults to [author](/configuration/configuration#Metadata-author).\n   */\n  readonly maintainer?: string | null\n\n  /**\n   * The vendor. Defaults to [author](/configuration/configuration#Metadata-author).\n   */\n  readonly vendor?: string | null\n\n  /**\n   * The path to icon set directory or one png file, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory. The icon filename must contain the size (e.g. 32x32.png) of the icon.\n   * By default will be generated automatically based on the macOS icns file.\n   */\n  readonly icon?: string\n\n  /**\n   * backward compatibility + to allow specify fpm-only category for all possible fpm targets in one place\n   * @private\n   */\n  readonly packageCategory?: string | null\n}\n\nexport interface CommonLinuxOptions {\n  /**\n   * The [short description](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Description).\n   */\n  readonly synopsis?: string | null\n\n  /**\n   * As [description](/configuration/configuration#Metadata-description) from application package.json, but allows you to specify different for Linux.\n   */\n  readonly description?: string | null\n\n  /**\n   * The [application category](https://specifications.freedesktop.org/menu-spec/latest/apa.html#main-category-registry).\n   */\n  readonly category?: string | null\n\n  /**\n   * The mime types in addition to specified in the file associations. Use it if you don't want to register a new mime type, but reuse existing.\n   */\n  readonly mimeTypes?: Array<string> | null\n\n  /**\n   * The [Desktop file](https://developer.gnome.org/documentation/guidelines/maintainer/integrating.html#desktop-files) entries (name to value).\n   */\n  readonly desktop?: any | null\n\n  /**\n   * The executable parameters. Pass to executableName\n   */\n  readonly executableArgs?: Array<string> | null\n}\n\n// fpm-only specific options\nexport interface LinuxTargetSpecificOptions extends CommonLinuxOptions, TargetSpecificOptions {\n  /**\n   * Package dependencies.\n   */\n  readonly depends?: Array<string> | null\n\n  /**\n   * The compression type.\n   * @default xz\n   */\n  readonly compression?: \"gz\" | \"bzip2\" | \"xz\" | \"lzo\" | null\n\n  readonly icon?: string\n\n  /**\n   * The package category.\n   */\n  readonly packageCategory?: string | null\n\n  /**\n   * The name of the package.\n   */\n  readonly packageName?: string | null\n\n  readonly vendor?: string | null\n  readonly maintainer?: string | null\n\n  readonly afterInstall?: string | null\n  readonly afterRemove?: string | null\n\n  /**\n   * *Advanced only* The [fpm](https://github.com/jordansissel/fpm/wiki#usage) options.\n   *\n   * Example: `[\"--before-install=build/deb-preinstall.sh\", \"--after-upgrade=build/deb-postinstall.sh\"]`\n   */\n  readonly fpm?: Array<string> | null\n}\nexport interface DebOptions extends LinuxTargetSpecificOptions {\n  /**\n   * Package dependencies. Defaults to `[\"gconf2\", \"gconf-service\", \"libnotify4\", \"libappindicator1\", \"libxtst6\", \"libnss3\"]`.\n   * If need to support Debian, `libappindicator1` should be removed, it is [deprecated in Debian](https://bugs.debian.org/cgi-bin/bugreport.cgi?bug=895037).\n   * If need to support KDE, `gconf2` and `gconf-service` should be removed as it's no longer used by GNOME](https://packages.debian.org/bullseye/gconf2).\n   */\n  readonly depends?: Array<string> | null\n\n  /**\n   * The [package category](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Section).\n   */\n  readonly packageCategory?: string | null\n\n  /**\n   * The [Priority](https://www.debian.org/doc/debian-policy/ch-controlfields.html#s-f-Priority) attribute.\n   */\n  readonly priority?: string | null\n}\n\nexport interface AppImageOptions extends CommonLinuxOptions, TargetSpecificOptions {\n  /**\n   * The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). Only plain text is supported.\n   */\n  readonly license?: string | null\n}\n\nexport interface FlatpakOptions extends CommonLinuxOptions, TargetSpecificOptions {\n  /**\n   * The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). Only plain text is supported.\n   */\n  readonly license?: string | null\n\n  /**\n   * The name of the runtime that the application uses. Defaults to `org.freedesktop.Platform`.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly runtime?: string\n\n  /**\n   * The version of the runtime that the application uses. Defaults to `20.08`.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly runtimeVersion?: string\n\n  /**\n   * The name of the development runtime that the application builds with. Defaults to `org.freedesktop.Sdk`.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly sdk?: string\n\n  /**\n   * Start with the files from the specified application. This can be used to create applications that extend another application.\n   * Defaults to [org.electronjs.Electron2.BaseApp](https://github.com/flathub/org.electronjs.Electron2.BaseApp).\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly base?: string\n\n  /**\n   * Use this specific version of the application specified in base. Defaults to `20.08`.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly baseVersion?: string\n\n  /**\n   * The branch to use when exporting the application. Defaults to `master`.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly branch?: string\n\n  /**\n   * An array of arguments passed to the flatpak build-finish command. Defaults to:\n   * ```json\n   * [\n   *   // Wayland/X11 Rendering\n   *   \"--socket=wayland\",\n   *   \"--socket=x11\",\n   *   \"--share=ipc\",\n   *   // Open GL\n   *   \"--device=dri\",\n   *   // Audio output\n   *   \"--socket=pulseaudio\",\n   *   // Read/write home directory access\n   *   \"--filesystem=home\",\n   *   // Allow communication with network\n   *   \"--share=network\",\n   *   // System notifications with libnotify\n   *   \"--talk-name=org.freedesktop.Notifications\",\n   * ]\n   * ```\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly finishArgs?: string[]\n\n  /**\n   * An array of objects specifying the modules to be built in order.\n   *\n   * See [flatpak manifest documentation](https://docs.flatpak.org/en/latest/flatpak-builder-command-reference.html#flatpak-manifest).\n   */\n  readonly modules?: (string | any)[]\n\n  /**\n   * Files to copy directly into the app. Should be a list of [source, dest] tuples. Source should be a relative/absolute path to a file/directory to copy into the flatpak, and dest should be the path inside the app install prefix (e.g. /share/applications/).\n   *\n   * See [@malept/flatpak-bundler documentation](https://github.com/malept/flatpak-bundler#build-options).\n   */\n  readonly files?: [string, string][]\n\n  /**\n   * Symlinks to create in the app files. Should be a list of [target, location] symlink tuples. Target can be either a relative or absolute path inside the app install prefix, and location should be a absolute path inside the prefix to create the symlink at.\n   *\n   * See [@malept/flatpak-bundler documentation](https://github.com/malept/flatpak-bundler#build-options).\n   */\n  readonly symlinks?: [string, string][]\n\n  /**\n   * Whether to enable the Wayland specific flags (`--enable-features=UseOzonePlatform --ozone-platform=wayland`) in the wrapper script. These flags are only available starting with Electron version 12. Defaults to `false`.\n   */\n  readonly useWaylandFlags?: boolean\n}\n"]}