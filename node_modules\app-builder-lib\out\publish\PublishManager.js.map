{"version": 3, "file": "PublishManager.js", "sourceRoot": "", "sources": ["../../src/publish/PublishManager.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAkK;AAClK,+DAW6B;AAC7B,iCAA0B;AAC1B,uDAAkG;AAClG,0EAAsE;AACtE,sEAAkE;AAClE,kDAA0C;AAC1C,0DAAkD;AAClD,0CAAuC;AACvC,8BAA6B;AAC7B,6BAA4B;AAE5B,2BAA0B;AAC1B,oCAAkH;AAGlH,yDAAmD;AAEnD,6DAAyD;AACzD,2DAAqG;AACrG,uDAAmD;AACnD,6DAAyD;AAEzD,MAAM,mBAAmB,GACvB,sKAAsK;IACtK,6LAA6L,CAAA;AAE/L,MAAM,KAAK,GAAG,eAAM,CAAC,0BAA0B,CAAC,CAAA;AAEhD,SAAS,YAAY,CAAC,aAAkB;IACtC,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,KAAK,OAAO,IAAI,aAAa,KAAK,cAAc,IAAI,aAAa,KAAK,QAAQ,IAAI,aAAa,KAAK,OAAO,EAAE;QACrJ,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAI,wCAAyB,CACjC,uEAAuE,IAAI,CAAC,SAAS,CACnF,aAAa,CACd,8EAA8E,CAChF,CAAA;SACF;KACF;AACH,CAAC;AAED,MAAa,cAAc;IAWzB,YAA6B,QAAkB,EAAmB,cAA8B,EAAW,oBAAuC,QAAQ,CAAC,iBAAiB;QAA/I,aAAQ,GAAR,QAAQ,CAAU;QAAmB,mBAAc,GAAd,cAAc,CAAgB;QAAW,sBAAiB,GAAjB,iBAAiB,CAAgD;QAV3J,oBAAe,GAAG,IAAI,GAAG,EAA4B,CAAA;QAI7D,cAAS,GAAY,KAAK,CAAA;QAE1B,aAAQ,GAAI,OAAO,CAAC,MAAyB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,6BAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;QAExE,wBAAmB,GAA8B,EAAE,CAAA;QAGlE,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;QAEpC,IAAI,CAAC,WAAW,GAAG,IAAI,+BAAgB,CAAC,iBAAiB,CAAC,CAAA;QAE1D,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,CAAA;QACzE,IAAI,CAAC,4BAAa,EAAE,IAAI,iBAAiB,EAAE;YACzC,IAAI,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE;gBACxC,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,SAAS,EAAE;oBACjD,cAAc,CAAC,OAAO,GAAG,QAAQ,CAAA;iBAClC;qBAAM;oBACL,MAAM,GAAG,GAAG,2BAAQ,EAAE,CAAA;oBACtB,IAAI,GAAG,IAAI,IAAI,EAAE;wBACf,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,EAAE,EAAE,6BAA6B,CAAC,CAAA;wBAC1E,cAAc,CAAC,OAAO,GAAG,OAAO,CAAA;qBACjC;yBAAM,IAAI,IAAI,EAAE;wBACf,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,qDAAqD,CAAC,CAAA;wBAC1F,cAAc,CAAC,OAAO,GAAG,cAAc,CAAA;qBACxC;iBACF;aACF;YAED,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAA;YAC5C,IAAI,CAAC,SAAS,GAAG,aAAa,IAAI,IAAI,IAAI,cAAc,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,aAAa,KAAK,OAAO,IAAI,2BAAQ,EAAE,IAAI,IAAI,CAAC,CAAA;YACjI,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,EAAE;gBACvC,kBAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;aAC9B;SACF;aAAM,IAAI,cAAc,CAAC,OAAO,KAAK,OAAO,EAAE;YAC7C,kBAAG,CAAC,IAAI,CACN;gBACE,MAAM,EAAE,yCAAyC;gBACjD,QAAQ,EAAE,mEAAmE,mBAAmB,EAAE;aACnG,EACD,4BAA4B,CAC7B,CAAA;SACF;QAED,QAAQ,CAAC,mBAAmB,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACzC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAA;YAC/B,IAAI,KAAK,CAAC,oBAAoB,KAAK,QAAQ,EAAE;gBAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;oBACrE,OAAM;iBACP;aACF;iBAAM,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,OAAO,EAAE;gBACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,CAAC,EAAE;oBAC1D,OAAM;iBACP;aACF;iBAAM;gBACL,oEAAoE;gBACpE,OAAM;aACP;YAED,MAAM,aAAa,GAAG,MAAM,gCAAgC,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;YAClG,IAAI,aAAa,IAAI,IAAI,EAAE;gBACzB,MAAM,oBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC,EAAE,8BAAe,CAAC,aAAa,CAAC,CAAC,CAAA;aACxH;QACH,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC/B,MAAM,oBAAoB,GAAG,KAAK,CAAC,aAAa,CAAA;YAChD,IAAI,oBAAoB,IAAI,IAAI,EAAE;gBAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,2CAA2C,CAAC,KAAK,CAAC,CAAC,CAAA;aAClF;iBAAM,IAAI,IAAI,CAAC,SAAS,EAAE;gBACzB,IAAI,KAAK,CAAC,OAAO,EAAE;oBACjB,KAAK,CAAC,+BAA+B,IAAI,CAAC,SAAS,MAAM,gCAAiB,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,uBAAuB,gCAAiB,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAA;iBAC1K;gBACD,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAA;aAClF;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,UAAU,CAAC,gBAA8C;QAC/D,OAAO,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAA;IACpF,CAAC;IAED,KAAK,CAAC,8BAA8B;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAA;QAC/C,OAAO,MAAM,4BAA4B,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACxF,CAAC;IAED,gBAAgB;IAChB,cAAc,CAAC,aAAmC,EAAE,KAAiB,EAAE,OAAgB;QACrF,IAAI,aAAa,CAAC,QAAQ,KAAK,SAAS,EAAE;YACxC,OAAM;SACP;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QACnE,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,kBAAG,CAAC,KAAK,CACP;gBACE,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,mBAAmB;gBAC3B,aAAa,EAAE,gCAAiB,CAAC,aAAa,CAAC;aAChD,EACD,eAAe,CAChB,CAAA;YACD,OAAM;SACP;QAED,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAA;QAC3C,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,cAAc,IAAI,2BAAQ,EAAE,IAAI,IAAI,IAAI,YAAY,KAAK,WAAW,IAAI,YAAY,KAAK,QAAQ,EAAE;YACrI,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,oCAAoC,EAAE,aAAa,EAAE,cAAc,EAAE,EAAE,oBAAoB,YAAY,EAAE,CAAC,CAAA;YAC/I,OAAM;SACP;QAED,IAAI,aAAa,CAAC,OAAO,EAAE;YACzB,KAAK,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAA;SACtC;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;IACnD,CAAC;IAEO,KAAK,CAAC,2CAA2C,CAAC,KAAsB;QAC9E,MAAM,gBAAgB,GAAG,KAAK,CAAC,QAAQ,CAAA;QACvC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;QAC3B,MAAM,cAAc,GAAG,MAAM,iBAAiB,CAAC,gBAAgB,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;QAEpI,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,KAAK,CAAC,+BAA+B,IAAI,CAAC,SAAS,MAAM,gCAAiB,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,wBAAwB,gCAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;SACrK;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAA;QAC5B,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,oBAAoB,EAAE,EAAE,eAAe,CAAC,CAAA;aAC9E;YACD,OAAM;SACP;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;gBAC1C,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;oBACpC,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,eAAe,CAAC,CAAA;oBACrE,MAAK;iBACN;gBAED,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAA;aAC7E;SACF;QAED,IACE,KAAK,CAAC,iBAAiB;YACvB,MAAM,IAAI,IAAI;YACd,SAAS,IAAI,IAAI;YACjB,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS;YACjC,CAAC,gBAAgB,CAAC,QAAQ,KAAK,gBAAQ,CAAC,OAAO,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC,EACnF;YACA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,yCAAqB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;SACxH;IACH,CAAC;IAEO,oBAAoB,CAAC,aAAmC,EAAE,OAAgB;QAChF,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,gCAAiB,CAAC,aAAa,CAAC,CAAA;QACzD,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;QAC1D,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,SAAS,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YACrG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAA;YACrD,kBAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,SAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,CAAC,CAAA;SAC7D;QACD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,qCAAqC;IACrC,WAAW;QACT,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAA;QAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;IAC9B,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAA;QAEnC,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAA;QACpD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YACxE,OAAM;SACP;QAED,MAAM,wCAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAA;IACrC,CAAC;CACF;AA/LD,wCA+LC;AAEM,KAAK,UAAU,gCAAgC,CAAC,QAA+B,EAAE,IAAU,EAAE,aAAsB;IACxH,MAAM,cAAc,GAAG,MAAM,8BAA8B,CAAC,QAAQ,EAAE,MAAM,iBAAiB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,EAAE,IAAI,CAAC,CAAA;IACzI,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;QACzD,OAAO,IAAI,CAAA;KACZ;IAED,MAAM,aAAa,GAAG;QACpB,GAAG,cAAc,CAAC,CAAC,CAAC;QACpB,mBAAmB,EAAE,QAAQ,CAAC,OAAO,CAAC,mBAAmB;KAC1D,CAAA;IAED,IAAI,QAAQ,CAAC,QAAQ,KAAK,gBAAQ,CAAC,OAAO,IAAI,aAAa,CAAC,aAAa,IAAI,IAAI,EAAE;QACjF,MAAM,WAAW,GAAG,QAAuB,CAAA;QAC3C,MAAM,aAAa,GAAG,WAAW,CAAC,8BAA8B,CAAC,CAAC,CAAC,MAAM,WAAW,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAA;QAC5H,IAAI,aAAa,IAAI,IAAI,EAAE;YACzB,aAAa,CAAC,aAAa,GAAG,aAAa,CAAA;SAC5C;KACF;IACD,OAAO,aAAa,CAAA;AACtB,CAAC;AAnBD,4EAmBC;AAEM,KAAK,UAAU,8BAA8B,CAClD,QAA+B,EAC/B,cAAkD,EAClD,IAAiB;IAEjB,IAAI,cAAc,KAAK,IAAI,EAAE;QAC3B,OAAO,IAAI,CAAA;KACZ;IAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/B,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,iFAAiF,CAAC,CAAA;QAClG,0FAA0F;QAC1F,kKAAkK;QAClK,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAA;QACzD,KAAK,CAAC,mCAAmC,gCAAiB,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;QAC7E,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC9D,MAAM,qBAAqB,GAAG,MAAM,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;YACrI,IAAI,qBAAqB,IAAI,IAAI,EAAE;gBACjC,KAAK,CAAC,6DAA6D,gCAAiB,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAA;gBAC9G,OAAO,CAAC,qBAAqB,CAAC,CAAA;aAC/B;SACF;KACF;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AAxBD,wEAwBC;AAED,SAAgB,eAAe,CAAC,OAAuB,EAAE,OAAe,EAAE,aAAmC,EAAE,OAAuB,EAAE,QAAkB;IACxJ,IAAI,KAAK,CAAC,OAAO,EAAE;QACjB,KAAK,CAAC,qBAAqB,gCAAiB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;KAC/D;IAED,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;IACvC,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ;YACX,OAAO,IAAI,iCAAe,CAAC,OAAO,EAAE,aAA8B,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAEvF,KAAK,QAAQ;YACX,OAAO,IAAI,iCAAe,CAAC,OAAO,EAAE,aAA8B,EAAE,OAAO,CAAC,CAAA;QAE9E,KAAK,WAAW;YACd,OAAO,IAAI,uCAAkB,CAAC,OAAO,EAAE,aAAiC,CAAC,CAAA;QAE3E,KAAK,SAAS;YACZ,OAAO,IAAI,CAAA;QAEb,OAAO,CAAC,CAAC;YACP,MAAM,KAAK,GAAG,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YACtD,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;SAChE;KACF;AACH,CAAC;AAxBD,0CAwBC;AAED,SAAS,oBAAoB,CAAC,QAAgB,EAAE,QAAkB;IAChE,QAAQ,QAAQ,EAAE;QAChB,KAAK,QAAQ;YACX,OAAO,iCAAe,CAAA;QAExB,KAAK,SAAS;YACZ,OAAO,IAAI,CAAA;QAEb,KAAK,QAAQ;YACX,OAAO,iCAAe,CAAA;QAExB,KAAK,IAAI;YACP,OAAO,qBAAW,CAAA;QAEpB,KAAK,WAAW;YACd,OAAO,uCAAkB,CAAA;QAE3B,KAAK,QAAQ;YACX,OAAO,yBAAe,CAAA;QAExB,KAAK,WAAW;YACd,OAAO,uCAAkB,CAAA;QAE3B,OAAO,CAAC,CAAC;YACP,MAAM,IAAI,GAAG,sBAAsB,QAAQ,EAAE,CAAA;YAC7C,IAAI,MAAM,GAAQ,IAAI,CAAA;YACtB,IAAI;gBACF,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,CAAA;aACtE;YAAC,OAAO,OAAO,EAAE;gBAChB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;aACrB;YAED,IAAI,MAAM,IAAI,IAAI,EAAE;gBAClB,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;aACvB;YACD,OAAO,MAAM,CAAC,OAAO,IAAI,MAAM,CAAA;SAChC;KACF;AACH,CAAC;AAED,SAAgB,kBAAkB,CAAC,oBAA0C,EAAE,QAAuB,EAAE,QAA+B;IACrI,IAAI,oBAAoB,CAAC,QAAQ,KAAK,SAAS,EAAE;QAC/C,MAAM,aAAa,GAAI,oBAA6C,CAAC,GAAG,CAAA;QACxE,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,OAAO,aAAa,CAAA;SACrB;QAED,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QACxC,OAAO,GAAG,CAAC,MAAM,CAAC,EAAE,GAAI,OAAyB,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAA;KACjI;IAED,IAAI,OAAO,CAAA;IACX,IAAI,oBAAoB,CAAC,QAAQ,KAAK,QAAQ,EAAE;QAC9C,MAAM,EAAE,GAAG,oBAAqC,CAAA;QAChD,OAAO,GAAG,GAAG,gCAAS,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,sBAAsB,EAAE,CAAC,gBAAgB,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;KAC7I;SAAM;QACL,OAAO,GAAG,+CAAwB,CAAC,oBAAoB,CAAC,CAAA;KACzD;IAED,IAAI,QAAQ,IAAI,IAAI,EAAE;QACpB,OAAO,OAAO,CAAA;KACf;IACD,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAA;AAC5C,CAAC;AAvBD,gDAuBC;AAEM,KAAK,UAAU,iBAAiB,CACrC,gBAAuC,EACvC,qBAAsE,EACtE,IAAiB,EACjB,aAAsB;IAEtB,IAAI,UAAU,CAAA;IAEd,4BAA4B;IAC5B,IAAI,qBAAqB,IAAI,IAAI,EAAE;QACjC,UAAU,GAAG,qBAAqB,CAAC,OAAO,CAAA;QAC1C,6CAA6C;QAC7C,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,OAAO,IAAI,CAAA;SACZ;KACF;IAED,6BAA6B;IAC7B,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,UAAU,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,OAAO,CAAA;QAClE,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,OAAO,IAAI,CAAA;SACZ;KACF;IAED,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAA;QAC5C,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,OAAO,IAAI,CAAA;SACZ;KACF;IACD,OAAO,MAAM,4BAA4B,CAAC,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAA;AACrH,CAAC;AAhCD,8CAgCC;AAED,KAAK,UAAU,4BAA4B,CACzC,UAAe,EACf,gBAA8C,EAC9C,QAAkB,EAClB,IAAiB,EACjB,aAAsB;IAEtB,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,IAAI,WAAW,GAA2B,IAAI,CAAA;QAC9C,IAAI,CAAC,8BAAe,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,8BAAe,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACxF,WAAW,GAAG,QAAQ,CAAA;SACvB;aAAM,IAAI,CAAC,8BAAe,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACrD,WAAW,GAAG,QAAQ,CAAA;SACvB;aAAM,IAAI,CAAC,8BAAe,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;YACxD,WAAW,GAAG,WAAW,CAAA;SAC1B;aAAM,IAAI,CAAC,8BAAe,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACjD,MAAM,IAAI,KAAK,CACb,+JAA+J,CAChK,CAAA;SACF;QAED,IAAI,WAAW,IAAI,IAAI,EAAE;YACvB,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,WAAW,sBAAsB,CAAC,CAAA;YAC5D,OAAO,CAAC,CAAC,MAAM,wBAAwB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,CAAE,CAAC,CAAA;SACvH;KACF;IAED,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,EAAE,CAAA;KACV;IAED,KAAK,CAAC,8BAA8B,gCAAiB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IACpE,OAAO,MAAO,sBAAe,CAAC,GAAG,CAAC,sBAAO,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,EAAE,CAC1D,wBAAwB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,CACjF,CAAA;AAC5C,CAAC;AAED,SAAS,uBAAuB,CAAC,MAAc;IAC7C,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,IAAK,MAAM,CAAC,OAAe,CAAC,oBAAoB,EAAE;QACpG,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AAClE,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAY,EAAE,gBAA8C,EAAE,QAAkB,EAAE,IAAiB;IAC9H,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACvC,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QAC3B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,SAAS,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAI,CAAC,IAAI,CAAC,CAAA;YAClD,MAAM,QAAQ,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,2BAAW,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAC5I,IAAI,QAAQ,KAAK,KAAK,EAAE;gBACtB,OAAO,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAA;aACzB;SACF;KACF;AACH,CAAC;AAED,SAAS,qBAAqB,CAAC,6BAAkE,EAAE,aAA4B;IAC7H,MAAM,KAAK,GAAG,6BAA6B,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC,mBAAmB,CAAA;IAC9G,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,mBAAmB,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;AAC5E,CAAC;AAED,KAAK,UAAU,wBAAwB,CACrC,gBAA8C,EAC9C,QAAkB,EAClB,OAA6B,EAC7B,IAAiB,EACjB,aAAsB;IAEtB,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,CAAA;IACxB,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;IAE9D,IAAI,qBAAqB,GAAkB,IAAI,CAAA;IAC/C,IACG,OAAgC,CAAC,OAAO,IAAI,IAAI;QACjD,qBAAqB,CAAC,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,MAAM,CAAC,EACvH;QACA,qBAAqB,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAA;KACjD;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IACjC,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAM,CAAC,GAAG,OAA+B,CAAA;QACzC,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,EAAE;YACjB,MAAM,IAAI,wCAAyB,CAAC,kDAAkD,CAAC,CAAA;SACxF;QAED,IAAI,qBAAqB,IAAI,IAAI,EAAE;YACjC,CAAC;YAAC,CAAS,CAAC,OAAO,GAAG,qBAAqB,CAAA;SAC5C;QACD,OAAO,OAAO,CAAA;KACf;IAED,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IACtE,IAAI,aAAa,IAAI,IAAI,IAAI,aAAa,CAAC,sBAAsB,IAAI,IAAI,EAAE;QACzE,MAAM,aAAa,CAAC,sBAAsB,CAAC,OAAO,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAA;QACzF,OAAO,OAAO,CAAA;KACf;IAED,IAAI,QAAQ,KAAK,QAAQ,EAAE;QACzB,OAAO;YACL,GAAG,OAAO;YACV,QAAQ,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,QAAQ,CAAC,IAAI;SACzB,CAAA;KACnB;IAED,MAAM,QAAQ,GAAG,QAAQ,KAAK,QAAQ,CAAA;IACtC,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,WAAW,EAAE;QACzC,OAAO,OAAO,CAAA;KACf;IAED,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAE,OAAyB,CAAC,KAAK,CAAC,CAAC,CAAE,OAA4B,CAAC,KAAK,CAAA;IAC7F,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAE,OAAyB,CAAC,IAAI,CAAC,CAAC,CAAE,OAA4B,CAAC,IAAI,CAAA;IAE7F,IAAI,QAAQ,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;QAChD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QAClC,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,MAAM,IAAI,GAAG,OAAO,CAAA;YACpB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;YAClC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;SAClC;KACF;IAED,KAAK,UAAU,OAAO;QACpB,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAA;QAC1C,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,OAAO,GAAG,2MAA2M,CAAA;QAC3N,IAAI,aAAa,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;SACzB;aAAM;YACL,kBAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACjB,OAAO,IAAI,CAAA;SACZ;IACH,CAAC;IAED,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;QACtB,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,8CAA8C,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,iBAAiB,CAAC,CAAA;QAClH,MAAM,IAAI,GAAG,MAAM,OAAO,EAAE,CAAA;QAC5B,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,IAAI,CAAC,IAAI,CAAA;SAClB;QACD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;SACvB;KACF;IAED,IAAI,QAAQ,EAAE;QACZ,IAAK,OAAyB,CAAC,KAAK,IAAI,IAAI,IAAI,CAAE,OAAyB,CAAC,OAAO,EAAE;YACnF,kBAAG,CAAC,IAAI,CAAC,yJAAyJ,CAAC,CAAA;SACpK;QACD,2DAA2D;QAC3D,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAmB,CAAA;KAC7D;SAAM;QACL,2DAA2D;QAC3D,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,EAAsB,CAAA;KAChE;AACH,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, as<PERSON><PERSON>y, AsyncTaskManager, InvalidConfigurationError, isEmptyOrSpaces, isPullRequest, log, safeStringifyJson, serializeToYaml } from \"builder-util\"\nimport {\n  CancellationToken,\n  GenericServerOptions,\n  getS3LikeProviderBaseUrl,\n  GithubOptions,\n  githubUrl,\n  KeygenOptions,\n  SnapStoreOptions,\n  PublishConfiguration,\n  PublishProvider,\n  BitbucketOptions,\n} from \"builder-util-runtime\"\nimport _debug from \"debug\"\nimport { getCiTag, PublishContext, Publisher, PublishOptions, UploadTask } from \"electron-publish\"\nimport { GitHubPublisher } from \"electron-publish/out/gitHubPublisher\"\nimport { MultiProgress } from \"electron-publish/out/multiProgress\"\nimport S3Publisher from \"./s3/s3Publisher\"\nimport SpacesPublisher from \"./s3/spacesPublisher\"\nimport { writeFile } from \"fs/promises\"\nimport * as isCi from \"is-ci\"\nimport * as path from \"path\"\nimport { WriteStream as TtyWriteStream } from \"tty\"\nimport * as url from \"url\"\nimport { AppInfo, ArtifactCreated, Configuration, Platform, PlatformSpecificBuildOptions, Target } from \"../index\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { expandMacro } from \"../util/macroExpander\"\nimport { WinPackager } from \"../winPackager\"\nimport { SnapStorePublisher } from \"./SnapStorePublisher\"\nimport { createUpdateInfoTasks, UpdateInfoFileTask, writeUpdateInfoFiles } from \"./updateInfoBuilder\"\nimport { KeygenPublisher } from \"./KeygenPublisher\"\nimport { BitbucketPublisher } from \"./BitbucketPublisher\"\n\nconst publishForPrWarning =\n  \"There are serious security concerns with PUBLISH_FOR_PULL_REQUEST=true (see the  CircleCI documentation (https://circleci.com/docs/1.0/fork-pr-builds/) for details)\" +\n  \"\\nIf you have SSH keys, sensitive env vars or AWS credentials stored in your project settings and untrusted forks can make pull requests against your repo, then this option isn't for you.\"\n\nconst debug = _debug(\"electron-builder:publish\")\n\nfunction checkOptions(publishPolicy: any) {\n  if (publishPolicy != null && publishPolicy !== \"onTag\" && publishPolicy !== \"onTagOrDraft\" && publishPolicy !== \"always\" && publishPolicy !== \"never\") {\n    if (typeof publishPolicy === \"string\") {\n      throw new InvalidConfigurationError(\n        `Expected one of \"onTag\", \"onTagOrDraft\", \"always\", \"never\", but got ${JSON.stringify(\n          publishPolicy\n        )}.\\nPlease note that publish configuration should be specified under \"config\"`\n      )\n    }\n  }\n}\n\nexport class PublishManager implements PublishContext {\n  private readonly nameToPublisher = new Map<string, Publisher | null>()\n\n  private readonly taskManager: AsyncTaskManager\n\n  readonly isPublish: boolean = false\n\n  readonly progress = (process.stdout as TtyWriteStream).isTTY ? new MultiProgress() : null\n\n  private readonly updateFileWriteTask: Array<UpdateInfoFileTask> = []\n\n  constructor(private readonly packager: Packager, private readonly publishOptions: PublishOptions, readonly cancellationToken: CancellationToken = packager.cancellationToken) {\n    checkOptions(publishOptions.publish)\n\n    this.taskManager = new AsyncTaskManager(cancellationToken)\n\n    const forcePublishForPr = process.env.PUBLISH_FOR_PULL_REQUEST === \"true\"\n    if (!isPullRequest() || forcePublishForPr) {\n      if (publishOptions.publish === undefined) {\n        if (process.env.npm_lifecycle_event === \"release\") {\n          publishOptions.publish = \"always\"\n        } else {\n          const tag = getCiTag()\n          if (tag != null) {\n            log.info({ reason: \"tag is defined\", tag }, \"artifacts will be published\")\n            publishOptions.publish = \"onTag\"\n          } else if (isCi) {\n            log.info({ reason: \"CI detected\" }, \"artifacts will be published if draft release exists\")\n            publishOptions.publish = \"onTagOrDraft\"\n          }\n        }\n      }\n\n      const publishPolicy = publishOptions.publish\n      this.isPublish = publishPolicy != null && publishOptions.publish !== \"never\" && (publishPolicy !== \"onTag\" || getCiTag() != null)\n      if (this.isPublish && forcePublishForPr) {\n        log.warn(publishForPrWarning)\n      }\n    } else if (publishOptions.publish !== \"never\") {\n      log.info(\n        {\n          reason: \"current build is a part of pull request\",\n          solution: `set env PUBLISH_FOR_PULL_REQUEST to true to force code signing\\n${publishForPrWarning}`,\n        },\n        \"publishing will be skipped\"\n      )\n    }\n\n    packager.addAfterPackHandler(async event => {\n      const packager = event.packager\n      if (event.electronPlatformName === \"darwin\") {\n        if (!event.targets.some(it => it.name === \"dmg\" || it.name === \"zip\")) {\n          return\n        }\n      } else if (packager.platform === Platform.WINDOWS) {\n        if (!event.targets.some(it => isSuitableWindowsTarget(it))) {\n          return\n        }\n      } else {\n        // AppImage writes data to AppImage stage dir, not to linux-unpacked\n        return\n      }\n\n      const publishConfig = await getAppUpdatePublishConfiguration(packager, event.arch, this.isPublish)\n      if (publishConfig != null) {\n        await writeFile(path.join(packager.getResourcesDir(event.appOutDir), \"app-update.yml\"), serializeToYaml(publishConfig))\n      }\n    })\n\n    packager.artifactCreated(event => {\n      const publishConfiguration = event.publishConfig\n      if (publishConfiguration == null) {\n        this.taskManager.addTask(this.artifactCreatedWithoutExplicitPublishConfig(event))\n      } else if (this.isPublish) {\n        if (debug.enabled) {\n          debug(`artifactCreated (isPublish: ${this.isPublish}): ${safeStringifyJson(event, new Set([\"packager\"]))},\\n  publishConfig: ${safeStringifyJson(publishConfiguration)}`)\n        }\n        this.scheduleUpload(publishConfiguration, event, this.getAppInfo(event.packager))\n      }\n    })\n  }\n\n  private getAppInfo(platformPackager: PlatformPackager<any> | null) {\n    return platformPackager == null ? this.packager.appInfo : platformPackager.appInfo\n  }\n\n  async getGlobalPublishConfigurations(): Promise<Array<PublishConfiguration> | null> {\n    const publishers = this.packager.config.publish\n    return await resolvePublishConfigurations(publishers, null, this.packager, null, true)\n  }\n\n  /** @internal */\n  scheduleUpload(publishConfig: PublishConfiguration, event: UploadTask, appInfo: AppInfo): void {\n    if (publishConfig.provider === \"generic\") {\n      return\n    }\n\n    const publisher = this.getOrCreatePublisher(publishConfig, appInfo)\n    if (publisher == null) {\n      log.debug(\n        {\n          file: event.file,\n          reason: \"publisher is null\",\n          publishConfig: safeStringifyJson(publishConfig),\n        },\n        \"not published\"\n      )\n      return\n    }\n\n    const providerName = publisher.providerName\n    if (this.publishOptions.publish === \"onTagOrDraft\" && getCiTag() == null && providerName !== \"bitbucket\" && providerName !== \"github\") {\n      log.info({ file: event.file, reason: \"current build is not for a git tag\", publishPolicy: \"onTagOrDraft\" }, `not published to ${providerName}`)\n      return\n    }\n\n    if (publishConfig.timeout) {\n      event.timeout = publishConfig.timeout\n    }\n\n    this.taskManager.addTask(publisher.upload(event))\n  }\n\n  private async artifactCreatedWithoutExplicitPublishConfig(event: ArtifactCreated) {\n    const platformPackager = event.packager\n    const target = event.target\n    const publishConfigs = await getPublishConfigs(platformPackager, target == null ? null : target.options, event.arch, this.isPublish)\n\n    if (debug.enabled) {\n      debug(`artifactCreated (isPublish: ${this.isPublish}): ${safeStringifyJson(event, new Set([\"packager\"]))},\\n  publishConfigs: ${safeStringifyJson(publishConfigs)}`)\n    }\n\n    const eventFile = event.file\n    if (publishConfigs == null) {\n      if (this.isPublish) {\n        log.debug({ file: eventFile, reason: \"no publish configs\" }, \"not published\")\n      }\n      return\n    }\n\n    if (this.isPublish) {\n      for (const publishConfig of publishConfigs) {\n        if (this.cancellationToken.cancelled) {\n          log.debug({ file: event.file, reason: \"cancelled\" }, \"not published\")\n          break\n        }\n\n        this.scheduleUpload(publishConfig, event, this.getAppInfo(platformPackager))\n      }\n    }\n\n    if (\n      event.isWriteUpdateInfo &&\n      target != null &&\n      eventFile != null &&\n      !this.cancellationToken.cancelled &&\n      (platformPackager.platform !== Platform.WINDOWS || isSuitableWindowsTarget(target))\n    ) {\n      this.taskManager.addTask(createUpdateInfoTasks(event, publishConfigs).then(it => this.updateFileWriteTask.push(...it)))\n    }\n  }\n\n  private getOrCreatePublisher(publishConfig: PublishConfiguration, appInfo: AppInfo): Publisher | null {\n    // to not include token into cache key\n    const providerCacheKey = safeStringifyJson(publishConfig)\n    let publisher = this.nameToPublisher.get(providerCacheKey)\n    if (publisher == null) {\n      publisher = createPublisher(this, appInfo.version, publishConfig, this.publishOptions, this.packager)\n      this.nameToPublisher.set(providerCacheKey, publisher)\n      log.info({ publisher: publisher!.toString() }, \"publishing\")\n    }\n    return publisher\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  cancelTasks() {\n    this.taskManager.cancelTasks()\n    this.nameToPublisher.clear()\n  }\n\n  async awaitTasks(): Promise<void> {\n    await this.taskManager.awaitTasks()\n\n    const updateInfoFileTasks = this.updateFileWriteTask\n    if (this.cancellationToken.cancelled || updateInfoFileTasks.length === 0) {\n      return\n    }\n\n    await writeUpdateInfoFiles(updateInfoFileTasks, this.packager)\n    await this.taskManager.awaitTasks()\n  }\n}\n\nexport async function getAppUpdatePublishConfiguration(packager: PlatformPackager<any>, arch: Arch, errorIfCannot: boolean) {\n  const publishConfigs = await getPublishConfigsForUpdateInfo(packager, await getPublishConfigs(packager, null, arch, errorIfCannot), arch)\n  if (publishConfigs == null || publishConfigs.length === 0) {\n    return null\n  }\n\n  const publishConfig = {\n    ...publishConfigs[0],\n    updaterCacheDirName: packager.appInfo.updaterCacheDirName,\n  }\n\n  if (packager.platform === Platform.WINDOWS && publishConfig.publisherName == null) {\n    const winPackager = packager as WinPackager\n    const publisherName = winPackager.isForceCodeSigningVerification ? await winPackager.computedPublisherName.value : undefined\n    if (publisherName != null) {\n      publishConfig.publisherName = publisherName\n    }\n  }\n  return publishConfig\n}\n\nexport async function getPublishConfigsForUpdateInfo(\n  packager: PlatformPackager<any>,\n  publishConfigs: Array<PublishConfiguration> | null,\n  arch: Arch | null\n): Promise<Array<PublishConfiguration> | null> {\n  if (publishConfigs === null) {\n    return null\n  }\n\n  if (publishConfigs.length === 0) {\n    log.debug(null, \"getPublishConfigsForUpdateInfo: no publishConfigs, detect using repository info\")\n    // https://github.com/electron-userland/electron-builder/issues/925#issuecomment-261732378\n    // default publish config is github, file should be generated regardless of publish state (user can test installer locally or manage the release process manually)\n    const repositoryInfo = await packager.info.repositoryInfo\n    debug(`getPublishConfigsForUpdateInfo: ${safeStringifyJson(repositoryInfo)}`)\n    if (repositoryInfo != null && repositoryInfo.type === \"github\") {\n      const resolvedPublishConfig = await getResolvedPublishConfig(packager, packager.info, { provider: repositoryInfo.type }, arch, false)\n      if (resolvedPublishConfig != null) {\n        debug(`getPublishConfigsForUpdateInfo: resolve to publish config ${safeStringifyJson(resolvedPublishConfig)}`)\n        return [resolvedPublishConfig]\n      }\n    }\n  }\n  return publishConfigs\n}\n\nexport function createPublisher(context: PublishContext, version: string, publishConfig: PublishConfiguration, options: PublishOptions, packager: Packager): Publisher | null {\n  if (debug.enabled) {\n    debug(`Create publisher: ${safeStringifyJson(publishConfig)}`)\n  }\n\n  const provider = publishConfig.provider\n  switch (provider) {\n    case \"github\":\n      return new GitHubPublisher(context, publishConfig as GithubOptions, version, options)\n\n    case \"keygen\":\n      return new KeygenPublisher(context, publishConfig as KeygenOptions, version)\n\n    case \"snapStore\":\n      return new SnapStorePublisher(context, publishConfig as SnapStoreOptions)\n\n    case \"generic\":\n      return null\n\n    default: {\n      const clazz = requireProviderClass(provider, packager)\n      return clazz == null ? null : new clazz(context, publishConfig)\n    }\n  }\n}\n\nfunction requireProviderClass(provider: string, packager: Packager): any | null {\n  switch (provider) {\n    case \"github\":\n      return GitHubPublisher\n\n    case \"generic\":\n      return null\n\n    case \"keygen\":\n      return KeygenPublisher\n\n    case \"s3\":\n      return S3Publisher\n\n    case \"snapStore\":\n      return SnapStorePublisher\n\n    case \"spaces\":\n      return SpacesPublisher\n\n    case \"bitbucket\":\n      return BitbucketPublisher\n\n    default: {\n      const name = `electron-publisher-${provider}`\n      let module: any = null\n      try {\n        module = require(path.join(packager.buildResourcesDir, name + \".js\"))\n      } catch (ignored) {\n        console.log(ignored)\n      }\n\n      if (module == null) {\n        module = require(name)\n      }\n      return module.default || module\n    }\n  }\n}\n\nexport function computeDownloadUrl(publishConfiguration: PublishConfiguration, fileName: string | null, packager: PlatformPackager<any>) {\n  if (publishConfiguration.provider === \"generic\") {\n    const baseUrlString = (publishConfiguration as GenericServerOptions).url\n    if (fileName == null) {\n      return baseUrlString\n    }\n\n    const baseUrl = url.parse(baseUrlString)\n    return url.format({ ...(baseUrl as url.UrlObject), pathname: path.posix.resolve(baseUrl.pathname || \"/\", encodeURI(fileName)) })\n  }\n\n  let baseUrl\n  if (publishConfiguration.provider === \"github\") {\n    const gh = publishConfiguration as GithubOptions\n    baseUrl = `${githubUrl(gh)}/${gh.owner}/${gh.repo}/releases/download/${gh.vPrefixedTagName === false ? \"\" : \"v\"}${packager.appInfo.version}`\n  } else {\n    baseUrl = getS3LikeProviderBaseUrl(publishConfiguration)\n  }\n\n  if (fileName == null) {\n    return baseUrl\n  }\n  return `${baseUrl}/${encodeURI(fileName)}`\n}\n\nexport async function getPublishConfigs(\n  platformPackager: PlatformPackager<any>,\n  targetSpecificOptions: PlatformSpecificBuildOptions | null | undefined,\n  arch: Arch | null,\n  errorIfCannot: boolean\n): Promise<Array<PublishConfiguration> | null> {\n  let publishers\n\n  // check build.nsis (target)\n  if (targetSpecificOptions != null) {\n    publishers = targetSpecificOptions.publish\n    // if explicitly set to null - do not publish\n    if (publishers === null) {\n      return null\n    }\n  }\n\n  // check build.win (platform)\n  if (publishers == null) {\n    publishers = platformPackager.platformSpecificBuildOptions.publish\n    if (publishers === null) {\n      return null\n    }\n  }\n\n  if (publishers == null) {\n    publishers = platformPackager.config.publish\n    if (publishers === null) {\n      return null\n    }\n  }\n  return await resolvePublishConfigurations(publishers, platformPackager, platformPackager.info, arch, errorIfCannot)\n}\n\nasync function resolvePublishConfigurations(\n  publishers: any,\n  platformPackager: PlatformPackager<any> | null,\n  packager: Packager,\n  arch: Arch | null,\n  errorIfCannot: boolean\n): Promise<Array<PublishConfiguration> | null> {\n  if (publishers == null) {\n    let serviceName: PublishProvider | null = null\n    if (!isEmptyOrSpaces(process.env.GH_TOKEN) || !isEmptyOrSpaces(process.env.GITHUB_TOKEN)) {\n      serviceName = \"github\"\n    } else if (!isEmptyOrSpaces(process.env.KEYGEN_TOKEN)) {\n      serviceName = \"keygen\"\n    } else if (!isEmptyOrSpaces(process.env.BITBUCKET_TOKEN)) {\n      serviceName = \"bitbucket\"\n    } else if (!isEmptyOrSpaces(process.env.BT_TOKEN)) {\n      throw new Error(\n        \"Bintray has been sunset and is no longer supported by electron-builder. Ref: https://jfrog.com/blog/into-the-sunset-bintray-jcenter-gocenter-and-chartcenter/\"\n      )\n    }\n\n    if (serviceName != null) {\n      log.debug(null, `detect ${serviceName} as publish provider`)\n      return [(await getResolvedPublishConfig(platformPackager, packager, { provider: serviceName }, arch, errorIfCannot))!]\n    }\n  }\n\n  if (publishers == null) {\n    return []\n  }\n\n  debug(`Explicit publish provider: ${safeStringifyJson(publishers)}`)\n  return await (BluebirdPromise.map(asArray(publishers), it =>\n    getResolvedPublishConfig(platformPackager, packager, typeof it === \"string\" ? { provider: it } : it, arch, errorIfCannot)\n  ) as Promise<Array<PublishConfiguration>>)\n}\n\nfunction isSuitableWindowsTarget(target: Target) {\n  if (target.name === \"appx\" && target.options != null && (target.options as any).electronUpdaterAware) {\n    return true\n  }\n  return target.name === \"nsis\" || target.name.startsWith(\"nsis-\")\n}\n\nfunction expandPublishConfig(options: any, platformPackager: PlatformPackager<any> | null, packager: Packager, arch: Arch | null): void {\n  for (const name of Object.keys(options)) {\n    const value = options[name]\n    if (typeof value === \"string\") {\n      const archValue = arch == null ? null : Arch[arch]\n      const expanded = platformPackager == null ? expandMacro(value, archValue, packager.appInfo) : platformPackager.expandMacro(value, archValue)\n      if (expanded !== value) {\n        options[name] = expanded\n      }\n    }\n  }\n}\n\nfunction isDetectUpdateChannel(platformSpecificConfiguration: PlatformSpecificBuildOptions | null, configuration: Configuration) {\n  const value = platformSpecificConfiguration == null ? null : platformSpecificConfiguration.detectUpdateChannel\n  return value == null ? configuration.detectUpdateChannel !== false : value\n}\n\nasync function getResolvedPublishConfig(\n  platformPackager: PlatformPackager<any> | null,\n  packager: Packager,\n  options: PublishConfiguration,\n  arch: Arch | null,\n  errorIfCannot: boolean\n): Promise<PublishConfiguration | GithubOptions | BitbucketOptions | null> {\n  options = { ...options }\n  expandPublishConfig(options, platformPackager, packager, arch)\n\n  let channelFromAppVersion: string | null = null\n  if (\n    (options as GenericServerOptions).channel == null &&\n    isDetectUpdateChannel(platformPackager == null ? null : platformPackager.platformSpecificBuildOptions, packager.config)\n  ) {\n    channelFromAppVersion = packager.appInfo.channel\n  }\n\n  const provider = options.provider\n  if (provider === \"generic\") {\n    const o = options as GenericServerOptions\n    if (o.url == null) {\n      throw new InvalidConfigurationError(`Please specify \"url\" for \"generic\" update server`)\n    }\n\n    if (channelFromAppVersion != null) {\n      ;(o as any).channel = channelFromAppVersion\n    }\n    return options\n  }\n\n  const providerClass = requireProviderClass(options.provider, packager)\n  if (providerClass != null && providerClass.checkAndResolveOptions != null) {\n    await providerClass.checkAndResolveOptions(options, channelFromAppVersion, errorIfCannot)\n    return options\n  }\n\n  if (provider === \"keygen\") {\n    return {\n      ...options,\n      platform: platformPackager?.platform.name,\n    } as KeygenOptions\n  }\n\n  const isGithub = provider === \"github\"\n  if (!isGithub && provider !== \"bitbucket\") {\n    return options\n  }\n\n  let owner = isGithub ? (options as GithubOptions).owner : (options as BitbucketOptions).owner\n  let project = isGithub ? (options as GithubOptions).repo : (options as BitbucketOptions).slug\n\n  if (isGithub && owner == null && project != null) {\n    const index = project.indexOf(\"/\")\n    if (index > 0) {\n      const repo = project\n      project = repo.substring(0, index)\n      owner = repo.substring(index + 1)\n    }\n  }\n\n  async function getInfo() {\n    const info = await packager.repositoryInfo\n    if (info != null) {\n      return info\n    }\n\n    const message = `Cannot detect repository by .git/config. Please specify \"repository\" in the package.json (https://docs.npmjs.com/files/package.json#repository).\\nPlease see https://electron.build/configuration/publish`\n    if (errorIfCannot) {\n      throw new Error(message)\n    } else {\n      log.warn(message)\n      return null\n    }\n  }\n\n  if (!owner || !project) {\n    log.debug({ reason: \"owner or project is not specified explicitly\", provider, owner, project }, \"calling getInfo\")\n    const info = await getInfo()\n    if (info == null) {\n      return null\n    }\n\n    if (!owner) {\n      owner = info.user\n    }\n    if (!project) {\n      project = info.project\n    }\n  }\n\n  if (isGithub) {\n    if ((options as GithubOptions).token != null && !(options as GithubOptions).private) {\n      log.warn('\"token\" specified in the github publish options. It should be used only for [setFeedURL](module:electron-updater/out/AppUpdater.AppUpdater+setFeedURL).')\n    }\n    //tslint:disable-next-line:no-object-literal-type-assertion\n    return { owner, repo: project, ...options } as GithubOptions\n  } else {\n    //tslint:disable-next-line:no-object-literal-type-assertion\n    return { owner, slug: project, ...options } as BitbucketOptions\n  }\n}\n"]}