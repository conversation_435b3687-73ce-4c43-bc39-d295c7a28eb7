{"version": 3, "file": "s3Publisher.js", "sourceRoot": "", "sources": ["../../../src/publish/s3/s3Publisher.ts"], "names": [], "mappings": ";;AAAA,+CAAgF;AAGhF,uDAAmD;AAEnD,MAAqB,WAAY,SAAQ,iCAAe;IAGtD,YAAY,OAAuB,EAAmB,IAAe;QACnE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QADgC,SAAI,GAAJ,IAAI,CAAW;QAF5D,iBAAY,GAAG,IAAI,CAAA;IAI5B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,OAAkB,EAAE,qBAAoC,EAAE,aAAsB;QAClH,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC7B,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,IAAI,wCAAyB,CAAC,mDAAmD,CAAC,CAAA;SACzF;QAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE;YAC9E,yHAAyH;YACzH,IAAI;gBACF,OAAO,CAAC,MAAM,GAAG,MAAM,gCAAiB,CAAC,CAAC,qBAAqB,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAA;aACtF;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,aAAa,EAAE;oBACjB,MAAM,CAAC,CAAA;iBACR;qBAAM;oBACL,kBAAG,CAAC,IAAI,CAAC,yHAAyH,CAAC,EAAE,CAAC,CAAA;iBACvI;aACF;SACF;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,qBAAqB,IAAI,IAAI,EAAE;YAC5D,OAAO,CAAC,OAAO,GAAG,qBAAqB,CAAA;SACxC;QAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC9D,CAAC;YAAC,OAAe,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;SAC3D;IACH,CAAC;IAES,aAAa;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;IACzB,CAAC;IAES,kBAAkB,CAAC,IAAmB;QAC9C,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;QAE9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC5C;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACxC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SACpD;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;SAChD;IACH,CAAC;IAED,QAAQ;QACN,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACnC,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,eAAe,QAAQ,GAAG,CAAA;SAC3E;QACD,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AAjED,8BAiEC", "sourcesContent": ["import { executeAppBuilder, InvalidConfigurationError, log } from \"builder-util\"\nimport { S3Options } from \"builder-util-runtime\"\nimport { PublishContext } from \"electron-publish\"\nimport { BaseS3Publisher } from \"./BaseS3Publisher\"\n\nexport default class S3Publisher extends BaseS3Publisher {\n  readonly providerName = \"s3\"\n\n  constructor(context: PublishContext, private readonly info: S3Options) {\n    super(context, info)\n  }\n\n  static async checkAndResolveOptions(options: S3Options, channelFromAppVersion: string | null, errorIfCannot: boolean) {\n    const bucket = options.bucket\n    if (bucket == null) {\n      throw new InvalidConfigurationError(`Please specify \"bucket\" for \"s3\" publish provider`)\n    }\n\n    if (options.endpoint == null && bucket.includes(\".\") && options.region == null) {\n      // on dotted bucket names, we need to use a path-based endpoint URL. Path-based endpoint URLs need to include the region.\n      try {\n        options.region = await executeAppBuilder([\"get-bucket-location\", \"--bucket\", bucket])\n      } catch (e) {\n        if (errorIfCannot) {\n          throw e\n        } else {\n          log.warn(`cannot compute region for bucket (required because on dotted bucket names, we need to use a path-based endpoint URL): ${e}`)\n        }\n      }\n    }\n\n    if (options.channel == null && channelFromAppVersion != null) {\n      options.channel = channelFromAppVersion\n    }\n\n    if (options.endpoint != null && options.endpoint.endsWith(\"/\")) {\n      ;(options as any).endpoint = options.endpoint.slice(0, -1)\n    }\n  }\n\n  protected getBucketName(): string {\n    return this.info.bucket\n  }\n\n  protected configureS3Options(args: Array<string>): void {\n    super.configureS3Options(args)\n\n    if (this.info.endpoint != null) {\n      args.push(\"--endpoint\", this.info.endpoint)\n    }\n    if (this.info.region != null) {\n      args.push(\"--region\", this.info.region)\n    }\n\n    if (this.info.storageClass != null) {\n      args.push(\"--storageClass\", this.info.storageClass)\n    }\n    if (this.info.encryption != null) {\n      args.push(\"--encryption\", this.info.encryption)\n    }\n  }\n\n  toString() {\n    const result = super.toString()\n    const endpoint = this.info.endpoint\n    if (endpoint != null) {\n      return result.substring(0, result.length - 1) + `, endpoint: ${endpoint})`\n    }\n    return result\n  }\n}\n"]}