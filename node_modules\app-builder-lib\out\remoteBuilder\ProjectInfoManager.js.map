{"version": 3, "file": "ProjectInfoManager.js", "sourceRoot": "", "sources": ["../../src/remoteBuilder/ProjectInfoManager.ts"], "names": [], "mappings": ";;;AAAA,uCAAqC;AACrC,uCAA+B;AAC/B,6BAA4B;AAG5B,MAAa,kBAAkB;IAG7B,YAAqB,QAAkB;QAAlB,aAAQ,GAAR,QAAQ,CAAU;QAF9B,aAAQ,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAA;IAE7B,CAAC;IAEnC,KAAK,CAAC,4BAA4B;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,uBAAuB,EAAE,CAAC,CAAA;QAChG,+DAA+D;QAC/D,MAAM,IAAI,GAAQ;YAChB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,cAAc,EAAE,MAAM,QAAQ,CAAC,cAAc;YAC7C,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,iBAAiB,CAAC;SAChE,CAAA;QACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,WAAW,IAAI,IAAI,EAAE;YAC9E,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAA;SACxC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;QAC5C,MAAM,qBAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAtBD,gDAsBC", "sourcesContent": ["import { outputJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { Packager } from \"../packager\"\n\nexport class ProjectInfoManager {\n  readonly infoFile = new Lazy(() => this.saveConfigurationAndMetadata())\n\n  constructor(readonly packager: Packager) {}\n\n  private async saveConfigurationAndMetadata() {\n    const packager = this.packager\n    const tempDir = await packager.tempDirManager.createTempDir({ prefix: \"remote-build-metadata\" })\n    // we cannot use getTempFile because file name must be constant\n    const info: any = {\n      metadata: packager.metadata,\n      configuration: packager.config,\n      repositoryInfo: await packager.repositoryInfo,\n      buildResourceDirName: path.basename(packager.buildResourcesDir),\n    }\n    if (packager.metadata !== packager.devMetadata && packager.devMetadata != null) {\n      info.devMetadata = packager.devMetadata\n    }\n    const file = path.join(tempDir, \"info.json\")\n    await outputJson(file, info)\n    return file\n  }\n}\n"]}