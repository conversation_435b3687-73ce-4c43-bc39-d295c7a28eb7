{"version": 3, "file": "RemoteBuilder.js", "sourceRoot": "", "sources": ["../../src/remoteBuilder/RemoteBuilder.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA8E;AAC9E,6BAA4B;AAE5B,kCAAiE;AAGjE,0DAAiG;AACjG,mDAA4D;AAC5D,6DAAyD;AASzD,MAAa,aAAa;IAIxB,YAAqB,QAA+B;QAA/B,aAAQ,GAAR,QAAQ,CAAuB;QAHnC,YAAO,GAAG,IAAI,GAAG,EAA2B,CAAA;QACrD,iBAAY,GAAG,KAAK,CAAA;IAE2B,CAAC;IAExD,aAAa,CAAC,MAAc,EAAE,IAAU,EAAE,iBAAyB;QACjE,IAAI,CAAC,wBAAS,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,KAAK,KAAK,EAAE;YACvF,MAAM,IAAI,wCAAyB,CAAC,2GAA2G,CAAC,CAAA;SACjJ;QAED,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACjC,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,IAAI,GAAG,EAAE,CAAA;YACT,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SAC7B;QAED,IAAI,CAAC,IAAI,CAAC;YACR,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;YAChB,iBAAiB;YACjB,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAA;IACJ,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;SACzB;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAExB,OAAO,sBAAe,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAU,EAAE,EAAE;YAC/E,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC5D,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,mCAAmC;IAC3B,KAAK,CAAC,MAAM,CAAC,OAA0B,EAAE,QAA+B;QAC9E,IAAI,kBAAG,CAAC,cAAc,EAAE;YACtB,kBAAG,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAA;SAClF;QAED,MAAM,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAEhE,MAAM,YAAY,GAAQ;YACxB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACxB,OAAO;oBACL,IAAI,EAAE,EAAE,CAAC,IAAI;oBACb,IAAI,EAAE,EAAE,CAAC,IAAI;oBACb,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,iBAAiB,CAAC;iBACrD,CAAA;YACH,CAAC,CAAC;YACF,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,qBAAqB;SAClD,CAAA;QAED,IAAI,4DAAyC,CAAC,QAAQ,CAAC,EAAE;YACvD,YAAY,CAAC,gBAAgB,GAAG;gBAC9B,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;gBACxC,QAAQ,EAAE,eAAQ,CAAC,KAAK,CAAC,QAAQ;gBACjC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;aACtB,CAAA;YAED,MAAM,aAAa,GAAG,QAAyB,CAAA;YAC/C,YAAY,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,CAAA;SAC3D;QAED,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QACxE,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QAChC,MAAM,IAAI,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;QAEnE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAA;QACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAE5D,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAA;QACpD,IAAI,iBAAiB,KAAK,QAAQ,CAAC,UAAU,EAAE;YAC7C,MAAM,IAAI,wCAAyB,CACjC,wOAAwO,CACzO,CAAA;SACF;QAED,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,CAAA;QAEpD,MAAM,MAAM,GAAQ,MAAM,oCAAuB,CAAC,IAAI,CAAC,CAAA;QACvD,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YACxB,MAAM,IAAI,wCAAyB,CACjC,qLAAqL,MAAM,CAAC,KAAK,EAAE,EACnM,sBAAsB,CACvB,CAAA;SACF;aAAM,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;YAC/B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,KAAK,EAAE;gBACnC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAA;gBAClD,MAAM,oBAAoB,GAAG,IAAI,CAAC,kCAAkC,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC,CAAA;gBACjG,iGAAiG;gBACjG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,CAAA;aAC1E;SACF;IACH,CAAC;IAEO,kCAAkC,CAAC,QAAsB,EAAE,SAAiB,EAAE,MAAc;QAClG,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAA;QAC9B,uCAAuC;QACvC,OAAO;YACL,GAAG,QAAQ;YACX,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,MAAM,EAAG,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,MAAM,CAAC,CAAC;YACrG,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAA;IACH,CAAC;CACF;AA7GD,sCA6GC;AAED,MAAM,UAAW,SAAQ,aAAM;IAC7B,YAAY,IAAY,EAAW,MAAc,EAAW,OAAiD;QAC3G,KAAK,CAAC,IAAI,CAAC,CAAA;QADsB,WAAM,GAAN,MAAM,CAAQ;QAAW,YAAO,GAAP,OAAO,CAA0C;IAE7G,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,WAAW;IACb,CAAC;CACF", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, isEnvTrue, log, InvalidConfigurationError } from \"builder-util\"\nimport * as path from \"path\"\nimport { UploadTask } from \"electron-publish/out/publisher\"\nimport { Platform, Target, TargetSpecificOptions } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { ArtifactCreated } from \"../packagerApi\"\nimport { isSafeToUnpackElectronOnRemoteBuildServer, PlatformPackager } from \"../platformPackager\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { ProjectInfoManager } from \"./ProjectInfoManager\"\n\ninterface TargetInfo {\n  name: string\n  arch: string\n  unpackedDirectory: string\n  outDir: string\n}\n\nexport class RemoteBuilder {\n  private readonly toBuild = new Map<Arch, Array<TargetInfo>>()\n  private buildStarted = false\n\n  constructor(readonly packager: PlatformPackager<any>) {}\n\n  scheduleBuild(target: Target, arch: Arch, unpackedDirectory: string) {\n    if (!isEnvTrue(process.env._REMOTE_BUILD) && this.packager.config.remoteBuild === false) {\n      throw new InvalidConfigurationError('Target is not supported on your OS and using of Electron Build Service is disabled (\"remoteBuild\" option)')\n    }\n\n    let list = this.toBuild.get(arch)\n    if (list == null) {\n      list = []\n      this.toBuild.set(arch, list)\n    }\n\n    list.push({\n      name: target.name,\n      arch: Arch[arch],\n      unpackedDirectory,\n      outDir: target.outDir,\n    })\n  }\n\n  build(): Promise<any> {\n    if (this.buildStarted) {\n      return Promise.resolve()\n    }\n\n    this.buildStarted = true\n\n    return BluebirdPromise.mapSeries(Array.from(this.toBuild.keys()), (arch: Arch) => {\n      return this._build(this.toBuild.get(arch)!, this.packager)\n    })\n  }\n\n  // noinspection JSMethodCanBeStatic\n  private async _build(targets: Array<TargetInfo>, packager: PlatformPackager<any>): Promise<void> {\n    if (log.isDebugEnabled) {\n      log.debug({ remoteTargets: JSON.stringify(targets, null, 2) }, \"remote building\")\n    }\n\n    const projectInfoManager = new ProjectInfoManager(packager.info)\n\n    const buildRequest: any = {\n      targets: targets.map(it => {\n        return {\n          name: it.name,\n          arch: it.arch,\n          unpackedDirName: path.basename(it.unpackedDirectory),\n        }\n      }),\n      platform: packager.platform.buildConfigurationKey,\n    }\n\n    if (isSafeToUnpackElectronOnRemoteBuildServer(packager)) {\n      buildRequest.electronDownload = {\n        version: packager.info.framework.version,\n        platform: Platform.LINUX.nodeName,\n        arch: targets[0].arch,\n      }\n\n      const linuxPackager = packager as LinuxPackager\n      buildRequest.executableName = linuxPackager.executableName\n    }\n\n    const req = Buffer.from(JSON.stringify(buildRequest)).toString(\"base64\")\n    const outDir = targets[0].outDir\n    const args = [\"remote-build\", \"--request\", req, \"--output\", outDir]\n\n    args.push(\"--file\", targets[0].unpackedDirectory)\n    args.push(\"--file\", await projectInfoManager.infoFile.value)\n\n    const buildResourcesDir = packager.buildResourcesDir\n    if (buildResourcesDir === packager.projectDir) {\n      throw new InvalidConfigurationError(\n        `Build resources dir equals to project dir and so, not sent to remote build agent. It will lead to incorrect results.\\nPlease set \"directories.buildResources\" to separate dir or leave default (\"build\" directory in the project root)`\n      )\n    }\n\n    args.push(\"--build-resource-dir\", buildResourcesDir)\n\n    const result: any = await executeAppBuilderAsJson(args)\n    if (result.error != null) {\n      throw new InvalidConfigurationError(\n        `Remote builder error (if you think that it is not your application misconfiguration issue, please file issue to https://github.com/electron-userland/electron-builder/issues):\\n\\n${result.error}`,\n        \"REMOTE_BUILDER_ERROR\"\n      )\n    } else if (result.files != null) {\n      for (const artifact of result.files) {\n        const localFile = path.join(outDir, artifact.file)\n        const artifactCreatedEvent = this.artifactInfoToArtifactCreatedEvent(artifact, localFile, outDir)\n        // PublishManager uses outDir and options, real (the same as for local build) values must be used\n        await this.packager.info.callArtifactBuildCompleted(artifactCreatedEvent)\n      }\n    }\n  }\n\n  private artifactInfoToArtifactCreatedEvent(artifact: ArtifactInfo, localFile: string, outDir: string): ArtifactCreated {\n    const target = artifact.target\n    // noinspection SpellCheckingInspection\n    return {\n      ...artifact,\n      file: localFile,\n      target: target == null ? null : new FakeTarget(target, outDir, (this.packager.config as any)[target]),\n      packager: this.packager,\n    }\n  }\n}\n\nclass FakeTarget extends Target {\n  constructor(name: string, readonly outDir: string, readonly options: TargetSpecificOptions | null | undefined) {\n    super(name)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  async build(appOutDir: string, arch: Arch) {\n    // no build\n  }\n}\n\ninterface ArtifactInfo extends UploadTask {\n  target: string | null\n\n  readonly isWriteUpdateInfo?: boolean\n  readonly updateInfo?: any\n}\n"]}