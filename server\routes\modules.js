const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const { body, validationResult, query } = require('express-validator');
const { getDatabase } = require('../database/init');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// 配置文件上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '..', 'uploads', 'temp');
    await fs.ensureDir(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['.zip', '.rar', '.7z', '.lua', '.miz'];
  const ext = path.extname(file.originalname).toLowerCase();

  if (allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${ext}。支持的类型: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 1
  }
});

// 获取模组列表
router.get('/', optionalAuth, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词过长'),
  query('category').optional().isLength({ max: 50 }).withMessage('分类参数过长'),
  query('author').optional().isLength({ max: 100 }).withMessage('作者参数过长')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '参数验证失败', 
        details: errors.array() 
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const category = req.query.category;
    const author = req.query.author;
    const offset = (page - 1) * limit;

    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = 'WHERE m.is_active = 1';
    let params = [];
    
    if (search) {
      whereClause += ' AND (m.name LIKE ? OR m.description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (category) {
      whereClause += ' AND m.category = ?';
      params.push(category);
    }
    
    if (author) {
      whereClause += ' AND m.author LIKE ?';
      params.push(`%${author}%`);
    }

    // 如果用户已登录，检查权限
    let permissionJoin = '';
    if (req.user) {
      if (req.user.role !== 'admin') {
        // 普通用户只能看到有权限的模组
        permissionJoin = 'JOIN user_module_permissions ump ON m.id = ump.module_id AND ump.user_id = ?';
        params.unshift(req.user.userId);
      }
    } else {
      // 未登录用户只能看到公开模组（这里假设所有模组都需要权限）
      whereClause += ' AND 1=0'; // 暂时不允许未登录用户查看
    }

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT m.id) as total 
      FROM modules m 
      ${permissionJoin}
      ${whereClause}
    `;
    const countResult = await db.get(countQuery, params);
    const total = countResult.total;

    // 获取模组列表
    const modulesQuery = `
      SELECT DISTINCT
        m.id,
        m.name,
        m.description,
        m.version,
        m.author,
        m.category,
        m.file_size,
        m.download_count,
        m.rating,
        m.created_at,
        m.updated_at,
        creator.username as created_by_username
      FROM modules m
      ${permissionJoin}
      LEFT JOIN users creator ON m.created_by = creator.id
      ${whereClause}
      ORDER BY m.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const modules = await db.all(modulesQuery, [...params, limit, offset]);

    // 为每个模组添加用户权限信息
    if (req.user && req.user.role !== 'admin') {
      for (let module of modules) {
        const permission = await db.get(
          'SELECT permission_type FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
          [req.user.userId, module.id]
        );
        module.user_permission = permission ? permission.permission_type : null;
      }
    }

    res.json({
      modules,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取模组列表失败:', error);
    res.status(500).json({ error: '获取模组列表失败' });
  }
});

// 模组类型管理API
// 获取模组类型列表
router.get('/types', async (req, res) => {
  try {
    // 返回预定义的模组类型
    const moduleTypes = [
      { id: 1, name: '喷气发动机飞机', code: 'aircraft-jet', description: '现代喷气式战斗机和攻击机' },
      { id: 2, name: '活塞发动机飞机', code: 'aircraft-prop', description: '二战时期的螺旋桨飞机' },
      { id: 3, name: '地形', code: 'terrain', description: '游戏地图和地形' },
      { id: 4, name: '战役', code: 'campaign', description: '单人战役任务' },
      { id: 5, name: '模组', code: 'module', description: '游戏模组和插件' },
      { id: 6, name: '地图', code: 'map', description: '游戏地图文件' },
      { id: 7, name: '涂装', code: 'livery', description: '飞机涂装文件' }
    ];
    
    res.json({ types: moduleTypes });
  } catch (error) {
    console.error('获取模组类型失败:', error);
    res.status(500).json({ error: '获取模组类型失败' });
  }
});

// 创建模组类型（管理员）
router.post('/types', authenticateToken, requireAdmin, [
  body('name').notEmpty().isLength({ min: 1, max: 100 }).withMessage('类型名称长度必须在1-100个字符之间'),
  body('code').notEmpty().isLength({ min: 1, max: 50 }).withMessage('类型代码长度必须在1-50个字符之间'),
  body('description').optional().isLength({ max: 500 }).withMessage('描述长度不能超过500个字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const { name, code, description } = req.body;
    
    // 模拟创建成功（实际应用中可以存储到数据库）
    const newType = {
      id: Date.now(), // 临时ID
      name,
      code,
      description: description || ''
    };
    
    res.status(201).json({
      message: '模组类型创建成功',
      type: newType
    });
  } catch (error) {
    console.error('创建模组类型失败:', error);
    res.status(500).json({ error: '创建模组类型失败' });
  }
});

// 更新模组类型（管理员）
router.put('/types/:typeId', authenticateToken, requireAdmin, [
  body('name').optional().isLength({ min: 1, max: 100 }).withMessage('类型名称长度必须在1-100个字符之间'),
  body('code').optional().isLength({ min: 1, max: 50 }).withMessage('类型代码长度必须在1-50个字符之间'),
  body('description').optional().isLength({ max: 500 }).withMessage('描述长度不能超过500个字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const typeId = parseInt(req.params.typeId);
    const { name, code, description } = req.body;
    
    // 模拟更新成功
    const updatedType = {
      id: typeId,
      name: name || '更新的类型名称',
      code: code || 'updated-code',
      description: description || '更新的描述'
    };
    
    res.json({
      message: '模组类型更新成功',
      type: updatedType
    });
  } catch (error) {
    console.error('更新模组类型失败:', error);
    res.status(500).json({ error: '更新模组类型失败' });
  }
});

// 删除模组类型（管理员）
router.delete('/types/:typeId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const typeId = parseInt(req.params.typeId);
    
    // 模拟删除成功
    res.json({ message: '模组类型删除成功' });
  } catch (error) {
    console.error('删除模组类型失败:', error);
    res.status(500).json({ error: '删除模组类型失败' });
  }
});

// 获取单个模组信息
router.get('/:moduleId', optionalAuth, async (req, res) => {
  try {
    const moduleId = parseInt(req.params.moduleId);
    const db = getDatabase();

    const module = await db.get(
      `SELECT 
        m.*,
        creator.username as created_by_username
       FROM modules m
       LEFT JOIN users creator ON m.created_by = creator.id
       WHERE m.id = ? AND m.is_active = 1`,
      [moduleId]
    );

    if (!module) {
      return res.status(404).json({ error: '模组不存在' });
    }

    // 检查用户权限
    if (req.user) {
      if (req.user.role !== 'admin') {
        const permission = await db.get(
          'SELECT permission_type FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
          [req.user.userId, moduleId]
        );
        
        if (!permission) {
          return res.status(403).json({ error: '没有访问此模组的权限' });
        }
        
        module.user_permission = permission.permission_type;
      }
    } else {
      return res.status(401).json({ error: '需要登录才能查看模组详情' });
    }

    res.json({ module });
  } catch (error) {
    console.error('获取模组信息失败:', error);
    res.status(500).json({ error: '获取模组信息失败' });
  }
});

// 创建模组（管理员）
router.post('/', authenticateToken, requireAdmin, upload.single('file'), [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('模组名称长度必须在1-100个字符之间'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('描述长度不能超过1000个字符'),
  body('version')
    .isLength({ min: 1, max: 20 })
    .withMessage('版本号长度必须在1-20个字符之间'),
  body('author')
    .optional()
    .isLength({ max: 100 })
    .withMessage('作者名称长度不能超过100个字符'),
  body('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('分类长度不能超过50个字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const { name, description, version, author, category } = req.body;
    const db = getDatabase();

    // 检查模组名称是否已存在
    const existingModule = await db.get(
      'SELECT id FROM modules WHERE name = ? AND version = ?',
      [name, version]
    );

    if (existingModule) {
      return res.status(409).json({ error: '相同名称和版本的模组已存在' });
    }

    // 创建模组记录
    const result = await db.run(
      'INSERT INTO modules (name, description, version, author, category, created_by) VALUES (?, ?, ?, ?, ?, ?)',
      [name, description || null, version, author || null, category || null, req.user.userId]
    );

    const moduleId = result.id;

    // 如果有文件上传，处理文件
    if (req.file) {
      try {
        // 创建模组文件目录
        const moduleDir = path.join(__dirname, '..', 'data', 'modules', moduleId.toString());
        await fs.ensureDir(moduleDir);

        // 生成最终文件名
        const finalFileName = `${name.replace(/[^a-zA-Z0-9\-_]/g, '_')}-v${version}${path.extname(req.file.originalname)}`;
        const finalFilePath = path.join(moduleDir, finalFileName);
        const relativeFilePath = path.join(moduleId.toString(), finalFileName);

        // 移动文件到最终位置
        await fs.move(req.file.path, finalFilePath);

        // 获取文件大小
        const stats = await fs.stat(finalFilePath);
        const fileSize = stats.size;

        // 更新模组记录
        await db.run(
          'UPDATE modules SET file_path = ?, file_size = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [relativeFilePath, fileSize, moduleId]
        );
      } catch (fileError) {
        console.error('文件处理失败:', fileError);
        // 如果文件处理失败，删除模组记录
        await db.run('DELETE FROM modules WHERE id = ?', [moduleId]);
        return res.status(500).json({ error: '文件上传处理失败' });
      }
    }

    // 返回创建的模组信息
    const newModule = await db.get(
      `SELECT
        m.*,
        creator.username as created_by_username
       FROM modules m
       LEFT JOIN users creator ON m.created_by = creator.id
       WHERE m.id = ?`,
      [moduleId]
    );

    res.status(201).json({
      message: '模组创建成功',
      module: newModule
    });
  } catch (error) {
    console.error('创建模组失败:', error);
    // 清理上传的文件
    if (req.file && req.file.path) {
      try {
        await fs.remove(req.file.path);
      } catch (cleanupError) {
        console.error('清理临时文件失败:', cleanupError);
      }
    }
    res.status(500).json({ error: '创建模组失败' });
  }
});

// 更新模组信息（管理员）
router.put('/:moduleId', authenticateToken, requireAdmin, [
  body('name').optional().isLength({ min: 1, max: 100 }).withMessage('模组名称长度必须在1-100个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('描述长度不能超过1000个字符'),
  body('version').optional().isLength({ min: 1, max: 20 }).withMessage('版本号长度必须在1-20个字符之间'),
  body('author').optional().isLength({ max: 100 }).withMessage('作者名称长度不能超过100个字符'),
  body('category').optional().isLength({ max: 50 }).withMessage('分类长度不能超过50个字符'),
  body('is_active').optional().isBoolean().withMessage('激活状态必须是布尔值')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const moduleId = parseInt(req.params.moduleId);
    const { name, description, version, author, category, is_active } = req.body;
    const db = getDatabase();

    // 检查模组是否存在
    const existingModule = await db.get('SELECT id, name, version FROM modules WHERE id = ?', [moduleId]);
    if (!existingModule) {
      return res.status(404).json({ error: '模组不存在' });
    }

    // 如果更新名称或版本，检查是否与其他模组冲突
    if (name || version) {
      const checkName = name || existingModule.name;
      const checkVersion = version || existingModule.version;
      
      const conflictModule = await db.get(
        'SELECT id FROM modules WHERE name = ? AND version = ? AND id != ?',
        [checkName, checkVersion, moduleId]
      );
      
      if (conflictModule) {
        return res.status(409).json({ error: '相同名称和版本的模组已存在' });
      }
    }

    // 构建更新语句
    const updateFields = [];
    const updateValues = [];
    
    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    
    if (version !== undefined) {
      updateFields.push('version = ?');
      updateValues.push(version);
    }
    
    if (author !== undefined) {
      updateFields.push('author = ?');
      updateValues.push(author);
    }
    
    if (category !== undefined) {
      updateFields.push('category = ?');
      updateValues.push(category);
    }
    
    if (is_active !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(is_active ? 1 : 0);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的字段' });
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(moduleId);

    // 执行更新
    await db.run(
      `UPDATE modules SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 返回更新后的模组信息
    const updatedModule = await db.get(
      `SELECT 
        m.*,
        creator.username as created_by_username
       FROM modules m
       LEFT JOIN users creator ON m.created_by = creator.id
       WHERE m.id = ?`,
      [moduleId]
    );

    res.json({
      message: '模组信息更新成功',
      module: updatedModule
    });
  } catch (error) {
    console.error('更新模组信息失败:', error);
    res.status(500).json({ error: '更新模组信息失败' });
  }
});

// 删除模组（管理员）
router.delete('/:moduleId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const moduleId = parseInt(req.params.moduleId);
    const db = getDatabase();

    // 检查模组是否存在
    const module = await db.get('SELECT id, name, file_path FROM modules WHERE id = ?', [moduleId]);
    if (!module) {
      return res.status(404).json({ error: '模组不存在' });
    }

    // 删除模组文件
    if (module.file_path) {
      const filePath = path.join(__dirname, '..', 'data', 'modules', module.file_path);
      try {
        await fs.remove(filePath);
      } catch (error) {
        console.warn('删除模组文件失败:', error.message);
      }
    }

    // 删除模组记录（级联删除相关数据）
    await db.run('DELETE FROM modules WHERE id = ?', [moduleId]);

    res.json({ message: `模组 ${module.name} 删除成功` });
  } catch (error) {
    console.error('删除模组失败:', error);
    res.status(500).json({ error: '删除模组失败' });
  }
});

// 下载模组文件
router.get('/:moduleId/download', authenticateToken, async (req, res) => {
  try {
    const moduleId = parseInt(req.params.moduleId);
    const db = getDatabase();

    // 获取模组信息
    const module = await db.get(
      'SELECT id, name, file_path, file_size FROM modules WHERE id = ? AND is_active = 1',
      [moduleId]
    );

    if (!module) {
      return res.status(404).json({ error: '模组不存在' });
    }

    if (!module.file_path) {
      return res.status(404).json({ error: '模组文件不存在' });
    }

    // 检查用户权限
    if (req.user.role !== 'admin') {
      const permission = await db.get(
        'SELECT permission_type FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
        [req.user.userId, moduleId]
      );
      
      if (!permission || permission.permission_type !== 'download') {
        return res.status(403).json({ error: '没有下载此模组的权限' });
      }
    }

    // 构建文件路径
    const filePath = path.join(__dirname, '..', 'data', 'modules', module.file_path);
    
    // 检查文件是否存在
    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({ error: '模组文件不存在' });
    }

    // 记录下载
    await db.run(
      'INSERT INTO module_downloads (user_id, module_id, ip_address) VALUES (?, ?, ?)',
      [req.user.userId, moduleId, req.ip]
    );

    // 更新下载计数
    await db.run(
      'UPDATE modules SET download_count = download_count + 1 WHERE id = ?',
      [moduleId]
    );

    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${module.name}.zip"`);
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Length', module.file_size);

    // 发送文件
    res.sendFile(filePath);
  } catch (error) {
    console.error('下载模组失败:', error);
    res.status(500).json({ error: '下载模组失败' });
  }
});

// 获取模组分类列表
router.get('/categories/list', async (req, res) => {
  try {
    const db = getDatabase();
    
    const categories = await db.all(
      `SELECT 
        category,
        COUNT(*) as count
       FROM modules 
       WHERE is_active = 1 AND category IS NOT NULL
       GROUP BY category
       ORDER BY count DESC, category ASC`
    );

    res.json({ categories });
  } catch (error) {
    console.error('获取模组分类失败:', error);
    res.status(500).json({ error: '获取模组分类失败' });
  }
});

// 获取模组统计信息
router.get('/stats/overview', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const db = getDatabase();
    
    const stats = await db.get(
      `SELECT 
        COUNT(*) as total_modules,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_modules,
        SUM(download_count) as total_downloads,
        SUM(file_size) as total_size
       FROM modules`
    );

    const categoryStats = await db.all(
      `SELECT 
        category,
        COUNT(*) as count
       FROM modules 
       WHERE is_active = 1
       GROUP BY category
       ORDER BY count DESC`
    );

    const recentDownloads = await db.all(
      `SELECT 
        m.name as module_name,
        u.username,
        md.download_at
       FROM module_downloads md
       JOIN modules m ON md.module_id = m.id
       JOIN users u ON md.user_id = u.id
       ORDER BY md.download_at DESC
       LIMIT 10`
    );

    res.json({
      stats,
      categoryStats,
      recentDownloads
    });
  } catch (error) {
    console.error('获取模组统计失败:', error);
    res.status(500).json({ error: '获取模组统计失败' });
  }
});

module.exports = router;