const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult, query } = require('express-validator');
const { getDatabase } = require('../database/init');
const { authenticateToken, requireAdmin, requireOwnerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取用户列表（管理员）
router.get('/', authenticateToken, requireAdmin, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词过长'),
  query('role').optional().isIn(['admin', 'user']).withMessage('角色参数无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '参数验证失败', 
        details: errors.array() 
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const role = req.query.role;
    const offset = (page - 1) * limit;

    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    let params = [];
    
    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    if (role) {
      whereClause += ' AND role = ?';
      params.push(role);
    }

    // 获取总数
    const countResult = await db.get(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    );
    const total = countResult.total;

    // 获取用户列表
    const users = await db.all(
      `SELECT id, username, email, role, avatar_url, created_at, last_login, is_active 
       FROM users ${whereClause} 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    res.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ error: '获取用户列表失败' });
  }
});

// 获取单个用户信息
router.get('/:userId', authenticateToken, requireOwnerOrAdmin('userId'), async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const db = getDatabase();

    const user = await db.get(
      'SELECT id, username, email, role, avatar_url, created_at, last_login, is_active FROM users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 如果是管理员，返回更多信息
    if (req.user.role === 'admin') {
      // 获取用户的模组权限统计
      const moduleStats = await db.get(
        `SELECT 
          COUNT(DISTINCT ump.module_id) as accessible_modules,
          COUNT(DISTINCT md.module_id) as downloaded_modules
         FROM users u
         LEFT JOIN user_module_permissions ump ON u.id = ump.user_id
         LEFT JOIN module_downloads md ON u.id = md.user_id
         WHERE u.id = ?`,
        [userId]
      );

      user.stats = moduleStats;
    }

    res.json({ user });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({ error: '获取用户信息失败' });
  }
});

// 创建用户（管理员）
router.post('/', authenticateToken, requireAdmin, [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('role')
    .isIn(['admin', 'user'])
    .withMessage('角色必须是admin或user')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const { username, email, password, role, avatar_url } = req.body;
    const db = getDatabase();

    // 检查用户名和邮箱是否已存在
    const existingUser = await db.get(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUser) {
      return res.status(409).json({ error: '用户名或邮箱已存在' });
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 12);

    // 创建用户
    const result = await db.run(
      'INSERT INTO users (username, email, password_hash, role, avatar_url) VALUES (?, ?, ?, ?, ?)',
      [username, email, passwordHash, role, avatar_url || null]
    );

    // 返回创建的用户信息（不包含密码）
    const newUser = await db.get(
      'SELECT id, username, email, role, avatar_url, created_at FROM users WHERE id = ?',
      [result.id]
    );

    res.status(201).json({
      message: '用户创建成功',
      user: newUser
    });
  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({ error: '创建用户失败' });
  }
});

// 更新用户信息（管理员或用户本人）
router.put('/:userId', authenticateToken, requireOwnerOrAdmin('userId'), [
  body('email').optional().isEmail().withMessage('请输入有效的邮箱地址'),
  body('role').optional().isIn(['admin', 'user']).withMessage('角色必须是admin或user'),
  body('avatar_url').optional().isURL().withMessage('请输入有效的头像URL'),
  body('is_active').optional().isBoolean().withMessage('激活状态必须是布尔值')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const userId = parseInt(req.params.userId);
    const { email, role, avatar_url, is_active } = req.body;
    const db = getDatabase();

    // 检查用户是否存在
    const existingUser = await db.get('SELECT id, role FROM users WHERE id = ?', [userId]);
    if (!existingUser) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 非管理员用户不能修改角色和激活状态
    if (req.user.role !== 'admin' && (role !== undefined || is_active !== undefined)) {
      return res.status(403).json({ error: '权限不足，无法修改角色或激活状态' });
    }

    // 检查邮箱是否已被其他用户使用
    if (email) {
      const emailExists = await db.get(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, userId]
      );
      
      if (emailExists) {
        return res.status(409).json({ error: '邮箱已被其他用户使用' });
      }
    }

    // 构建更新语句
    const updateFields = [];
    const updateValues = [];
    
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    
    if (role !== undefined && req.user.role === 'admin') {
      updateFields.push('role = ?');
      updateValues.push(role);
    }
    
    if (avatar_url !== undefined) {
      updateFields.push('avatar_url = ?');
      updateValues.push(avatar_url);
    }
    
    if (is_active !== undefined && req.user.role === 'admin') {
      updateFields.push('is_active = ?');
      updateValues.push(is_active ? 1 : 0);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({ error: '没有提供要更新的字段' });
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(userId);

    // 执行更新
    await db.run(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 返回更新后的用户信息
    const updatedUser = await db.get(
      'SELECT id, username, email, role, avatar_url, created_at, updated_at, is_active FROM users WHERE id = ?',
      [userId]
    );

    res.json({
      message: '用户信息更新成功',
      user: updatedUser
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({ error: '更新用户信息失败' });
  }
});

// 删除用户（管理员）
router.delete('/:userId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const db = getDatabase();

    // 检查用户是否存在
    const user = await db.get('SELECT id, username FROM users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 不能删除自己
    if (userId === req.user.userId) {
      return res.status(400).json({ error: '不能删除自己的账户' });
    }

    // 删除用户（级联删除相关数据）
    await db.run('DELETE FROM users WHERE id = ?', [userId]);

    res.json({ message: `用户 ${user.username} 删除成功` });
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({ error: '删除用户失败' });
  }
});

// 重置用户密码（管理员）
router.post('/:userId/reset-password', authenticateToken, requireAdmin, [
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少6个字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const userId = parseInt(req.params.userId);
    const { newPassword } = req.body;
    const db = getDatabase();

    // 检查用户是否存在
    const user = await db.get('SELECT id, username FROM users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 加密新密码
    const passwordHash = await bcrypt.hash(newPassword, 12);

    // 更新密码
    await db.run(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [passwordHash, userId]
    );

    // 清除该用户的所有会话
    await db.run('DELETE FROM user_sessions WHERE user_id = ?', [userId]);

    res.json({ message: `用户 ${user.username} 的密码重置成功` });
  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({ error: '重置密码失败' });
  }
});

// 获取用户的模组权限
router.get('/:userId/module-permissions', authenticateToken, requireOwnerOrAdmin('userId'), async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const db = getDatabase();

    const permissions = await db.all(
      `SELECT 
        ump.id,
        ump.module_id,
        ump.permission_type,
        ump.granted_at,
        m.name as module_name,
        m.version as module_version,
        m.category as module_category,
        grantor.username as granted_by_username
       FROM user_module_permissions ump
       JOIN modules m ON ump.module_id = m.id
       LEFT JOIN users grantor ON ump.granted_by = grantor.id
       WHERE ump.user_id = ?
       ORDER BY ump.granted_at DESC`,
      [userId]
    );

    res.json({ permissions });
  } catch (error) {
    console.error('获取用户模组权限失败:', error);
    res.status(500).json({ error: '获取用户模组权限失败' });
  }
});

// 授予用户模组权限（管理员）
router.post('/:userId/module-permissions', authenticateToken, requireAdmin, [
  body('moduleId').isInt({ min: 1 }).withMessage('模组ID必须是正整数'),
  body('permissionType').isIn(['read', 'download']).withMessage('权限类型必须是read或download')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: '输入验证失败', 
        details: errors.array() 
      });
    }

    const userId = parseInt(req.params.userId);
    const { moduleId, permissionType } = req.body;
    const db = getDatabase();

    // 检查用户和模组是否存在
    const user = await db.get('SELECT id FROM users WHERE id = ?', [userId]);
    const module = await db.get('SELECT id FROM modules WHERE id = ?', [moduleId]);
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    
    if (!module) {
      return res.status(404).json({ error: '模组不存在' });
    }

    // 检查权限是否已存在
    const existingPermission = await db.get(
      'SELECT id FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
      [userId, moduleId]
    );

    if (existingPermission) {
      // 更新现有权限
      await db.run(
        'UPDATE user_module_permissions SET permission_type = ?, granted_by = ? WHERE id = ?',
        [permissionType, req.user.userId, existingPermission.id]
      );
    } else {
      // 创建新权限
      await db.run(
        'INSERT INTO user_module_permissions (user_id, module_id, permission_type, granted_by) VALUES (?, ?, ?, ?)',
        [userId, moduleId, permissionType, req.user.userId]
      );
    }

    res.json({ message: '模组权限授予成功' });
  } catch (error) {
    console.error('授予模组权限失败:', error);
    res.status(500).json({ error: '授予模组权限失败' });
  }
});

// 撤销用户模组权限（管理员）
router.delete('/:userId/module-permissions/:moduleId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const moduleId = parseInt(req.params.moduleId);
    const db = getDatabase();

    const result = await db.run(
      'DELETE FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
      [userId, moduleId]
    );

    if (result.changes === 0) {
      return res.status(404).json({ error: '权限记录不存在' });
    }

    res.json({ message: '模组权限撤销成功' });
  } catch (error) {
    console.error('撤销模组权限失败:', error);
    res.status(500).json({ error: '撤销模组权限失败' });
  }
});

// 批量授予用户模组权限（管理员）
router.post('/:userId/module-permissions/batch', authenticateToken, requireAdmin, [
  body('moduleIds').isArray({ min: 1 }).withMessage('模组ID列表不能为空'),
  body('moduleIds.*').isInt({ min: 1 }).withMessage('模组ID必须是正整数'),
  body('permissionType').isIn(['read', 'download']).withMessage('权限类型必须是read或download')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const userId = parseInt(req.params.userId);
    const { moduleIds, permissionType } = req.body;
    const db = getDatabase();

    // 检查用户是否存在
    const user = await db.get('SELECT id FROM users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 检查所有模组是否存在
    const modules = await db.all(
      `SELECT id FROM modules WHERE id IN (${moduleIds.map(() => '?').join(',')})`,
      moduleIds
    );

    if (modules.length !== moduleIds.length) {
      return res.status(404).json({ error: '部分模组不存在' });
    }

    // 批量处理权限
    const results = [];
    for (const moduleId of moduleIds) {
      try {
        // 检查权限是否已存在
        const existingPermission = await db.get(
          'SELECT id FROM user_module_permissions WHERE user_id = ? AND module_id = ?',
          [userId, moduleId]
        );

        if (existingPermission) {
          // 更新现有权限
          await db.run(
            'UPDATE user_module_permissions SET permission_type = ?, granted_by = ? WHERE id = ?',
            [permissionType, req.user.userId, existingPermission.id]
          );
          results.push({ moduleId, action: 'updated' });
        } else {
          // 创建新权限
          await db.run(
            'INSERT INTO user_module_permissions (user_id, module_id, permission_type, granted_by) VALUES (?, ?, ?, ?)',
            [userId, moduleId, permissionType, req.user.userId]
          );
          results.push({ moduleId, action: 'created' });
        }
      } catch (error) {
        console.error(`处理模组 ${moduleId} 权限失败:`, error);
        results.push({ moduleId, action: 'failed', error: error.message });
      }
    }

    res.json({
      message: '批量权限操作完成',
      results
    });
  } catch (error) {
    console.error('批量授予模组权限失败:', error);
    res.status(500).json({ error: '批量授予模组权限失败' });
  }
});

// 撤销用户所有模组权限（管理员）
router.delete('/:userId/module-permissions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const db = getDatabase();

    // 检查用户是否存在
    const user = await db.get('SELECT id FROM users WHERE id = ?', [userId]);
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 删除所有权限
    const result = await db.run('DELETE FROM user_module_permissions WHERE user_id = ?', [userId]);

    res.json({
      message: '用户所有模组权限撤销成功',
      deletedCount: result.changes
    });
  } catch (error) {
    console.error('撤销用户所有模组权限失败:', error);
    res.status(500).json({ error: '撤销用户所有模组权限失败' });
  }
});

module.exports = router;