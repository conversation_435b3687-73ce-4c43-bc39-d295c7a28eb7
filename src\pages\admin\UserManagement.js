import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../context/AuthContext';
import authService from '../../services/authService';
import gameResourceService from '../../services/gameResourceService';

const Container = styled.div`
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  color: #fff;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.6) 100%);
  min-height: 100vh;
  backdrop-filter: blur(10px);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 107, 53, 0.05);
  border: 1px solid rgba(255, 107, 53, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const Title = styled.h1`
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b35 0%, #ff9d75 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
`;

const Button = styled.button`
  background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)' : 'linear-gradient(135deg, #ff6b35 0%, #ff8555 100%)'};
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);

  &:hover {
    background: ${props => props.variant === 'danger' ? 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)' : 'linear-gradient(135deg, #ff8555 0%, #ff9d75 100%)'};
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
  }

  &:active {
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: rgba(45, 45, 45, 0.9);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const Th = styled.th`
  background: linear-gradient(135deg, #ff6b35 0%, #ff8555 100%);
  color: white;
  padding: 18px 20px;
  text-align: left;
  font-weight: 700;
  font-size: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(255, 107, 53, 0.3);
`;

const Td = styled.td`
  padding: 18px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 14px;
  transition: background 0.3s ease;
  
  tr:hover & {
    background: rgba(255, 107, 53, 0.05);
  }
`;

const Tr = styled.tr`
  &:hover {
    background: ${props => props.theme.colors.hover};
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
`;

const ModalContent = styled.div`
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(20px);
  padding: 32px;
  border-radius: 20px;
  width: 90%;
  max-width: 520px;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 107, 53, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  animation: slideUp 0.3s ease;
  
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const FormGroup = styled.div`
  margin-bottom: 24px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #fff;
  font-size: 15px;
  letter-spacing: 0.3px;
`;

const Input = styled.input`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  background: rgba(45, 45, 45, 0.8);
  color: #fff;
  font-size: 15px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  
  &:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
    background: rgba(55, 55, 55, 0.9);
  }
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 14px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  background: rgba(45, 45, 45, 0.8);
  color: #fff;
  font-size: 15px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
    background: rgba(55, 55, 55, 0.9);
  }
  
  option {
    background: #2d2d2d;
    color: #fff;
  }
`;

const CheckboxGroup = styled.div`
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 5px;
  padding: 10px;
`;

const CheckboxItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  input {
    margin-right: 10px;
  }

  label {
    margin: 0;
    cursor: pointer;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.colors.error};
  margin-bottom: 15px;
  padding: 10px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 5px;
`;

const UserManagement = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [authorizingUser, setAuthorizingUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    role: 'user'
  });
  const [authData, setAuthData] = useState({
    selectedModules: [],
    permissionType: 'download'
  });

  useEffect(() => {
    if (user?.role === 'admin') {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [usersData, modulesData] = await Promise.all([
        authService.getAllUsers(),
        gameResourceService.getAllModules()
      ]);
      setUsers(usersData);
      setModules(modulesData);
    } catch (err) {
      setError('加载数据失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setFormData({
      username: '',
      email: '',
      password: '',
      role: 'user'
    });
    setShowModal(true);
  };

  const handleEditUser = (userToEdit) => {
    setEditingUser(userToEdit);
    setFormData({
      username: userToEdit.username,
      email: userToEdit.email,
      password: '',
      role: userToEdit.role
    });
    setShowModal(true);
  };

  const handleAuthorizeUser = (userToAuthorize) => {
    setAuthorizingUser(userToAuthorize);
    setAuthData({
      selectedModules: [],
      permissionType: 'download'
    });
    setShowAuthModal(true);
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('确定要删除这个用户吗？')) {
      try {
        await authService.deleteUser(userId);
        await loadData();
      } catch (err) {
        setError('删除用户失败: ' + err.message);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingUser) {
        const updateData = { ...formData };
        if (!updateData.password) {
          delete updateData.password;
        }
        await authService.updateUser(editingUser.id, updateData);
      } else {
        await authService.createUser(formData);
      }
      setShowModal(false);
      await loadData();
    } catch (err) {
      setError('保存用户失败: ' + err.message);
    }
  };

  const handleAuthSubmit = async (e) => {
    e.preventDefault();
    try {
      if (authData.selectedModules.length > 0) {
        await authService.batchGrantModulePermissions(
          authorizingUser.id,
          authData.selectedModules,
          authData.permissionType
        );
      }
      setShowAuthModal(false);
      await loadData();
    } catch (err) {
      setError('授权失败: ' + err.message);
    }
  };

  const handleModuleToggle = (moduleId) => {
    setAuthData(prev => ({
      ...prev,
      selectedModules: prev.selectedModules.includes(moduleId)
        ? prev.selectedModules.filter(id => id !== moduleId)
        : [...prev.selectedModules, moduleId]
    }));
  };

  if (user?.role !== 'admin') {
    return (
      <Container>
        <ErrorMessage>您没有权限访问此页面</ErrorMessage>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container>
        <div>加载中...</div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>用户管理</Title>
        <Button onClick={handleCreateUser}>添加用户</Button>
      </Header>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <Table>
        <thead>
          <tr>
            <Th>ID</Th>
            <Th>用户名</Th>
            <Th>邮箱</Th>
            <Th>角色</Th>
            <Th>状态</Th>
            <Th>操作</Th>
          </tr>
        </thead>
        <tbody>
          {users.map(userItem => (
            <Tr key={userItem.id}>
              <Td>{userItem.id}</Td>
              <Td>{userItem.username}</Td>
              <Td>{userItem.email}</Td>
              <Td>{userItem.role === 'admin' ? '管理员' : '普通用户'}</Td>
              <Td>{userItem.is_active ? '激活' : '禁用'}</Td>
              <Td>
                <ButtonGroup>
                  <Button onClick={() => handleEditUser(userItem)}>编辑</Button>
                  {userItem.role !== 'admin' && (
                    <>
                      <Button
                        variant="secondary"
                        onClick={() => handleAuthorizeUser(userItem)}
                      >
                        模组授权
                      </Button>
                      <Button
                        variant="danger"
                        onClick={() => handleDeleteUser(userItem.id)}
                      >
                        删除
                      </Button>
                    </>
                  )}
                </ButtonGroup>
              </Td>
            </Tr>
          ))}
        </tbody>
      </Table>

      {showModal && (
        <Modal onClick={() => setShowModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <h2>{editingUser ? '编辑用户' : '添加用户'}</h2>
            <form onSubmit={handleSubmit}>
              <FormGroup>
                <Label>用户名</Label>
                <Input
                  type="text"
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>邮箱</Label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>密码 {editingUser && '(留空表示不修改)'}</Label>
                <Input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  required={!editingUser}
                />
              </FormGroup>

              <FormGroup>
                <Label>角色</Label>
                <Select
                  value={formData.role}
                  onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
                >
                  <option value="user">普通用户</option>
                  <option value="admin">管理员</option>
                </Select>
              </FormGroup>

              <ButtonGroup>
                <Button type="button" onClick={() => setShowModal(false)}>取消</Button>
                <Button type="submit">{editingUser ? '更新' : '创建'}</Button>
              </ButtonGroup>
            </form>
          </ModalContent>
        </Modal>
      )}

      {/* 模组授权模态框 */}
      {showAuthModal && (
        <Modal onClick={() => setShowAuthModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <h2>模组授权 - {authorizingUser?.username}</h2>
            <form onSubmit={handleAuthSubmit}>
              <FormGroup>
                <Label>权限类型</Label>
                <Select
                  value={authData.permissionType}
                  onChange={(e) => setAuthData(prev => ({ ...prev, permissionType: e.target.value }))}
                >
                  <option value="read">只读</option>
                  <option value="download">下载</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>选择模组</Label>
                <CheckboxGroup>
                  {modules.map(module => (
                    <CheckboxItem key={module.id}>
                      <input
                        type="checkbox"
                        checked={authData.selectedModules.includes(module.id)}
                        onChange={() => handleModuleToggle(module.id)}
                      />
                      <label>{module.name} (v{module.version})</label>
                    </CheckboxItem>
                  ))}
                </CheckboxGroup>
              </FormGroup>

              <ButtonGroup>
                <Button type="button" onClick={() => setShowAuthModal(false)}>取消</Button>
                <Button type="submit">授权</Button>
              </ButtonGroup>
            </form>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default UserManagement;