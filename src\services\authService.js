import axios from 'axios';

// 连接到真实后端API
const MOCK_MODE = false;
const API_BASE_URL = 'http://localhost:3001/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    permissions: ['all'],
    authorizedModules: []
  },
  {
    id: 2,
    username: 'demo',
    email: '<EMAIL>',
    password: 'demo123',
    role: 'user',
    permissions: [],
    authorizedModules: []
  }
];

// 模拟模组类型数据
const mockModuleTypes = [
  { id: 1, name: '喷气发动机飞机', code: 'aircraft-jet', description: '现代喷气式战斗机和攻击机' },
  { id: 2, name: '活塞发动机飞机', code: 'aircraft-prop', description: '二战时期的螺旋桨飞机' },
  { id: 3, name: '地形', code: 'terrain', description: '游戏地图和地形' },
  { id: 4, name: '战役', code: 'campaign', description: '单人战役任务' }
];

// 模拟所有用户数据（用于管理员管理）
const mockAllUsers = [...mockUsers];

// 模拟API响应
const mockAPI = {
  login: async (credentials) => {
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟
    const user = mockUsers.find(u => 
      (u.username === credentials.username || u.email === credentials.username) && 
      u.password === credentials.password
    );
    if (user) {
      const token = 'mock_token_' + Date.now();
      return {
        success: true,
        token,
        user: { 
          id: user.id, 
          username: user.username, 
          email: user.email,
          role: user.role,
          permissions: user.permissions,
          authorizedModules: user.authorizedModules
        }
      };
    } else {
      throw new Error('用户名或密码错误');
    }
  },
  register: async (userData) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const existingUser = mockUsers.find(u => 
      u.username === userData.username || u.email === userData.email
    );
    if (existingUser) {
      throw new Error('用户名或邮箱已存在');
    }
    const newUser = {
      id: mockUsers.length + 1,
      username: userData.username,
      email: userData.email,
      password: userData.password,
      role: 'user',
      permissions: [],
      authorizedModules: []
    };
    mockUsers.push(newUser);
    mockAllUsers.push(newUser);
    const token = 'mock_token_' + Date.now();
    return {
      success: true,
      token,
      user: { 
        id: newUser.id, 
        username: newUser.username, 
        email: newUser.email,
        role: newUser.role,
        permissions: newUser.permissions,
        authorizedModules: newUser.authorizedModules
      }
    };
  },
  verifyToken: async (token) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    if (token && token.startsWith('mock_token_')) {
      return mockUsers[0]; // 返回默认用户
    }
    throw new Error('Invalid token');
  },

  // 管理员API - 获取所有用户
  getAllUsers: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockAllUsers.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      authorizedModules: user.authorizedModules
    }));
  },

  // 管理员API - 创建用户
  createUser: async (userData) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const existingUser = mockAllUsers.find(u => 
      u.username === userData.username || u.email === userData.email
    );
    if (existingUser) {
      throw new Error('用户名或邮箱已存在');
    }
    const newUser = {
      id: mockAllUsers.length + 1,
      username: userData.username,
      email: userData.email,
      password: userData.password || 'default123',
      role: userData.role || 'user',
      permissions: userData.permissions || [],
      authorizedModules: userData.authorizedModules || []
    };
    mockAllUsers.push(newUser);
    return {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      role: newUser.role,
      permissions: newUser.permissions,
      authorizedModules: newUser.authorizedModules
    };
  },

  // 管理员API - 更新用户
  updateUser: async (userId, userData) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const userIndex = mockAllUsers.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }
    mockAllUsers[userIndex] = { ...mockAllUsers[userIndex], ...userData };
    return {
      id: mockAllUsers[userIndex].id,
      username: mockAllUsers[userIndex].username,
      email: mockAllUsers[userIndex].email,
      role: mockAllUsers[userIndex].role,
      permissions: mockAllUsers[userIndex].permissions,
      authorizedModules: mockAllUsers[userIndex].authorizedModules
    };
  },

  // 管理员API - 删除用户
  deleteUser: async (userId) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const userIndex = mockAllUsers.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }
    if (mockAllUsers[userIndex].role === 'admin') {
      throw new Error('不能删除管理员用户');
    }
    mockAllUsers.splice(userIndex, 1);
    return { success: true };
  },

  // 管理员API - 获取模组类型
  getModuleTypes: async () => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return mockModuleTypes;
  },

  // 管理员API - 创建模组类型
  createModuleType: async (typeData) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const existingType = mockModuleTypes.find(t => t.code === typeData.code);
    if (existingType) {
      throw new Error('模组类型代码已存在');
    }
    const newType = {
      id: mockModuleTypes.length + 1,
      name: typeData.name,
      code: typeData.code,
      description: typeData.description
    };
    mockModuleTypes.push(newType);
    return newType;
  },

  // 管理员API - 更新模组类型
  updateModuleType: async (typeId, typeData) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);
    if (typeIndex === -1) {
      throw new Error('模组类型不存在');
    }
    mockModuleTypes[typeIndex] = { ...mockModuleTypes[typeIndex], ...typeData };
    return mockModuleTypes[typeIndex];
  },

  // 管理员API - 删除模组类型
  deleteModuleType: async (typeId) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    const typeIndex = mockModuleTypes.findIndex(t => t.id === typeId);
    if (typeIndex === -1) {
      throw new Error('模组类型不存在');
    }
    mockModuleTypes.splice(typeIndex, 1);
    return { success: true };
  }
};

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('dcs_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理token过期
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，清除本地存储并重定向到登录页
      localStorage.removeItem('dcs_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const authService = {
  // 用户登录
  async login(credentials) {
    try {
      if (MOCK_MODE) {
        const response = await mockAPI.login(credentials);
        if (response.token) {
          localStorage.setItem('dcs_token', response.token);
        }
        return response;
      } else {
        const response = await api.post('/auth/login', credentials);
        return response;
      }
    } catch (error) {
      throw error;
    }
  },

  // 用户注册
  async register(userData) {
    try {
      if (MOCK_MODE) {
        const response = await mockAPI.register(userData);
        if (response.token) {
          localStorage.setItem('dcs_token', response.token);
        }
        return response;
      } else {
        const response = await api.post('/auth/register', userData);
        return response;
      }
    } catch (error) {
      throw error;
    }
  },

  // 验证token
  async verifyToken(token) {
    try {
      if (MOCK_MODE) {
        const user = await mockAPI.verifyToken(token);
        return user;
      } else {
        const response = await api.get('/auth/verify', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        return response.user;
      }
    } catch (error) {
      throw error;
    }
  },

  // 获取用户资料
  async getProfile() {
    try {
      const response = await api.get('/auth/profile');
      return response.user;
    } catch (error) {
      throw error;
    }
  },

  // 更新用户资料
  async updateProfile(profileData) {
    try {
      const response = await api.put('/auth/profile', profileData);
      return response.user;
    } catch (error) {
      throw error;
    }
  },

  // 修改密码
  async changePassword(passwordData) {
    try {
      const response = await api.put('/auth/change-password', passwordData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 重置密码请求
  async requestPasswordReset(email) {
    try {
      const response = await api.post('/auth/reset-password-request', { email });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 重置密码
  async resetPassword(resetData) {
    try {
      const response = await api.post('/auth/reset-password', resetData);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 刷新token
  async refreshToken() {
    try {
      const response = await api.post('/auth/refresh');
      const { token } = response;
      localStorage.setItem('dcs_token', token);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 登出
  async logout() {
    try {
      await api.post('/auth/logout');
      localStorage.removeItem('dcs_token');
    } catch (error) {
      // 即使请求失败也要清除本地token
      localStorage.removeItem('dcs_token');
      throw error;
    }
  },

  // 管理员功能 - 获取所有用户
  async getAllUsers() {
    try {
      if (MOCK_MODE) {
        return await mockAPI.getAllUsers();
      } else {
        const response = await api.get('/users');
        return response.data.users || [];
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 创建用户
  async createUser(userData) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.createUser(userData);
      } else {
        const response = await api.post('/users', userData);
        return response.data.user;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 更新用户
  async updateUser(userId, userData) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.updateUser(userId, userData);
      } else {
        const response = await api.put(`/users/${userId}`, userData);
        return response.data.user;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 删除用户
  async deleteUser(userId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.deleteUser(userId);
      } else {
        const response = await api.delete(`/users/${userId}`);
        return response.data;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 获取模组类型
  async getModuleTypes() {
    try {
      if (MOCK_MODE) {
        return await mockAPI.getModuleTypes();
      } else {
        const response = await api.get('/admin/module-types/types');
        return response.types;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 创建模组类型
  async createModuleType(typeData) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.createModuleType(typeData);
      } else {
        const response = await api.post('/admin/module-types/types', typeData);
        return response.type;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 更新模组类型
  async updateModuleType(typeId, typeData) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.updateModuleType(typeId, typeData);
      } else {
        const response = await api.put(`/admin/module-types/types/${typeId}`, typeData);
        return response.type;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 删除模组类型
  async deleteModuleType(typeId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.deleteModuleType(typeId);
      } else {
        const response = await api.delete(`/admin/module-types/types/${typeId}`);
        return response;
      }
    } catch (error) {
      throw error;
    }
  },

  // 获取用户模组权限
  async getUserModulePermissions(userId) {
    try {
      const response = await api.get(`/users/${userId}/module-permissions`);
      return response.data.permissions || [];
    } catch (error) {
      throw error;
    }
  },

  // 授予用户模组权限
  async grantModulePermission(userId, moduleId, permissionType = 'download') {
    try {
      const response = await api.post(`/users/${userId}/module-permissions`, {
        moduleId,
        permissionType
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // 批量授予用户模组权限
  async batchGrantModulePermissions(userId, moduleIds, permissionType = 'download') {
    try {
      const response = await api.post(`/users/${userId}/module-permissions/batch`, {
        moduleIds,
        permissionType
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // 撤销用户模组权限
  async revokeModulePermission(userId, permissionId) {
    try {
      const response = await api.delete(`/users/${userId}/module-permissions/${permissionId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // 撤销用户所有模组权限
  async revokeAllModulePermissions(userId) {
    try {
      const response = await api.delete(`/users/${userId}/module-permissions`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

export default authService;