import axios from 'axios';

// 连接到真实后端API
const MOCK_MODE = false;
const API_BASE_URL = 'http://localhost:3001/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 游戏资源操作可能需要更长时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 模拟游戏资源数据
let mockResources = [
  {
    id: 1,
    name: 'F/A-18C Hornet',
    category: 'aircraft-jet',
    description: '美国海军和海军陆战队的多用途战斗机，具备空对空和空对地作战能力。',
    installed: true,
    enabled: true,
    hasUpdate: false,
    updating: false,
    version: '2.8.1',
    size: '15.2 GB',
    author: 'Eagle Dynamics',
    uploadDate: '2023-01-15',
    downloadCount: 15420,
    rating: 4.8
  },
  {
    id: 2,
    name: 'A-10C II Tank Killer',
    category: 'aircraft-jet',
    description: '专门设计用于近距离空中支援的攻击机，擅长反装甲作战。',
    installed: true,
    enabled: false,
    hasUpdate: true,
    updating: false,
    version: '2.7.5',
    size: '12.8 GB',
    author: 'Eagle Dynamics',
    uploadDate: '2023-02-20',
    downloadCount: 12850,
    rating: 4.7
  },
  {
    id: 3,
    name: 'F-16C Viper',
    category: 'aircraft-jet',
    description: '世界上最成功的多用途战斗机之一，具备优秀的机动性和多任务能力。',
    installed: false,
    enabled: false,
    hasUpdate: false,
    updating: false,
    version: '2.8.0',
    size: '14.5 GB',
    author: 'Eagle Dynamics',
    uploadDate: '2023-03-10',
    downloadCount: 18920,
    rating: 4.9
  },
  {
    id: 4,
    name: 'Persian Gulf Map',
    category: 'terrain',
    description: '波斯湾地区的高精度地形图，包含阿联酋、伊朗南部等地区。',
    installed: true,
    enabled: true,
    hasUpdate: false,
    updating: false,
    version: '2.8.1',
    size: '8.9 GB',
    author: 'Eagle Dynamics',
    uploadDate: '2023-01-25',
    downloadCount: 9540,
    rating: 4.6
  },
  {
    id: 5,
    name: 'Syria Map',
    category: 'terrain',
    description: '叙利亚地区地形图，包含黎巴嫩、以色列、约旦和土耳其南部。',
    installed: false,
    enabled: false,
    hasUpdate: false,
    updating: false,
    version: '2.8.1',
    size: '7.2 GB',
    author: 'Eagle Dynamics',
    uploadDate: '2023-04-05',
    downloadCount: 7320,
    rating: 4.5
  }
];

// 模拟API响应
const mockAPI = {
  getResources: async (params = {}) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    let filtered = [...mockResources];
    
    if (params.category && params.category !== 'all') {
      filtered = filtered.filter(r => r.category === params.category);
    }
    
    if (params.search) {
      filtered = filtered.filter(r => 
        r.name.toLowerCase().includes(params.search.toLowerCase())
      );
    }
    
    return filtered;
  },
  
  toggleInstall: async (resourceId) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const resource = mockResources.find(r => r.id === resourceId);
    if (resource) {
      resource.installed = !resource.installed;
      if (!resource.installed) {
        resource.enabled = false;
      }
    }
    return resource;
  },
  
  toggleEnable: async (resourceId) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    const resource = mockResources.find(r => r.id === resourceId);
    if (resource && resource.installed) {
      resource.enabled = !resource.enabled;
    }
    return resource;
  },
  
  checkUpdates: async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockResources.filter(r => r.hasUpdate);
  },
  
  updateResource: async (resourceId) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const resource = mockResources.find(r => r.id === resourceId);
    if (resource) {
      resource.hasUpdate = false;
      resource.updating = false;
    }
    return resource;
  },
  
  repairResource: async (resourceId) => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const resource = mockResources.find(r => r.id === resourceId);
    return resource;
  },

  // 管理员API - 上传模组
  uploadModule: async (moduleData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newModule = {
      id: mockResources.length + 1,
      name: moduleData.name,
      category: moduleData.category,
      description: moduleData.description,
      installed: false,
      enabled: false,
      hasUpdate: false,
      updating: false,
      version: moduleData.version || '1.0.0',
      size: moduleData.size || '0 MB',
      author: moduleData.author,
      uploadDate: new Date().toISOString().split('T')[0],
      downloadCount: 0,
      rating: 0
    };
    mockResources.push(newModule);
    return newModule;
  },

  // 管理员API - 更新模组信息
  updateModule: async (moduleId, moduleData) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);
    if (moduleIndex === -1) {
      throw new Error('模组不存在');
    }
    mockResources[moduleIndex] = { ...mockResources[moduleIndex], ...moduleData };
    return mockResources[moduleIndex];
  },

  // 管理员API - 删除模组
  deleteModule: async (moduleId) => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const moduleIndex = mockResources.findIndex(r => r.id === moduleId);
    if (moduleIndex === -1) {
      throw new Error('模组不存在');
    }
    mockResources.splice(moduleIndex, 1);
    return { success: true };
  },

  // 管理员API - 获取所有模组（包括未发布的）
  getAllModules: async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockResources;
  },

  // 根据用户权限过滤模组
  getAuthorizedModules: async (userAuthorizedModules) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    if (!userAuthorizedModules || userAuthorizedModules.length === 0) {
      return [];
    }
    return mockResources.filter(r => userAuthorizedModules.includes(r.id));
  }
};

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('dcs_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('dcs_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const gameResourceService = {
  // 获取游戏资源列表
  async getResources(params = {}) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.getResources(params);
      } else {
        const response = await api.get('/game-resources', { params });
        return response.resources || [];
      }
    } catch (error) {
      throw error;
    }
  },

  // 获取资源分类
  async getCategories() {
    try {
      if (MOCK_MODE) {
        return [
          { id: 'aircraft-jet', name: '喷气发动机飞机' },
          { id: 'aircraft-prop', name: '活塞发动机飞机' },
          { id: 'terrain', name: '地形' },
          { id: 'campaign', name: '战役' }
        ];
      } else {
        const response = await api.get('/game-resources/categories');
        return response.categories || [];
      }
    } catch (error) {
      throw error;
    }
  },

  // 获取单个资源详情
  async getResource(resourceId) {
    try {
      if (MOCK_MODE) {
        return mockResources.find(r => r.id === resourceId);
      } else {
        const response = await api.get(`/game-resources/${resourceId}`);
        return response.resource;
      }
    } catch (error) {
      throw error;
    }
  },

  // 安装/卸载资源
  async toggleInstall(resourceId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.toggleInstall(resourceId);
      } else {
        const response = await api.post(`/game-resources/${resourceId}/toggle-install`);
        return response.resource;
      }
    } catch (error) {
      throw error;
    }
  },

  // 启用/禁用资源
  async toggleEnable(resourceId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.toggleEnable(resourceId);
      } else {
        const response = await api.post(`/game-resources/${resourceId}/toggle-enable`);
        return response.resource;
      }
    } catch (error) {
      throw error;
    }
  },

  // 检查更新
  async checkUpdates() {
    try {
      if (MOCK_MODE) {
        return await mockAPI.checkUpdates();
      } else {
        const response = await api.post('/game-resources/check-updates');
        return response.updates || [];
      }
    } catch (error) {
      throw error;
    }
  },

  // 更新资源
  async updateResource(resourceId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.updateResource(resourceId);
      } else {
        const response = await api.post(`/game-resources/${resourceId}/update`);
        return response.resource;
      }
    } catch (error) {
      throw error;
    }
  },

  // 修复资源
  async repairResource(resourceId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.repairResource(resourceId);
      } else {
        const response = await api.post(`/game-resources/${resourceId}/repair`);
        return response.resource;
      }
    } catch (error) {
      throw error;
    }
  },

  // 清理资源缓存
  async cleanCache() {
    try {
      const response = await api.post('/game-resources/clean-cache');
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 批量操作
  async batchOperation(operation, resourceIds) {
    try {
      const response = await api.post('/game-resources/batch', {
        operation,
        resourceIds
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 获取安装进度
  async getInstallProgress(resourceId) {
    try {
      const response = await api.get(`/game-resources/${resourceId}/progress`);
      return response.progress;
    } catch (error) {
      throw error;
    }
  },

  // 取消安装
  async cancelInstall(resourceId) {
    try {
      const response = await api.post(`/game-resources/${resourceId}/cancel`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 搜索资源
  async searchResources(query, filters = {}) {
    try {
      const response = await api.get('/game-resources/search', {
        params: { q: query, ...filters }
      });
      return response.resources || [];
    } catch (error) {
      throw error;
    }
  },

  // 获取推荐资源
  async getRecommended() {
    try {
      const response = await api.get('/game-resources/recommended');
      return response.resources || [];
    } catch (error) {
      throw error;
    }
  },

  // 获取最新资源
  async getLatest(limit = 10) {
    try {
      const response = await api.get('/game-resources/latest', {
        params: { limit }
      });
      return response.resources || [];
    } catch (error) {
      throw error;
    }
  },

  // 获取热门资源
  async getPopular(limit = 10) {
    try {
      const response = await api.get('/game-resources/popular', {
        params: { limit }
      });
      return response.resources || [];
    } catch (error) {
      throw error;
    }
  },

  // 评价资源
  async rateResource(resourceId, rating, comment = '') {
    try {
      const response = await api.post(`/game-resources/${resourceId}/rate`, {
        rating,
        comment
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 获取资源评价
  async getResourceRatings(resourceId) {
    try {
      const response = await api.get(`/game-resources/${resourceId}/ratings`);
      return response.ratings || [];
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 上传模组
  async uploadModule(moduleData, file) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.uploadModule(moduleData);
      } else {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('data', JSON.stringify(moduleData));
        const response = await api.post('/admin/modules/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        return response.module;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 更新模组
  async updateModule(moduleId, moduleData) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.updateModule(moduleId, moduleData);
      } else {
        const response = await api.put(`/admin/modules/${moduleId}`, moduleData);
        return response.module;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 删除模组
  async deleteModule(moduleId) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.deleteModule(moduleId);
      } else {
        const response = await api.delete(`/admin/modules/${moduleId}`);
        return response;
      }
    } catch (error) {
      throw error;
    }
  },

  // 管理员功能 - 获取所有模组
  async getAllModules() {
    try {
      if (MOCK_MODE) {
        return await mockAPI.getAllModules();
      } else {
        const response = await api.get('/admin/modules');
        return response.modules || [];
      }
    } catch (error) {
      throw error;
    }
  },

  // 根据用户权限获取授权模组
  async getAuthorizedModules(userAuthorizedModules) {
    try {
      if (MOCK_MODE) {
        return await mockAPI.getAuthorizedModules(userAuthorizedModules);
      } else {
        const response = await api.post('/game-resources/authorized', {
          authorizedModules: userAuthorizedModules
        });
        return response.resources || [];
      }
    } catch (error) {
      throw error;
    }
  }
};

export default gameResourceService;